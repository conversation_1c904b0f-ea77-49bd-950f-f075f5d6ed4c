<?php
/**
 * 系统首页
 * 管理系统 - management.djxs.xyz
 */

require_once __DIR__ . '/api/config/config.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 认证管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .hero-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            border: none;
            transition: transform 0.3s;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s;
        }
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="hero-card">
                    <div class="card-body p-5">
                        <!-- 头部 -->
                        <div class="text-center mb-5">
                            <i class="bi bi-shield-check display-1 text-primary"></i>
                            <h1 class="mt-3 mb-3"><?php echo SYSTEM_NAME; ?></h1>
                            <p class="lead text-muted">专业的用户认证管理系统</p>
                            <p class="text-muted"><?php echo DOMAIN; ?></p>
                        </div>

                        <!-- 功能特性 -->
                        <div class="row mb-5">
                            <div class="col-md-4 mb-4">
                                <div class="card feature-card h-100">
                                    <div class="card-body text-center">
                                        <i class="bi bi-person-plus-fill display-4 text-primary mb-3"></i>
                                        <h5>用户注册</h5>
                                        <p class="text-muted">支持用户注册申请，管理员审批制度</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <div class="card feature-card h-100">
                                    <div class="card-body text-center">
                                        <i class="bi bi-key-fill display-4 text-success mb-3"></i>
                                        <h5>安全认证</h5>
                                        <p class="text-muted">Token认证机制，保障系统安全</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <div class="card feature-card h-100">
                                    <div class="card-body text-center">
                                        <i class="bi bi-gear-fill display-4 text-warning mb-3"></i>
                                        <h5>管理后台</h5>
                                        <p class="text-muted">完善的用户管理和系统监控</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- API接口 -->
                        <div class="row mb-5">
                            <div class="col-lg-8 mx-auto">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="bi bi-api"></i>
                                            API接口
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6><i class="bi bi-person-plus text-primary"></i> 用户注册</h6>
                                                <code>POST /api/auth/register.php</code>
                                                <hr>
                                                <h6><i class="bi bi-box-arrow-in-right text-success"></i> 用户登录</h6>
                                                <code>POST /api/auth/login.php</code>
                                            </div>
                                            <div class="col-md-6">
                                                <h6><i class="bi bi-check-circle text-info"></i> Token验证</h6>
                                                <code>POST /api/auth/verify.php</code>
                                                <hr>
                                                <h6><i class="bi bi-book text-warning"></i> 完整文档</h6>
                                                <a href="api/docs.php" class="btn btn-sm btn-outline-primary">查看API文档</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="text-center">
                            <div class="row justify-content-center">
                                <div class="col-md-8">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <a href="admin/login.php" class="btn btn-gradient text-white w-100">
                                                <i class="bi bi-shield-lock"></i>
                                                管理后台
                                            </a>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <a href="api/docs.php" class="btn btn-outline-primary w-100">
                                                <i class="bi bi-book"></i>
                                                API文档
                                            </a>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <a href="install.php" class="btn btn-outline-secondary w-100">
                                                <i class="bi bi-gear"></i>
                                                系统安装
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 系统信息 -->
                        <div class="row mt-5">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6><i class="bi bi-info-circle text-primary"></i> 系统信息</h6>
                                        <p class="mb-1"><strong>版本：</strong><?php echo SYSTEM_VERSION; ?></p>
                                        <p class="mb-1"><strong>域名：</strong><?php echo DOMAIN; ?></p>
                                        <p class="mb-0"><strong>PHP：</strong><?php echo PHP_VERSION; ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6><i class="bi bi-database text-success"></i> 数据库配置</h6>
                                        <p class="mb-1"><strong>数据库：</strong><?php echo DB_NAME; ?></p>
                                        <p class="mb-1"><strong>主机：</strong><?php echo DB_HOST; ?></p>
                                        <p class="mb-0"><strong>字符集：</strong><?php echo DB_CHARSET; ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 页脚 -->
                        <div class="text-center mt-5">
                            <p class="text-muted mb-0">
                                © 2024 <?php echo SYSTEM_NAME; ?> - 专业认证管理系统
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
