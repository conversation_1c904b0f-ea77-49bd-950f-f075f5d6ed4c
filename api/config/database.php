<?php
/**
 * 数据库配置文件
 * 管理系统 - management.djxs.xyz
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'management';
    private $username = 'management';
    private $password = 'bt3YBkRR3rfeXY5j';
    private $charset = 'utf8mb4';
    public $conn;

    /**
     * 获取数据库连接
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];
            
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $exception) {
            error_log("数据库连接错误: " . $exception->getMessage());
            throw new Exception("数据库连接失败");
        }
        
        return $this->conn;
    }

    /**
     * 关闭数据库连接
     */
    public function closeConnection() {
        $this->conn = null;
    }

    /**
     * 测试数据库连接
     */
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            if ($conn) {
                return true;
            }
        } catch (Exception $e) {
            return false;
        }
        return false;
    }
}
?>
