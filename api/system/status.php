<?php
/**
 * 系统状态检查API
 * 智能权限管理系统 💖
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../../includes/auth.php';
require_once '../config/database.php';

try {
    // 只允许GET请求
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('只允许GET请求');
    }

    $database = new Database();
    $conn = $database->getConnection();
    
    // 检查数据库连接
    $databaseStatus = 'online';
    try {
        $stmt = $conn->query("SELECT 1");
        $stmt->fetch();
    } catch (Exception $e) {
        $databaseStatus = 'offline';
    }
    
    // 检查权限管理器
    $permissionManagerStatus = 'online';
    try {
        require_once '../../includes/module_permission_manager.php';
        $permissionManager = new ModulePermissionManager($conn);
        $permissionManager->getAvailableModules();
    } catch (Exception $e) {
        $permissionManagerStatus = 'offline';
    }
    
    // 检查认证系统
    $authenticationStatus = 'online';
    try {
        $auth = new Auth();
        // 简单的认证系统检查
    } catch (Exception $e) {
        $authenticationStatus = 'offline';
    }
    
    // 检查文件存储
    $fileStorageStatus = 'online';
    $uploadsDir = '../../uploads';
    if (!is_dir($uploadsDir) || !is_writable($uploadsDir)) {
        $fileStorageStatus = 'offline';
    }
    
    // 获取系统统计信息
    $stats = [];
    
    try {
        // 用户统计
        $stmt = $conn->query("SELECT 
            COUNT(*) as total_users,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_users,
            SUM(CASE WHEN status = 'banned' THEN 1 ELSE 0 END) as banned_users
            FROM users WHERE status != 'deleted'");
        $userStats = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['users'] = $userStats;
        
        // 权限统计
        $stmt = $conn->query("SELECT 
            COUNT(DISTINCT user_id) as users_with_android,
            SUM(download_count) as total_android_downloads
            FROM user_app_permissions 
            WHERE app_type = 'android' AND is_active = 1");
        $androidStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stmt = $conn->query("SELECT 
            COUNT(DISTINCT user_id) as users_with_windows
            FROM user_app_permissions 
            WHERE app_type = 'windows' AND is_active = 1");
        $windowsStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stmt = $conn->query("SELECT 
            COUNT(*) as total_module_permissions
            FROM user_module_permissions 
            WHERE is_active = 1");
        $moduleStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stats['permissions'] = [
            'android_users' => (int)$androidStats['users_with_android'],
            'total_android_downloads' => (int)$androidStats['total_android_downloads'],
            'windows_users' => (int)$windowsStats['users_with_windows'],
            'total_module_permissions' => (int)$moduleStats['total_module_permissions']
        ];
        
        // 今日活动统计
        $today = date('Y-m-d');
        $stmt = $conn->prepare("SELECT 
            COUNT(*) as today_logins
            FROM user_sessions 
            WHERE DATE(created_at) = ?");
        $stmt->execute([$today]);
        $todayStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stats['today'] = [
            'logins' => (int)$todayStats['today_logins']
        ];
        
    } catch (Exception $e) {
        $stats['error'] = '统计信息获取失败: ' . $e->getMessage();
    }
    
    // 计算系统整体状态
    $allServices = [$databaseStatus, $permissionManagerStatus, $authenticationStatus, $fileStorageStatus];
    $onlineServices = array_filter($allServices, function($status) {
        return $status === 'online';
    });
    
    $systemStatus = 'healthy';
    if (count($onlineServices) < count($allServices)) {
        if (count($onlineServices) === 0) {
            $systemStatus = 'critical';
        } else {
            $systemStatus = 'degraded';
        }
    }
    
    // 获取系统信息
    $systemInfo = [
        'version' => '2.0.0',
        'php_version' => PHP_VERSION,
        'server_time' => date('Y-m-d H:i:s'),
        'timezone' => date_default_timezone_get(),
        'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
        'memory_limit' => ini_get('memory_limit')
    ];
    
    // 构建响应
    $response = [
        'code' => 200,
        'success' => true,
        'message' => '系统状态检查完成',
        'data' => [
            'system_status' => $systemStatus,
            'services' => [
                'authentication' => $authenticationStatus,
                'permission_manager' => $permissionManagerStatus,
                'database' => $databaseStatus,
                'file_storage' => $fileStorageStatus
            ],
            'system_info' => $systemInfo,
            'statistics' => $stats,
            'health_check' => [
                'timestamp' => date('Y-m-d H:i:s'),
                'response_time' => round((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2) . 'ms'
            ]
        ]
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'code' => 500,
        'success' => false,
        'message' => '系统状态检查失败: ' . $e->getMessage(),
        'data' => [
            'system_status' => 'error',
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ], JSON_UNESCAPED_UNICODE);
}
?>
