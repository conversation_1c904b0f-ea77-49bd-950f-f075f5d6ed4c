<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能API文档 💖 - 管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .api-section {
            border-left: 4px solid var(--primary-gradient);
            padding-left: 20px;
            margin-bottom: 40px;
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .api-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .method-badge {
            font-size: 0.8em;
            padding: 0.4em 0.8em;
            border-radius: 20px;
            font-weight: bold;
            margin-right: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .method-post { background: var(--success-gradient); color: white; border: none; }
        .method-get { background: var(--primary-gradient); color: white; border: none; }
        .method-put { background: var(--warning-gradient); color: white; border: none; }
        .method-delete { background: var(--danger-gradient); color: white; border: none; }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            border: 1px solid #4a5568;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            font-family: 'Fira Code', 'Courier New', monospace;
            position: relative;
            overflow-x: auto;
        }

        .code-block::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-gradient);
            border-radius: 12px 12px 0 0;
        }

        .sidebar {
            background: var(--primary-gradient);
            min-height: 100vh;
            box-shadow: 2px 0 20px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 10px;
            margin: 3px 10px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .sidebar .nav-link:hover::before {
            left: 100%;
        }

        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.2);
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .card-header {
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 15px 20px;
            font-weight: 600;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table th {
            background: var(--primary-gradient);
            color: white;
            border: none;
            font-weight: 600;
        }

        .table td {
            border: none;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 12px;
        }

        .alert {
            border: none;
            border-radius: 12px;
            padding: 15px 20px;
        }

        .endpoint-url {
            background: rgba(0,0,0,0.05);
            padding: 8px 12px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #2d3748;
        }

        .feature-badge {
            background: var(--success-gradient);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 600;
            margin-left: 10px;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏导航 -->
            <nav class="col-md-3 col-lg-2 sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="bi bi-code-slash"></i>
                            智能API文档 💖
                        </h4>
                        <small class="text-white-50">management.djxs.xyz</small>
                        <div class="mt-2">
                            <span class="feature-badge">v2.0.0</span>
                        </div>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#overview">
                                <i class="bi bi-info-circle"></i> 概述
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#authentication">
                                <i class="bi bi-shield-lock"></i> 认证说明
                            </a>
                        </li>

                        <!-- 用户认证接口 -->
                        <li class="nav-item mt-3">
                            <h6 class="text-white-50 px-3 py-2 mb-0 small fw-bold">
                                🔐 用户认证接口
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#register">
                                <i class="bi bi-person-plus"></i> 用户注册
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#login">
                                <i class="bi bi-box-arrow-in-right"></i> 用户登录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#logout">
                                <i class="bi bi-box-arrow-right"></i> 用户登出
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#verify">
                                <i class="bi bi-check-circle"></i> Token验证
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#user-info">
                                <i class="bi bi-person-circle"></i> 用户信息
                            </a>
                        </li>

                        <!-- 权限管理接口 -->
                        <li class="nav-item mt-3">
                            <h6 class="text-white-50 px-3 py-2 mb-0 small fw-bold">
                                🛡️ 权限管理接口
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#module-permissions">
                                <i class="bi bi-gear"></i> 模块权限检查
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#batch-check">
                                <i class="bi bi-list-check"></i> 批量权限检查
                            </a>
                        </li>

                        <!-- 统计分析接口 -->
                        <li class="nav-item mt-3">
                            <h6 class="text-white-50 px-3 py-2 mb-0 small fw-bold">
                                📊 统计分析接口
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#user-stats">
                                <i class="bi bi-graph-up"></i> 用户统计
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#system-status">
                                <i class="bi bi-activity"></i> 系统状态
                            </a>
                        </li>

                        <!-- 其他 -->
                        <li class="nav-item mt-3">
                            <h6 class="text-white-50 px-3 py-2 mb-0 small fw-bold">
                                📚 其他信息
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#errors">
                                <i class="bi bi-exclamation-triangle"></i> 错误代码
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#examples">
                                <i class="bi bi-code"></i> 代码示例
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="pt-3 pb-2 mb-4 border-bottom">
                    <h1 class="h2 animate__animated animate__fadeInDown">
                        <i class="bi bi-rocket text-primary"></i>
                        智能API接口文档 💖
                        <span class="feature-badge">全新升级</span>
                    </h1>
                    <p class="text-muted">管理系统完整API文档 - 版本 2.0.0</p>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h5 class="text-primary mb-1">10+</h5>
                                <small class="text-muted">API接口</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h5 class="text-success mb-1">99.9%</h5>
                                <small class="text-muted">可用性</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h5 class="text-warning mb-1">< 100ms</h5>
                                <small class="text-muted">响应时间</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h5 class="text-info mb-1">HTTPS</h5>
                                <small class="text-muted">安全协议</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 概述 -->
                <section id="overview" class="api-section animate-fade-in">
                    <h3><i class="bi bi-info-circle text-primary"></i> 概述 <span class="feature-badge">重要</span></h3>
                    <p>本API提供完整的用户认证、权限管理、统计分析等功能。所有API请求都使用HTTPS协议，确保数据传输安全。</p>

                    <div class="row">
                        <div class="col-md-6">
                            <h5>🌟 核心特性</h5>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success"></i> JWT Token认证</li>
                                <li><i class="bi bi-check-circle text-success"></i> 模块化权限管理</li>
                                <li><i class="bi bi-check-circle text-success"></i> 实时统计分析</li>
                                <li><i class="bi bi-check-circle text-success"></i> 安全监控</li>
                                <li><i class="bi bi-check-circle text-success"></i> 批量操作支持</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>📋 基础信息</h5>
                            <ul class="list-unstyled">
                                <li><strong>基础URL：</strong> <span class="endpoint-url">https://management.djxs.xyz/api</span></li>
                                <li><strong>数据格式：</strong> JSON</li>
                                <li><strong>字符编码：</strong> UTF-8</li>
                                <li><strong>请求方法：</strong> GET, POST</li>
                                <li><strong>认证方式：</strong> Bearer Token</li>
                            </ul>
                        </div>
                    </div>

                    <h5>📤 标准响应格式</h5>
                    <div class="code-block">
                        <pre><code class="language-json">{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 响应数据
  },
  "timestamp": **********
}</code></pre>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-lightbulb"></i>
                        <strong>智能提示：</strong> 所有接口都支持跨域请求(CORS)，可以直接在前端JavaScript中调用。
                    </div>
                </section>

                <!-- 认证说明 -->
                <section id="authentication" class="api-section">
                    <h3><i class="bi bi-shield-lock text-warning"></i> 认证说明</h3>
                    <p>用户登录成功后会获得一个Token，后续需要身份验证的请求需要在Header中携带此Token。</p>
                    
                    <h5>Token使用方式</h5>
                    <div class="code-block">
                        <pre><code class="language-http">Authorization: Bearer {your_token_here}</code></pre>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        Token有效期为2小时，过期后需要重新登录获取新Token。
                    </div>
                </section>

                <!-- 用户注册 -->
                <section id="register" class="api-section">
                    <h3><i class="bi bi-person-plus text-success"></i> 用户注册</h3>
                    <p>提交用户注册申请，需要管理员审批后才能正式注册成功。</p>
                    
                    <div class="card">
                        <div class="card-header">
                            <span class="method-badge method-post">POST</span>
                            <code>/auth/register.php</code>
                        </div>
                        <div class="card-body">
                            <h6>请求参数</h6>
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>参数名</th>
                                        <th>类型</th>
                                        <th>必填</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>username</td>
                                        <td>string</td>
                                        <td>是</td>
                                        <td>用户名，3-20个字符</td>
                                    </tr>
                                    <tr>
                                        <td>password</td>
                                        <td>string</td>
                                        <td>是</td>
                                        <td>密码，最少6个字符</td>
                                    </tr>
                                    <tr>
                                        <td>email</td>
                                        <td>string</td>
                                        <td>是</td>
                                        <td>邮箱地址</td>
                                    </tr>
                                    <tr>
                                        <td>phone</td>
                                        <td>string</td>
                                        <td>否</td>
                                        <td>手机号码</td>
                                    </tr>
                                    <tr>
                                        <td>real_name</td>
                                        <td>string</td>
                                        <td>否</td>
                                        <td>真实姓名</td>
                                    </tr>
                                    <tr>
                                        <td>reason</td>
                                        <td>string</td>
                                        <td>否</td>
                                        <td>申请理由</td>
                                    </tr>
                                </tbody>
                            </table>

                            <h6>请求示例</h6>
                            <div class="code-block">
                                <pre><code class="language-json">{
  "username": "testuser",
  "password": "123456",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "real_name": "测试用户",
  "reason": "需要使用系统功能"
}</code></pre>
                            </div>

                            <h6>响应示例</h6>
                            <div class="code-block">
                                <pre><code class="language-json">{
  "code": 200,
  "message": "注册申请已提交，请等待管理员审批",
  "data": null,
  "timestamp": 1234567890
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 用户登录 -->
                <section id="login" class="api-section">
                    <h3><i class="bi bi-box-arrow-in-right text-primary"></i> 用户登录</h3>
                    <p>用户登录获取访问Token。</p>
                    
                    <div class="card">
                        <div class="card-header">
                            <span class="method-badge method-post">POST</span>
                            <code>/auth/login.php</code>
                        </div>
                        <div class="card-body">
                            <h6>请求参数</h6>
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>参数名</th>
                                        <th>类型</th>
                                        <th>必填</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>username</td>
                                        <td>string</td>
                                        <td>是</td>
                                        <td>用户名</td>
                                    </tr>
                                    <tr>
                                        <td>password</td>
                                        <td>string</td>
                                        <td>是</td>
                                        <td>密码</td>
                                    </tr>
                                </tbody>
                            </table>

                            <h6>请求示例</h6>
                            <div class="code-block">
                                <pre><code class="language-json">{
  "username": "testuser",
  "password": "123456"
}</code></pre>
                            </div>

                            <h6>响应示例</h6>
                            <div class="code-block">
                                <pre><code class="language-json">{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "abc123def456...",
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "real_name": "测试用户",
      "last_login": "2024-01-01 12:00:00"
    }
  },
  "timestamp": 1234567890
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Token验证 -->
                <section id="verify" class="api-section">
                    <h3><i class="bi bi-check-circle text-info"></i> Token验证</h3>
                    <p>验证用户Token的有效性。</p>
                    
                    <div class="card">
                        <div class="card-header">
                            <span class="method-badge method-post">POST</span>
                            <span class="method-badge method-get">GET</span>
                            <code>/auth/verify.php</code>
                        </div>
                        <div class="card-body">
                            <h6>请求方式</h6>
                            <p>可以通过以下任一方式传递Token：</p>
                            <ul>
                                <li>POST请求体中的 <code>token</code> 参数</li>
                                <li>GET请求的 <code>token</code> 参数</li>
                                <li>HTTP Header: <code>Authorization: Bearer {token}</code></li>
                            </ul>

                            <h6>请求示例</h6>
                            <div class="code-block">
                                <pre><code class="language-json">{
  "token": "abc123def456..."
}</code></pre>
                            </div>

                            <h6>响应示例</h6>
                            <div class="code-block">
                                <pre><code class="language-json">{
  "code": 200,
  "message": "Token验证成功",
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>"
    }
  },
  "timestamp": 1234567890
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 用户登出 -->
                <section id="logout" class="api-section animate-fade-in">
                    <h3><i class="bi bi-box-arrow-right text-warning"></i> 用户登出 <span class="feature-badge">新增</span></h3>
                    <p>安全登出，清除用户会话。</p>

                    <div class="card">
                        <div class="card-header">
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-url">/auth/logout.php</span>
                        </div>
                        <div class="card-body">
                            <h6>请求头</h6>
                            <div class="code-block">
                                <pre><code class="language-http">Authorization: Bearer {your_token_here}</code></pre>
                            </div>

                            <h6>响应示例</h6>
                            <div class="code-block">
                                <pre><code class="language-json">{
  "code": 200,
  "message": "登出成功",
  "data": null,
  "timestamp": **********
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 用户信息 -->
                <section id="user-info" class="api-section animate-fade-in">
                    <h3><i class="bi bi-person-circle text-info"></i> 获取用户信息 <span class="feature-badge">新增</span></h3>
                    <p>获取当前登录用户的详细信息。</p>

                    <div class="card">
                        <div class="card-header">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-url">/auth/user_info.php</span>
                        </div>
                        <div class="card-body">
                            <h6>请求头</h6>
                            <div class="code-block">
                                <pre><code class="language-http">Authorization: Bearer {your_token_here}</code></pre>
                            </div>

                            <h6>响应示例</h6>
                            <div class="code-block">
                                <pre><code class="language-json">{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "real_name": "测试用户",
      "phone": "13800138000",
      "status": "active",
      "created_at": "2024-01-01 12:00:00",
      "last_login": "2024-01-02 10:30:00"
    },
    "permissions": {
      "android": {
        "is_active": true,
        "download_count": 5,
        "expires_at": null
      },
      "windows": {
        "is_active": true,
        "expires_at": "2024-12-31 23:59:59"
      }
    },
    "modules": [
      "batch_rename",
      "format_names",
      "folder_counter"
    ]
  },
  "timestamp": **********
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 模块权限检查 -->
                <section id="module-permissions" class="api-section animate-fade-in">
                    <h3><i class="bi bi-gear text-success"></i> 模块权限检查 <span class="feature-badge">核心</span></h3>
                    <p>检查用户对特定模块的访问权限。</p>

                    <div class="card">
                        <div class="card-header">
                            <span class="method-badge method-get">GET</span>
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-url">/auth/module_permissions.php</span>
                            <span class="feature-badge">正确路径</span>
                        </div>
                        <div class="card-body">
                            <h6>请求参数</h6>
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>参数名</th>
                                        <th>类型</th>
                                        <th>必填</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>token</td>
                                        <td>string</td>
                                        <td>是</td>
                                        <td>用户Token</td>
                                    </tr>
                                    <tr>
                                        <td>module</td>
                                        <td>string</td>
                                        <td>是</td>
                                        <td>模块代码</td>
                                    </tr>
                                </tbody>
                            </table>

                            <h6>可用模块列表</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><code>batch_rename</code> - 批量重命名</li>
                                        <li><code>format_names</code> - 整理文件名</li>
                                        <li><code>folder_counter</code> - 文件夹计数重命名</li>
                                        <li><code>author_organizer</code> - 同名作者整理</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><code>anti_piracy_generator</code> - 防盗文件生成</li>
                                        <li><code>content_cleaner</code> - 文件内容净化</li>
                                        <li><code>file_duplicate_finder</code> - 文件查重</li>
                                        <li><code>directory_tree_generator</code> - 目录树生成</li>
                                    </ul>
                                </div>
                            </div>

                            <h6>请求示例</h6>
                            <div class="code-block">
                                <pre><code class="language-json">{
  "token": "abc123def456...",
  "module": "batch_rename"
}</code></pre>
                            </div>

                            <h6>响应示例</h6>
                            <div class="code-block">
                                <pre><code class="language-json">{
  "code": 200,
  "message": "权限检查成功",
  "data": {
    "has_permission": true,
    "module": "batch_rename",
    "module_name": "批量重命名",
    "expires_at": null
  },
  "timestamp": **********
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 批量权限检查 -->
                <section id="batch-check" class="api-section animate-fade-in">
                    <h3><i class="bi bi-list-check text-primary"></i> 批量权限检查 <span class="feature-badge">高效</span></h3>
                    <p>一次性检查多个权限，提高效率。</p>

                    <div class="card">
                        <div class="card-header">
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-url">/permissions/batch_check.php</span>
                        </div>
                        <div class="card-body">
                            <h6>请求参数</h6>
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>参数名</th>
                                        <th>类型</th>
                                        <th>必填</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>permissions</td>
                                        <td>array</td>
                                        <td>是</td>
                                        <td>权限列表</td>
                                    </tr>
                                </tbody>
                            </table>

                            <h6>请求示例</h6>
                            <div class="code-block">
                                <pre><code class="language-json">{
  "permissions": [
    {"type": "app", "name": "android"},
    {"type": "app", "name": "windows"},
    {"type": "module", "name": "batch_rename"},
    {"type": "module", "name": "format_names"}
  ]
}</code></pre>
                            </div>

                            <h6>响应示例</h6>
                            <div class="code-block">
                                <pre><code class="language-json">{
  "code": 200,
  "message": "批量权限检查完成",
  "data": {
    "results": [
      {"type": "app", "name": "android", "has_permission": true},
      {"type": "app", "name": "windows", "has_permission": true},
      {"type": "module", "name": "batch_rename", "has_permission": true},
      {"type": "module", "name": "format_names", "has_permission": false}
    ],
    "summary": {
      "total": 4,
      "granted": 3,
      "denied": 1
    }
  },
  "timestamp": **********
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 用户统计 -->
                <section id="user-stats" class="api-section animate-fade-in">
                    <h3><i class="bi bi-graph-up text-info"></i> 用户使用统计 <span class="feature-badge">分析</span></h3>
                    <p>获取用户的使用统计数据。</p>

                    <div class="card">
                        <div class="card-header">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-url">/stats/user.php</span>
                        </div>
                        <div class="card-body">
                            <h6>请求头</h6>
                            <div class="code-block">
                                <pre><code class="language-http">Authorization: Bearer {your_token_here}</code></pre>
                            </div>

                            <h6>响应示例</h6>
                            <div class="code-block">
                                <pre><code class="language-json">{
  "code": 200,
  "message": "获取统计数据成功",
  "data": {
    "user_id": 1,
    "username": "testuser",
    "stats": {
      "total_logins": 25,
      "last_login": "2024-01-02 10:30:00",
      "android_downloads": 5,
      "active_permissions": 8,
      "account_age_days": 30
    },
    "usage": {
      "most_used_module": "batch_rename",
      "total_operations": 150,
      "this_month_operations": 45
    }
  },
  "timestamp": **********
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 系统状态 -->
                <section id="system-status" class="api-section animate-fade-in">
                    <h3><i class="bi bi-activity text-success"></i> 系统状态检查 <span class="feature-badge">监控</span></h3>
                    <p>获取系统运行状态和健康信息。</p>

                    <div class="card">
                        <div class="card-header">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-url">/system/status.php</span>
                        </div>
                        <div class="card-body">
                            <h6>响应示例</h6>
                            <div class="code-block">
                                <pre><code class="language-json">{
  "code": 200,
  "message": "系统状态正常",
  "data": {
    "system": {
      "status": "healthy",
      "uptime": "15 days, 8 hours",
      "version": "2.0.0"
    },
    "services": {
      "database": "online",
      "authentication": "online",
      "permission_manager": "online",
      "file_storage": "online"
    },
    "stats": {
      "total_users": 150,
      "active_users": 45,
      "total_permissions": 1200,
      "today": {
        "logins": 25
      }
    }
  },
  "timestamp": **********
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 错误代码 -->
                <section id="errors" class="api-section animate-fade-in">
                    <h3><i class="bi bi-exclamation-triangle text-danger"></i> 错误代码 <span class="feature-badge">重要</span></h3>
                    <p>API可能返回的错误代码说明。</p>
                    
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>状态码</th>
                                <th>说明</th>
                                <th>示例</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>200</td>
                                <td>请求成功</td>
                                <td>操作成功完成</td>
                            </tr>
                            <tr>
                                <td>400</td>
                                <td>请求参数错误</td>
                                <td>缺少必填字段、参数格式错误</td>
                            </tr>
                            <tr>
                                <td>401</td>
                                <td>未授权</td>
                                <td>Token无效、用户名密码错误</td>
                            </tr>
                            <tr>
                                <td>403</td>
                                <td>禁止访问</td>
                                <td>用户被封禁、权限不足</td>
                            </tr>
                            <tr>
                                <td>404</td>
                                <td>资源不存在</td>
                                <td>接口不存在</td>
                            </tr>
                            <tr>
                                <td>405</td>
                                <td>请求方法不允许</td>
                                <td>使用了不支持的HTTP方法</td>
                            </tr>
                            <tr>
                                <td>500</td>
                                <td>服务器内部错误</td>
                                <td>数据库连接失败、系统异常</td>
                            </tr>
                        </tbody>
                    </table>
                </section>

                <!-- 代码示例 -->
                <section id="examples" class="api-section animate-fade-in">
                    <h3><i class="bi bi-code text-primary"></i> 代码示例 <span class="feature-badge">实用</span></h3>
                    <p>各种编程语言的调用示例。</p>

                    <div class="row">
                        <div class="col-md-6">
                            <h5>🐍 Python示例</h5>
                            <div class="code-block">
                                <pre><code class="language-python">import requests

# 用户登录
def login(username, password):
    url = "https://management.djxs.xyz/api/auth/login.php"
    data = {
        "username": username,
        "password": password
    }
    response = requests.post(url, json=data)
    return response.json()

# 检查权限
def check_permission(token, module):
    url = "https://management.djxs.xyz/auth/module_permissions.php"
    headers = {"Authorization": f"Bearer {token}"}
    data = {"module": module}
    response = requests.post(url, json=data, headers=headers)
    return response.json()

# 使用示例
result = login("testuser", "123456")
if result["code"] == 200:
    token = result["data"]["token"]
    perm = check_permission(token, "batch_rename")
    print(f"权限检查结果: {perm}")
</code></pre>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>🌐 JavaScript示例</h5>
                            <div class="code-block">
                                <pre><code class="language-javascript">// 用户登录
async function login(username, password) {
    const response = await fetch('https://management.djxs.xyz/api/auth/login.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            username: username,
            password: password
        })
    });
    return await response.json();
}

// 检查权限
async function checkPermission(token, module) {
    const response = await fetch('https://management.djxs.xyz/auth/module_permissions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            module: module
        })
    });
    return await response.json();
}

// 使用示例
login('testuser', '123456').then(result => {
    if (result.code === 200) {
        const token = result.data.token;
        return checkPermission(token, 'batch_rename');
    }
}).then(perm => {
    console.log('权限检查结果:', perm);
});
</code></pre>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-success mt-4">
                        <i class="bi bi-lightbulb"></i>
                        <strong>开发提示：</strong>
                        <ul class="mb-0 mt-2">
                            <li>建议使用HTTPS协议确保数据传输安全</li>
                            <li>Token有效期为2小时，请及时刷新</li>
                            <li>所有接口都支持CORS跨域请求</li>
                            <li>建议实现错误重试机制</li>
                        </ul>
                    </div>
                </section>

                <div class="text-center mt-5 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-heart text-danger"></i>
                                感谢使用智能管理系统API 💖
                            </h5>
                            <p class="card-text text-muted">
                                如有问题或建议，请联系技术支持团队
                            </p>
                            <div class="row text-center mt-4">
                                <div class="col-md-3">
                                    <h6 class="text-primary">10+</h6>
                                    <small class="text-muted">API接口</small>
                                </div>
                                <div class="col-md-3">
                                    <h6 class="text-success">99.9%</h6>
                                    <small class="text-muted">可用性</small>
                                </div>
                                <div class="col-md-3">
                                    <h6 class="text-warning">24/7</h6>
                                    <small class="text-muted">技术支持</small>
                                </div>
                                <div class="col-md-3">
                                    <h6 class="text-info">v2.0.0</h6>
                                    <small class="text-muted">当前版本</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p class="text-muted mt-3">
                        © 2025 智能管理系统 API文档 - 版本 2.0.0 💖
                    </p>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script>
        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // 更新导航状态
                    document.querySelectorAll('.nav-link').forEach(link => {
                        link.classList.remove('active');
                    });
                    this.classList.add('active');
                }
            });
        });

        // 代码复制功能
        document.querySelectorAll('.code-block').forEach(block => {
            const copyBtn = document.createElement('button');
            copyBtn.className = 'btn btn-sm btn-outline-light position-absolute';
            copyBtn.style.top = '10px';
            copyBtn.style.right = '10px';
            copyBtn.innerHTML = '<i class="bi bi-clipboard"></i>';
            copyBtn.title = '复制代码';

            block.style.position = 'relative';
            block.appendChild(copyBtn);

            copyBtn.addEventListener('click', () => {
                const code = block.querySelector('code').textContent;
                navigator.clipboard.writeText(code).then(() => {
                    copyBtn.innerHTML = '<i class="bi bi-check"></i>';
                    copyBtn.className = 'btn btn-sm btn-success position-absolute';
                    setTimeout(() => {
                        copyBtn.innerHTML = '<i class="bi bi-clipboard"></i>';
                        copyBtn.className = 'btn btn-sm btn-outline-light position-absolute';
                    }, 2000);
                });
            });
        });

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.api-section');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate__animated', 'animate__fadeInUp');
                    }
                });
            }, { threshold: 0.1 });

            sections.forEach(section => {
                observer.observe(section);
            });
        });

        // API测试功能
        function testAPI(endpoint, method = 'GET') {
            const baseUrl = 'https://management.djxs.xyz/api';
            const fullUrl = baseUrl + endpoint;

            console.log(`测试API: ${method} ${fullUrl}`);

            // 这里可以添加实际的API测试逻辑
            alert(`API测试功能开发中...\n端点: ${fullUrl}\n方法: ${method}`);
        }

        // 搜索功能
        function searchAPI() {
            const searchTerm = prompt('搜索API接口:');
            if (searchTerm) {
                const sections = document.querySelectorAll('.api-section');
                let found = false;

                sections.forEach(section => {
                    const text = section.textContent.toLowerCase();
                    if (text.includes(searchTerm.toLowerCase())) {
                        section.scrollIntoView({ behavior: 'smooth' });
                        section.style.backgroundColor = '#fff3cd';
                        setTimeout(() => {
                            section.style.backgroundColor = '';
                        }, 3000);
                        found = true;
                        return;
                    }
                });

                if (!found) {
                    alert('未找到相关API接口');
                }
            }
        }

        // 添加搜索按钮
        const searchBtn = document.createElement('button');
        searchBtn.className = 'btn btn-outline-primary btn-sm position-fixed';
        searchBtn.style.bottom = '20px';
        searchBtn.style.right = '20px';
        searchBtn.style.zIndex = '1000';
        searchBtn.innerHTML = '<i class="bi bi-search"></i>';
        searchBtn.title = '搜索API';
        searchBtn.onclick = searchAPI;
        document.body.appendChild(searchBtn);
    </script>
</body>
</html>
