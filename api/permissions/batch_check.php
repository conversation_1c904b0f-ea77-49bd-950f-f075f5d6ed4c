<?php
/**
 * 批量权限检查API
 * 智能权限管理系统 💖
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../../includes/auth.php';
require_once '../../includes/jwt_helper.php';
require_once '../../includes/module_permission_manager.php';
require_once '../config/database.php';

try {
    // 只允许POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只允许POST请求');
    }

    $auth = new Auth();
    $jwtHelper = new JWTHelper();
    $database = new Database();
    $conn = $database->getConnection();
    $permissionManager = new ModulePermissionManager($conn);
    
    // 获取Authorization头
    $authHeader = '';

    // 更健壮的头部获取方法
    if (function_exists('getallheaders')) {
        $headers = getallheaders();
        // 尝试多种可能的键名
        $authKeys = ['Authorization', 'authorization', 'AUTHORIZATION'];
        foreach ($authKeys as $key) {
            if (isset($headers[$key])) {
                $authHeader = $headers[$key];
                break;
            }
        }
    }

    // 如果getallheaders()失败，直接从$_SERVER获取
    if (!$authHeader) {
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    }

    $token = null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
    }
    
    if (!$token) {
        throw new Exception('缺少访问令牌');
    }
    
    // 验证token
    $payload = $jwtHelper->validateToken($token);
    
    if (!$payload) {
        throw new Exception('访问令牌无效或已过期');
    }
    
    $userId = $payload['user_id'];
    
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['checks']) || !is_array($input['checks'])) {
        throw new Exception('请求数据格式错误，需要checks数组');
    }
    
    $checks = $input['checks'];
    
    if (empty($checks)) {
        throw new Exception('检查列表不能为空');
    }
    
    if (count($checks) > 50) {
        throw new Exception('单次最多检查50个权限');
    }
    
    // 获取用户权限信息
    $userPermissions = $permissionManager->getUserPermissions($userId);
    
    $results = [];
    $passedCount = 0;
    $failedCount = 0;
    
    foreach ($checks as $check) {
        if (!is_array($check) || !isset($check['type'])) {
            continue;
        }
        
        $checkType = $check['type'];
        $checkValue = $check['value'] ?? '';
        $checkKey = $checkType . ':' . $checkValue;
        
        $hasPermission = false;
        
        try {
            switch ($checkType) {
                case 'app':
                    if ($checkValue === 'android') {
                        $hasPermission = in_array('android', $userPermissions['apps']);
                    } elseif ($checkValue === 'windows') {
                        $hasPermission = in_array('windows', $userPermissions['apps']);
                    }
                    break;
                    
                case 'module':
                    $hasPermission = in_array($checkValue, $userPermissions['modules']);
                    break;
                    
                case 'android_download':
                    $hasPermission = in_array('android', $userPermissions['apps']) && 
                                   $userPermissions['android_downloads'] > 0;
                    break;
                    
                default:
                    $hasPermission = false;
            }
        } catch (Exception $e) {
            $hasPermission = false;
        }
        
        $results[$checkKey] = $hasPermission;
        
        if ($hasPermission) {
            $passedCount++;
        } else {
            $failedCount++;
        }
    }
    
    // 记录批量检查日志
    $auth->logUserAction($userId, 'batch_permission_check', '批量权限检查', [
        'total_checks' => count($checks),
        'passed_checks' => $passedCount,
        'failed_checks' => $failedCount,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ]);
    
    echo json_encode([
        'code' => 200,
        'success' => true,
        'message' => '批量权限检查完成',
        'data' => [
            'results' => $results,
            'summary' => [
                'total_checks' => count($checks),
                'passed_checks' => $passedCount,
                'failed_checks' => $failedCount,
                'success_rate' => count($checks) > 0 ? round($passedCount / count($checks) * 100, 2) : 0
            ],
            'user_id' => $userId,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'code' => 400,
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?>
