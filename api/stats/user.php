<?php
/**
 * 用户使用统计API
 * 智能权限管理系统 💖
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../../includes/auth.php';
require_once '../../includes/jwt_helper.php';
require_once '../../includes/module_permission_manager.php';
require_once '../config/database.php';

try {
    // 只允许GET请求
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('只允许GET请求');
    }

    $auth = new Auth();
    $jwtHelper = new JWTHelper();
    $database = new Database();
    $conn = $database->getConnection();
    $permissionManager = new ModulePermissionManager($conn);
    
    // 获取Authorization头
    $authHeader = '';

    // 更健壮的头部获取方法
    if (function_exists('getallheaders')) {
        $headers = getallheaders();
        // 尝试多种可能的键名
        $authKeys = ['Authorization', 'authorization', 'AUTHORIZATION'];
        foreach ($authKeys as $key) {
            if (isset($headers[$key])) {
                $authHeader = $headers[$key];
                break;
            }
        }
    }

    // 如果getallheaders()失败，直接从$_SERVER获取
    if (!$authHeader) {
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    }

    $token = null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
    }
    
    if (!$token) {
        throw new Exception('缺少访问令牌');
    }
    
    // 验证token
    $payload = $jwtHelper->validateToken($token);
    
    if (!$payload) {
        throw new Exception('访问令牌无效或已过期');
    }
    
    $userId = $payload['user_id'];
    
    // 获取用户基本信息
    $sql = "SELECT username, email, status, created_at, last_login, login_count 
            FROM users WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$userId]);
    $userInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userInfo) {
        throw new Exception('用户不存在');
    }
    
    // 获取权限统计
    $userPermissions = $permissionManager->getUserPermissions($userId);
    
    // 获取Android使用统计
    $sql = "SELECT download_count, expires_at, granted_at 
            FROM user_app_permissions 
            WHERE user_id = ? AND app_type = 'android' AND is_active = 1";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$userId]);
    $androidPermission = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $androidStats = [
        'has_permission' => (bool)$androidPermission,
        'total_downloads_granted' => $androidPermission ? (int)$androidPermission['download_count'] : 0,
        'downloads_remaining' => $userPermissions['android_downloads'],
        'downloads_used' => $androidPermission ? 
            ((int)$androidPermission['download_count'] - $userPermissions['android_downloads']) : 0,
        'granted_at' => $androidPermission['granted_at'] ?? null,
        'expires_at' => $androidPermission['expires_at'] ?? null
    ];
    
    // 获取Windows使用统计
    $sql = "SELECT expires_at, granted_at 
            FROM user_app_permissions 
            WHERE user_id = ? AND app_type = 'windows' AND is_active = 1";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$userId]);
    $windowsPermission = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $windowsStats = [
        'has_permission' => (bool)$windowsPermission,
        'modules_available' => count($userPermissions['modules']),
        'granted_at' => $windowsPermission['granted_at'] ?? null,
        'expires_at' => $windowsPermission['expires_at'] ?? null
    ];
    
    // 获取模块使用统计
    $availableModules = $permissionManager->getAvailableModules();
    $userModules = $userPermissions['modules'];
    
    $moduleStats = [
        'total_available' => count($availableModules),
        'user_enabled' => count($userModules),
        'enabled_modules' => $userModules,
        'disabled_modules' => array_diff(array_keys($availableModules), $userModules)
    ];
    
    // 获取登录统计
    $sql = "SELECT COUNT(*) as session_count, MAX(created_at) as last_session 
            FROM user_sessions WHERE user_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$userId]);
    $sessionStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 获取最近30天的登录统计
    $sql = "SELECT DATE(created_at) as login_date, COUNT(*) as login_count 
            FROM user_sessions 
            WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(created_at) 
            ORDER BY login_date DESC 
            LIMIT 30";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$userId]);
    $recentLogins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取操作日志统计
    $sql = "SELECT COUNT(*) as total_actions, MAX(created_at) as last_action 
            FROM user_logs WHERE user_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$userId]);
    $actionStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 计算账户年龄
    $accountAge = time() - strtotime($userInfo['created_at']);
    $accountAgeDays = floor($accountAge / 86400);
    
    // 构建统计数据
    $stats = [
        'user_info' => [
            'user_id' => $userId,
            'username' => $userInfo['username'],
            'email' => $userInfo['email'],
            'status' => $userInfo['status'],
            'account_created' => $userInfo['created_at'],
            'account_age_days' => $accountAgeDays,
            'last_login' => $userInfo['last_login'],
            'total_logins' => (int)$userInfo['login_count']
        ],
        'permissions' => [
            'android' => $androidStats,
            'windows' => $windowsStats,
            'modules' => $moduleStats
        ],
        'usage_stats' => [
            'total_sessions' => (int)$sessionStats['session_count'],
            'last_session' => $sessionStats['last_session'],
            'total_actions' => (int)$actionStats['total_actions'],
            'last_action' => $actionStats['last_action'],
            'recent_logins' => $recentLogins
        ],
        'summary' => [
            'permissions_expires_at' => [
                'android' => $androidStats['expires_at'],
                'windows' => $windowsStats['expires_at']
            ],
            'activity_score' => min(100, ($sessionStats['session_count'] * 2) + ($actionStats['total_actions'] * 0.5)),
            'permission_coverage' => round((count($userModules) / max(1, count($availableModules))) * 100, 1)
        ]
    ];
    
    // 记录统计查询日志
    $auth->logUserAction($userId, 'get_user_stats', '获取用户统计信息', [
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ]);
    
    echo json_encode([
        'code' => 200,
        'success' => true,
        'message' => '用户统计信息获取成功',
        'data' => $stats
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'code' => 400,
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?>
