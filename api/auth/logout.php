<?php
/**
 * 用户登出API
 * 智能权限管理系统 💖
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../../includes/auth.php';
require_once '../../includes/jwt_helper.php';

try {
    // 只允许POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只允许POST请求');
    }

    $auth = new Auth();
    $jwtHelper = new JWTHelper();
    
    // 获取Authorization头
    $authHeader = '';

    // 更健壮的头部获取方法
    if (function_exists('getallheaders')) {
        $headers = getallheaders();
        // 尝试多种可能的键名
        $authKeys = ['Authorization', 'authorization', 'AUTHORIZATION'];
        foreach ($authKeys as $key) {
            if (isset($headers[$key])) {
                $authHeader = $headers[$key];
                break;
            }
        }
    }

    // 如果getallheaders()失败，直接从$_SERVER获取
    if (!$authHeader) {
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    }

    $token = null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
    }
    
    if (!$token) {
        // 如果没有token，也算登出成功
        echo json_encode([
            'code' => 200,
            'success' => true,
            'message' => '登出成功',
            'data' => [
                'logged_out' => true,
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证token
    $payload = $jwtHelper->validateToken($token);
    
    if ($payload) {
        $userId = $payload['user_id'];
        
        // 记录登出日志
        $auth->logUserAction($userId, 'logout', '用户登出', [
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        // 可以在这里添加token黑名单逻辑
        // 目前JWT是无状态的，客户端删除token即可
        
        echo json_encode([
            'code' => 200,
            'success' => true,
            'message' => '登出成功',
            'data' => [
                'user_id' => $userId,
                'logged_out' => true,
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ], JSON_UNESCAPED_UNICODE);
    } else {
        // Token无效，但也算登出成功
        echo json_encode([
            'code' => 200,
            'success' => true,
            'message' => '登出成功（Token已失效）',
            'data' => [
                'logged_out' => true,
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'code' => 400,
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?>
