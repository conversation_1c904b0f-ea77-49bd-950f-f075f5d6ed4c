<?php
/**
 * 获取用户信息API
 * 智能权限管理系统 💖
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../../includes/auth.php';
require_once '../../includes/jwt_helper.php';
require_once '../config/database.php';

try {
    // 只允许GET请求
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('只允许GET请求');
    }

    $auth = new Auth();
    $jwtHelper = new JWTHelper();
    $database = new Database();
    $conn = $database->getConnection();
    
    // 获取Authorization头
    $authHeader = '';

    // 更健壮的头部获取方法
    if (function_exists('getallheaders')) {
        $headers = getallheaders();
        // 尝试多种可能的键名
        $authKeys = ['Authorization', 'authorization', 'AUTHORIZATION'];
        foreach ($authKeys as $key) {
            if (isset($headers[$key])) {
                $authHeader = $headers[$key];
                break;
            }
        }
    }

    // 如果getallheaders()失败，直接从$_SERVER获取
    if (!$authHeader) {
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    }

    $token = null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
    }
    
    if (!$token) {
        throw new Exception('缺少访问令牌');
    }
    
    // 验证token
    $payload = $jwtHelper->validateToken($token);
    
    if (!$payload) {
        throw new Exception('访问令牌无效或已过期');
    }
    
    $userId = $payload['user_id'];
    
    // 获取用户详细信息
    $sql = "SELECT id, username, email, phone, real_name, status, created_at, last_login, login_count, last_ip 
            FROM users WHERE id = ? AND status != 'deleted'";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception('用户不存在或已被删除');
    }
    
    // 检查用户状态
    if ($user['status'] === 'banned') {
        throw new Exception('用户账号已被封禁');
    }
    
    // 获取用户权限统计
    $permissionStats = [];
    
    // Android权限统计
    $sql = "SELECT download_count, expires_at FROM user_app_permissions 
            WHERE user_id = ? AND app_type = 'android' AND is_active = 1";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$userId]);
    $androidPermission = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $permissionStats['android'] = [
        'has_permission' => (bool)$androidPermission,
        'download_count' => $androidPermission ? (int)$androidPermission['download_count'] : 0,
        'expires_at' => $androidPermission['expires_at'] ?? null
    ];
    
    // Windows权限统计
    $sql = "SELECT expires_at FROM user_app_permissions 
            WHERE user_id = ? AND app_type = 'windows' AND is_active = 1";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$userId]);
    $windowsPermission = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $permissionStats['windows'] = [
        'has_permission' => (bool)$windowsPermission,
        'expires_at' => $windowsPermission['expires_at'] ?? null
    ];
    
    // 模块权限统计
    $sql = "SELECT COUNT(*) as module_count FROM user_module_permissions 
            WHERE user_id = ? AND is_active = 1";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$userId]);
    $moduleCount = $stmt->fetchColumn();
    
    $permissionStats['modules'] = [
        'total_count' => (int)$moduleCount
    ];
    
    // 构建响应数据
    $userData = [
        'id' => (int)$user['id'],
        'username' => $user['username'],
        'email' => $user['email'],
        'phone' => $user['phone'],
        'real_name' => $user['real_name'],
        'status' => $user['status'],
        'created_at' => $user['created_at'],
        'last_login' => $user['last_login'],
        'login_count' => (int)$user['login_count'],
        'last_ip' => $user['last_ip'],
        'permissions' => $permissionStats
    ];
    
    // 记录访问日志
    $auth->logUserAction($userId, 'get_user_info', '获取用户信息', [
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    echo json_encode([
        'code' => 200,
        'success' => true,
        'message' => '用户信息获取成功',
        'data' => [
            'user' => $userData,
            'token_info' => [
                'expires_at' => date('Y-m-d H:i:s', $payload['exp']),
                'issued_at' => date('Y-m-d H:i:s', $payload['iat'])
            ]
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'code' => 400,
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?>
