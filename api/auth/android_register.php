<?php
/**
 * Android专用注册API 💖
 * 确保只有Android用户可以注册并获得相应权限
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Android-Package, X-App-Version');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许POST请求',
        'code' => 405
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    // 引入必要文件
    require_once __DIR__ . '/../config/config.php';
    require_once __DIR__ . '/../../includes/auth.php';
    require_once __DIR__ . '/../../includes/module_permission_manager.php';

    // 获取请求数据
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);
    
    // 如果JSON解析失败，返回错误
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('请求数据格式错误: ' . json_last_error_msg());
    }
    
    // 验证必填字段
    $requiredFields = ['username', 'password', 'email', 'verification_code'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            throw new Exception("缺少必填字段: {$field}");
        }
    }
    
    // 获取Android特有的请求头
    $androidPackage = $_SERVER['HTTP_X_ANDROID_PACKAGE'] ?? '';
    $appVersion = $_SERVER['HTTP_X_APP_VERSION'] ?? '';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // 验证是否来自Android应用
    $isAndroidRequest = false;
    if (!empty($androidPackage) || strpos(strtolower($userAgent), 'android') !== false) {
        $isAndroidRequest = true;
    }
    
    // 记录请求信息用于调试
    logMessage('INFO', 'Android注册请求', [
        'username' => $input['username'],
        'email' => $input['email'],
        'android_package' => $androidPackage,
        'app_version' => $appVersion,
        'user_agent' => $userAgent,
        'is_android' => $isAndroidRequest,
        'ip' => getClientIP()
    ]);
    
    // 获取参数
    $username = trim($input['username']);
    $password = $input['password'];
    $email = trim($input['email']);
    $verificationCode = trim($input['verification_code']);
    $phone = trim($input['phone'] ?? '');
    $realName = trim($input['real_name'] ?? '');
    $reason = trim($input['reason'] ?? '申请Android应用访问权限');
    
    // 如果没有填写申请理由，自动添加Android相关信息
    if (empty($reason)) {
        $reason = 'Android应用用户注册';
        if (!empty($androidPackage)) {
            $reason .= " (包名: {$androidPackage})";
        }
        if (!empty($appVersion)) {
            $reason .= " (版本: {$appVersion})";
        }
    }
    
    // 创建认证实例
    $auth = new Auth();
    
    // 执行注册
    $result = $auth->register($username, $password, $email, $phone, $realName, $reason, $verificationCode);
    
    if ($result['success']) {
        // 注册成功后，记录Android相关信息
        $database = new Database();
        $conn = $database->getConnection();
        
        // 查找刚创建的pending用户
        $sql = "SELECT id FROM pending_users WHERE username = ? AND email = ? ORDER BY created_at DESC LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$username, $email]);
        $pendingUser = $stmt->fetch();
        
        if ($pendingUser) {
            // 在pending_users表中添加Android相关信息
            $updateSql = "UPDATE pending_users SET 
                         reason = ?, 
                         user_agent = ?
                         WHERE id = ?";
            $updateStmt = $conn->prepare($updateSql);
            $updateStmt->execute([
                $reason,
                $userAgent,
                $pendingUser['id']
            ]);
            
            // 记录Android设备信息（如果有的话）
            if (!empty($androidPackage) || !empty($appVersion)) {
                $deviceInfoSql = "INSERT INTO android_device_info (pending_user_id, package_name, app_version, device_info, created_at) 
                                 VALUES (?, ?, ?, ?, NOW())";
                $deviceStmt = $conn->prepare($deviceInfoSql);
                $deviceStmt->execute([
                    $pendingUser['id'],
                    $androidPackage,
                    $appVersion,
                    $userAgent
                ]);
            }
        }
        
        // 返回成功响应，包含Android特有信息
        $response = [
            'success' => true,
            'message' => $result['message'],
            'data' => [
                'username' => $username,
                'email' => $email,
                'android_detected' => $isAndroidRequest,
                'next_steps' => [
                    '1. 等待管理员审批您的注册申请',
                    '2. 审批通过后，您将获得Android应用访问权限',
                    '3. 请保存好您的用户名和密码用于登录'
                ]
            ],
            'code' => 200,
            'timestamp' => time()
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        
    } else {
        throw new Exception($result['message'] ?? '注册失败');
    }
    
} catch (Exception $e) {
    // 记录错误
    logMessage('ERROR', 'Android注册API错误', [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'ip' => getClientIP(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);
    
    // 返回错误响应
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'code' => 400,
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
}

// 创建Android设备信息表（如果不存在）
function createAndroidDeviceTable() {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $sql = "CREATE TABLE IF NOT EXISTS android_device_info (
            id INT AUTO_INCREMENT PRIMARY KEY,
            pending_user_id INT,
            user_id INT DEFAULT NULL,
            package_name VARCHAR(255) DEFAULT NULL,
            app_version VARCHAR(100) DEFAULT NULL,
            device_info TEXT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_pending_user (pending_user_id),
            INDEX idx_user (user_id),
            INDEX idx_package (package_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Android设备信息表'";
        
        $conn->exec($sql);
        
    } catch (Exception $e) {
        // 忽略表创建错误，可能已经存在
        logMessage('WARNING', 'Android设备信息表创建失败', ['error' => $e->getMessage()]);
    }
}

// 在脚本开始时创建表
createAndroidDeviceTable();
?>
