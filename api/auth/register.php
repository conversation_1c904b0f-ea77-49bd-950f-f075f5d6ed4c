<?php
/**
 * 用户注册API
 * 管理系统 - management.djxs.xyz
 * 
 * 接口说明：
 * POST /api/auth/register.php
 * 
 * 请求参数：
 * - username: 用户名 (必填, 3-20字符)
 * - password: 密码 (必填, 最少6字符)
 * - email: 邮箱 (必填)
 * - verification_code: 邮箱验证码 (必填)
 * - phone: 手机号 (可选)
 * - real_name: 真实姓名 (可选)
 * - reason: 申请理由 (可选)
 * 
 * 返回格式：
 * {
 *   "code": 200,
 *   "message": "注册申请已提交，请等待管理员审批",
 *   "data": null,
 *   "timestamp": 1234567890
 * }
 */

header('Content-Type: application/json; charset=utf-8');

// 引入必要文件
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../../includes/auth.php';

// 设置CORS
setCORSHeaders();

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('只允许POST请求', 405);
}

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 如果JSON解析失败，尝试从POST获取
    if (json_last_error() !== JSON_ERROR_NONE) {
        $input = $_POST;
    }
    
    // 验证必填字段
    $requiredFields = ['username', 'password', 'email', 'verification_code'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            errorResponse("缺少必填字段: {$field}", API_ERROR_CODE);
        }
    }
    
    // 获取参数
    $username = trim($input['username']);
    $password = $input['password'];
    $email = trim($input['email']);
    $verificationCode = trim($input['verification_code']);
    $phone = trim($input['phone'] ?? '');
    $realName = trim($input['real_name'] ?? '');
    $reason = trim($input['reason'] ?? '');
    
    // 创建认证实例
    $auth = new Auth();
    
    // 执行注册
    $result = $auth->register($username, $password, $email, $phone, $realName, $reason, $verificationCode);
    
    if ($result['success']) {
        successResponse(null, $result['message']);
    } else {
        errorResponse($result['message'] ?? '注册失败');
    }
    
} catch (Exception $e) {
    logMessage('ERROR', 'API注册错误', [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'ip' => getClientIP()
    ]);
    
    errorResponse($e->getMessage(), API_SERVER_ERROR_CODE);
}
?>
