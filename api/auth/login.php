<?php
/**
 * 用户登录API
 * 管理系统 - management.djxs.xyz
 * 
 * 接口说明：
 * POST /api/auth/login.php
 * 
 * 请求参数：
 * - username: 用户名 (必填)
 * - password: 密码 (必填)
 * 
 * 返回格式：
 * {
 *   "code": 200,
 *   "message": "登录成功",
 *   "data": {
 *     "token": "用户token",
 *     "user": {
 *       "id": 1,
 *       "username": "testuser",
 *       "email": "<EMAIL>",
 *       "real_name": "测试用户",
 *       "last_login": "2024-01-01 12:00:00"
 *     }
 *   },
 *   "timestamp": 1234567890
 * }
 */

header('Content-Type: application/json; charset=utf-8');

// 引入必要文件
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../../includes/auth.php';

// 设置CORS
setCORSHeaders();

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('只允许POST请求', 405);
}

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 如果JSON解析失败，尝试从POST获取
    if (json_last_error() !== JSON_ERROR_NONE) {
        $input = $_POST;
    }
    
    // 验证必填字段
    if (empty($input['username']) || empty($input['password'])) {
        errorResponse('用户名和密码不能为空', API_ERROR_CODE);
    }
    
    $username = trim($input['username']);
    $password = $input['password'];
    
    // 创建认证实例
    $auth = new Auth();
    
    // 执行登录
    $result = $auth->login($username, $password);
    
    if ($result['success']) {
        successResponse([
            'token' => $result['token'],
            'user' => $result['user']
        ], $result['message']);
    } else {
        errorResponse($result['message'] ?? '登录失败', API_UNAUTHORIZED_CODE);
    }
    
} catch (Exception $e) {
    logMessage('ERROR', 'API登录错误', [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'username' => $input['username'] ?? '',
        'ip' => getClientIP()
    ]);
    
    errorResponse($e->getMessage(), API_UNAUTHORIZED_CODE);
}
?>
