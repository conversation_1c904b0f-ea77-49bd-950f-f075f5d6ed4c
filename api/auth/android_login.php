<?php
/**
 * Android专用登录API 💖
 * 登录后自动检查Android权限，确保用户有权限访问
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Android-Package, X-App-Version');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许POST请求',
        'code' => 405
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    // 引入必要文件
    require_once __DIR__ . '/../config/config.php';
    require_once __DIR__ . '/../../includes/auth.php';
    require_once __DIR__ . '/../../includes/module_permission_manager.php';

    // 获取请求数据
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);
    
    // 如果JSON解析失败，返回错误
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('请求数据格式错误: ' . json_last_error_msg());
    }
    
    // 验证必填字段
    if (empty($input['username']) || empty($input['password'])) {
        throw new Exception('用户名和密码不能为空');
    }
    
    // 获取Android特有的请求头
    $androidPackage = $_SERVER['HTTP_X_ANDROID_PACKAGE'] ?? '';
    $appVersion = $_SERVER['HTTP_X_APP_VERSION'] ?? '';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // 验证是否来自Android应用
    $isAndroidRequest = false;
    if (!empty($androidPackage) || strpos(strtolower($userAgent), 'android') !== false) {
        $isAndroidRequest = true;
    }
    
    $username = trim($input['username']);
    $password = $input['password'];
    
    // 记录登录尝试
    logMessage('INFO', 'Android登录尝试', [
        'username' => $username,
        'android_package' => $androidPackage,
        'app_version' => $appVersion,
        'user_agent' => $userAgent,
        'is_android' => $isAndroidRequest,
        'ip' => getClientIP()
    ]);
    
    // 创建认证实例
    $auth = new Auth();
    
    // 执行登录
    $loginResult = $auth->login($username, $password);
    
    if ($loginResult['success']) {
        $userId = $loginResult['user']['id'];
        $token = $loginResult['token'];
        
        // 检查用户的Android权限
        $database = new Database();
        $conn = $database->getConnection();
        $permissionManager = new ModulePermissionManager($conn);
        
        // 获取用户权限
        $permissions = $permissionManager->getUserPermissions($userId);
        
        // 检查是否有Android权限
        $hasAndroidAccess = in_array('android', $permissions['apps']);
        $androidDownloads = $permissions['android_downloads'];
        $canDownload = $hasAndroidAccess && $androidDownloads > 0;
        
        // 检查权限是否过期
        $sql = "SELECT expires_at FROM user_app_permissions WHERE user_id = ? AND app_type = 'android' AND is_active = 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$userId]);
        $androidPermission = $stmt->fetch();
        
        $isExpired = false;
        $expiresAt = null;
        if ($androidPermission) {
            $expiresAt = $androidPermission['expires_at'];
            $isExpired = $expiresAt && strtotime($expiresAt) <= time();
        }
        
        // 如果没有Android权限，返回特殊提示
        if (!$hasAndroidAccess) {
            $response = [
                'success' => false,
                'message' => '您的账号没有Android应用访问权限',
                'code' => 403,
                'data' => [
                    'user' => $loginResult['user'],
                    'android_access' => false,
                    'reason' => 'no_android_permission',
                    'help' => [
                        '请联系管理员申请Android应用访问权限',
                        '或者检查您的账号是否已通过审批'
                    ]
                ],
                'timestamp' => time()
            ];
            
            logMessage('WARNING', 'Android登录被拒绝 - 无权限', [
                'user_id' => $userId,
                'username' => $username,
                'has_android_access' => $hasAndroidAccess
            ]);
            
            http_response_code(403);
            echo json_encode($response, JSON_UNESCAPED_UNICODE);
            exit();
        }
        
        // 如果权限已过期
        if ($isExpired) {
            $response = [
                'success' => false,
                'message' => '您的Android应用权限已过期',
                'code' => 403,
                'data' => [
                    'user' => $loginResult['user'],
                    'android_access' => true,
                    'expired' => true,
                    'expires_at' => $expiresAt,
                    'reason' => 'permission_expired',
                    'help' => [
                        '您的Android权限已于 ' . date('Y-m-d H:i:s', strtotime($expiresAt)) . ' 过期',
                        '请联系管理员续期权限'
                    ]
                ],
                'timestamp' => time()
            ];
            
            logMessage('WARNING', 'Android登录被拒绝 - 权限过期', [
                'user_id' => $userId,
                'username' => $username,
                'expires_at' => $expiresAt
            ]);
            
            http_response_code(403);
            echo json_encode($response, JSON_UNESCAPED_UNICODE);
            exit();
        }
        
        // 登录成功且有权限，返回完整信息
        $response = [
            'success' => true,
            'message' => '登录成功',
            'code' => 200,
            'data' => [
                'token' => $token,
                'user' => $loginResult['user'],
                'android_permissions' => [
                    'has_access' => true,
                    'download_count' => $androidDownloads,
                    'can_download' => $canDownload,
                    'expires_at' => $expiresAt,
                    'is_expired' => false
                ],
                'capabilities' => $permissions,
                'android_detected' => $isAndroidRequest
            ],
            'timestamp' => time()
        ];
        
        // 更新Android设备信息
        if ($isAndroidRequest && (!empty($androidPackage) || !empty($appVersion))) {
            try {
                $deviceSql = "INSERT INTO android_device_info (user_id, package_name, app_version, device_info, created_at) 
                             VALUES (?, ?, ?, ?, NOW())
                             ON DUPLICATE KEY UPDATE 
                             app_version = VALUES(app_version),
                             device_info = VALUES(device_info),
                             updated_at = NOW()";
                $deviceStmt = $conn->prepare($deviceSql);
                $deviceStmt->execute([$userId, $androidPackage, $appVersion, $userAgent]);
            } catch (Exception $e) {
                // 忽略设备信息更新错误
                logMessage('WARNING', 'Android设备信息更新失败', ['error' => $e->getMessage()]);
            }
        }
        
        logMessage('INFO', 'Android登录成功', [
            'user_id' => $userId,
            'username' => $username,
            'download_count' => $androidDownloads,
            'can_download' => $canDownload
        ]);
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        
    } else {
        throw new Exception($loginResult['message'] ?? '登录失败');
    }
    
} catch (Exception $e) {
    // 记录错误
    logMessage('ERROR', 'Android登录API错误', [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'ip' => getClientIP(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);
    
    // 返回错误响应
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'code' => 400,
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
}
?>
