<?php
/**
 * 发送邮箱验证码API
 * 管理系统 - management.djxs.xyz
 * 
 * 接口说明：
 * POST /api/auth/send_verification.php
 * 
 * 请求参数：
 * - email: 邮箱地址 (必填)
 * - type: 验证类型 (可选, 默认为register)
 *   - register: 注册验证码
 *   - password_reset: 密码重置验证码
 *   - login: 登录验证码
 * 
 * 返回格式：
 * {
 *   "code": 200,
 *   "message": "验证码发送成功",
 *   "data": null,
 *   "timestamp": 1234567890
 * }
 */

header('Content-Type: application/json; charset=utf-8');

// 引入必要文件
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../../includes/auth.php';

// 设置CORS
setCORSHeaders();

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('只允许POST请求', 405);
}

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 如果JSON解析失败，尝试从POST获取
    if (json_last_error() !== JSON_ERROR_NONE) {
        $input = $_POST;
    }
    
    // 验证必填字段
    if (empty($input['email'])) {
        errorResponse('邮箱地址不能为空', API_ERROR_CODE);
    }
    
    $email = trim($input['email']);
    $type = trim($input['type'] ?? 'register');
    
    // 验证邮箱格式
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        errorResponse('邮箱格式不正确', API_ERROR_CODE);
    }
    
    // 验证类型
    $allowedTypes = ['register', 'password_reset', 'login'];
    if (!in_array($type, $allowedTypes)) {
        errorResponse('无效的验证类型', API_ERROR_CODE);
    }
    
    // 如果是注册验证码，检查邮箱是否已存在
    if ($type === 'register') {
        $database = new Database();
        $conn = $database->getConnection();
        
        // 检查邮箱是否已存在（正式用户和待审批用户）
        $checkEmailSql = "SELECT COUNT(*) FROM pending_users WHERE email = ? AND status = 'pending'
                         UNION ALL
                         SELECT COUNT(*) FROM users WHERE email = ?";
        $checkEmailStmt = $conn->prepare($checkEmailSql);
        $checkEmailStmt->execute([$email, $email]);
        $emailResults = $checkEmailStmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (array_sum($emailResults) > 0) {
            errorResponse('该邮箱已被注册或正在审批中', API_ERROR_CODE);
        }
    }
    
    // 创建认证实例并发送验证码
    $auth = new Auth();
    $result = $auth->sendEmailVerification($email, $type);
    
    if ($result['success']) {
        successResponse(null, $result['message'] ?? '验证码发送成功');
    } else {
        errorResponse($result['message'] ?? '验证码发送失败');
    }
    
} catch (Exception $e) {
    logMessage('ERROR', 'API发送验证码错误', [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'email' => $input['email'] ?? '',
        'type' => $input['type'] ?? '',
        'ip' => getClientIP()
    ]);
    
    errorResponse($e->getMessage(), API_SERVER_ERROR_CODE);
}
?>
