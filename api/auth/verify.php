<?php
/**
 * Token验证API
 * 管理系统 - management.djxs.xyz
 * 
 * 接口说明：
 * POST /api/auth/verify.php
 * 
 * 请求参数：
 * - token: 用户token (必填)
 * 或者在Header中传递：Authorization: Bearer {token}
 * 
 * 返回格式：
 * {
 *   "code": 200,
 *   "message": "Token验证成功",
 *   "data": {
 *     "user": {
 *       "id": 1,
 *       "username": "testuser",
 *       "email": "<EMAIL>"
 *     }
 *   },
 *   "timestamp": 1234567890
 * }
 */

header('Content-Type: application/json; charset=utf-8');

// 引入必要文件
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../../includes/auth.php';

// 设置CORS
setCORSHeaders();

// 允许GET和POST请求
if (!in_array($_SERVER['REQUEST_METHOD'], ['GET', 'POST'])) {
    errorResponse('只允许GET或POST请求', 405);
}

try {
    $token = '';
    
    // 从多个来源获取token
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $input = $_POST;
        }
        $token = $input['token'] ?? '';
    } else {
        $token = $_GET['token'] ?? '';
    }
    
    // 从Authorization header获取token
    if (empty($token)) {
        $authHeader = '';

        // 更健壮的头部获取方法
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
            // 尝试多种可能的键名
            $authKeys = ['Authorization', 'authorization', 'AUTHORIZATION'];
            foreach ($authKeys as $key) {
                if (isset($headers[$key])) {
                    $authHeader = $headers[$key];
                    break;
                }
            }
        }

        // 如果getallheaders()失败，直接从$_SERVER获取
        if (!$authHeader) {
            $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        }

        if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $token = $matches[1];
        }
    }
    
    // 验证token是否存在
    if (empty($token)) {
        errorResponse('Token不能为空', API_UNAUTHORIZED_CODE);
    }
    
    // 创建认证实例
    $auth = new Auth();
    
    // 验证token
    $result = $auth->verifyToken($token);
    
    if ($result['success']) {
        successResponse([
            'user' => $result['user']
        ], 'Token验证成功');
    } else {
        errorResponse($result['message'], API_UNAUTHORIZED_CODE);
    }
    
} catch (Exception $e) {
    logMessage('ERROR', 'API Token验证错误', [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'ip' => getClientIP()
    ]);
    
    errorResponse('Token验证失败', API_UNAUTHORIZED_CODE);
}
?>
