<?php
/**
 * 模块权限管理器
 * 简化的权限系统 - 基于应用和模块的授权
 */

class ModulePermissionManager {
    private $conn;
    
    // 应用类型
    const APP_ANDROID = 'android';
    const APP_WINDOWS = 'windows';
    
    // Windows工具箱模块
    const WINDOWS_MODULES = [
        'batch_rename' => '批量重命名',
        'format_names' => '整理文件名',
        'folder_counter' => '文件夹计数重命名',
        'author_organizer' => '同名作者整理',
        'anti_piracy_generator' => '防盗文件生成',
        'content_cleaner' => '文件内容净化',
        'file_duplicate_finder' => '文件查重',
        'directory_tree_generator' => '目录树生成'
    ];
    
    public function __construct($database) {
        $this->conn = $database;
    }
    
    /**
     * 检查用户是否有应用访问权限
     */
    public function hasAppAccess($userId, $appType) {
        try {
            $sql = "SELECT COUNT(*) FROM user_app_permissions 
                    WHERE user_id = ? AND app_type = ? AND is_active = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId, $appType]);
            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            error_log('检查应用权限失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查用户是否有特定模块权限
     */
    public function hasModuleAccess($userId, $moduleCode) {
        try {
            $sql = "SELECT COUNT(*) FROM user_module_permissions 
                    WHERE user_id = ? AND module_code = ? AND is_active = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId, $moduleCode]);
            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            error_log('检查模块权限失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取用户Android下载次数
     */
    public function getAndroidDownloadCount($userId) {
        try {
            $sql = "SELECT download_count FROM user_app_permissions 
                    WHERE user_id = ? AND app_type = ? AND is_active = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId, self::APP_ANDROID]);
            $result = $stmt->fetch();
            return $result ? (int)$result['download_count'] : 0;
        } catch (Exception $e) {
            error_log('获取下载次数失败: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 消费Android下载次数
     */
    public function consumeAndroidDownload($userId) {
        try {
            $this->conn->beginTransaction();
            
            // 检查当前次数
            $currentCount = $this->getAndroidDownloadCount($userId);
            if ($currentCount <= 0) {
                throw new Exception('下载次数不足');
            }
            
            // 减少次数
            $sql = "UPDATE user_app_permissions 
                    SET download_count = download_count - 1, updated_at = NOW()
                    WHERE user_id = ? AND app_type = ? AND is_active = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId, self::APP_ANDROID]);
            
            $this->conn->commit();
            return ['success' => true, 'remaining' => $currentCount - 1];
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 授予用户应用访问权限（升级版 - 支持授权时长）
     */
    public function grantAppAccess($userId, $appType, $downloadCount = 0, $expiresAt = null, $grantedBy = null, $notes = '') {
        try {
            // 检查是否已有权限
            $sql = "SELECT id FROM user_app_permissions
                    WHERE user_id = ? AND app_type = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId, $appType]);

            if ($stmt->fetch()) {
                // 更新现有权限
                $sql = "UPDATE user_app_permissions
                        SET is_active = 1, download_count = ?, expires_at = ?,
                            granted_by = ?, notes = ?, granted_at = NOW(), updated_at = NOW()
                        WHERE user_id = ? AND app_type = ?";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$downloadCount, $expiresAt, $grantedBy, $notes, $userId, $appType]);
            } else {
                // 创建新权限
                $sql = "INSERT INTO user_app_permissions
                        (user_id, app_type, download_count, expires_at, granted_by, notes, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, 1)";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$userId, $appType, $downloadCount, $expiresAt, $grantedBy, $notes]);
            }

            return ['success' => true, 'message' => '应用权限授予成功'];

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 撤销用户应用访问权限
     */
    public function revokeAppAccess($userId, $appType) {
        try {
            $sql = "UPDATE user_app_permissions 
                    SET is_active = 0, updated_at = NOW()
                    WHERE user_id = ? AND app_type = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId, $appType]);
            
            return ['success' => true, 'message' => '应用权限撤销成功'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 授予用户模块权限
     */
    public function grantModuleAccess($userId, $moduleCode, $grantedBy = null) {
        try {
            // 检查模块是否有效
            if (!isset(self::WINDOWS_MODULES[$moduleCode])) {
                throw new Exception('无效的模块代码');
            }

            // 检查是否已有权限
            $sql = "SELECT id FROM user_module_permissions
                    WHERE user_id = ? AND module_code = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId, $moduleCode]);

            if ($stmt->fetch()) {
                // 更新现有权限
                $sql = "UPDATE user_module_permissions
                        SET is_active = 1, granted_by = ?, updated_at = NOW()
                        WHERE user_id = ? AND module_code = ?";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$grantedBy, $userId, $moduleCode]);
            } else {
                // 创建新权限
                $sql = "INSERT INTO user_module_permissions (user_id, module_code, is_active, granted_by)
                        VALUES (?, ?, 1, ?)";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$userId, $moduleCode, $grantedBy]);
            }

            return ['success' => true, 'message' => '模块权限授予成功'];

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 撤销用户模块权限
     */
    public function revokeModuleAccess($userId, $moduleCode) {
        try {
            $sql = "UPDATE user_module_permissions 
                    SET is_active = 0, updated_at = NOW()
                    WHERE user_id = ? AND module_code = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId, $moduleCode]);
            
            return ['success' => true, 'message' => '模块权限撤销成功'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 调整Android下载次数
     */
    public function adjustAndroidDownloadCount($userId, $adjustment, $operation = 'add') {
        try {
            $this->conn->beginTransaction();
            
            if ($operation === 'add') {
                $sql = "UPDATE user_app_permissions 
                        SET download_count = download_count + ?, updated_at = NOW()
                        WHERE user_id = ? AND app_type = ? AND is_active = 1";
            } else {
                $sql = "UPDATE user_app_permissions 
                        SET download_count = GREATEST(0, download_count - ?), updated_at = NOW()
                        WHERE user_id = ? AND app_type = ? AND is_active = 1";
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$adjustment, $userId, self::APP_ANDROID]);
            
            $this->conn->commit();
            return ['success' => true, 'message' => '下载次数调整成功'];
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 获取用户所有权限（升级版 - 包含到期时间信息）
     */
    public function getUserPermissions($userId) {
        try {
            $permissions = [
                'apps' => [],
                'modules' => [],
                'android_downloads' => 0,
                'app_details' => []
            ];

            // 获取应用权限（包含到期时间）
            $sql = "SELECT app_type, download_count, expires_at, granted_at, granted_by, notes
                    FROM user_app_permissions
                    WHERE user_id = ? AND is_active = 1
                    AND (expires_at IS NULL OR expires_at > NOW())";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);

            while ($row = $stmt->fetch()) {
                $permissions['apps'][] = $row['app_type'];
                $permissions['app_details'][$row['app_type']] = [
                    'download_count' => (int)$row['download_count'],
                    'expires_at' => $row['expires_at'],
                    'granted_at' => $row['granted_at'],
                    'granted_by' => $row['granted_by'],
                    'notes' => $row['notes'],
                    'is_expired' => false
                ];

                if ($row['app_type'] === self::APP_ANDROID) {
                    $permissions['android_downloads'] = (int)$row['download_count'];
                }
            }

            // 获取模块权限
            $sql = "SELECT module_code FROM user_module_permissions
                    WHERE user_id = ? AND is_active = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);

            while ($row = $stmt->fetch()) {
                $permissions['modules'][] = $row['module_code'];
            }

            return $permissions;

        } catch (Exception $e) {
            error_log('获取用户权限失败: ' . $e->getMessage());
            return ['apps' => [], 'modules' => [], 'android_downloads' => 0, 'app_details' => []];
        }
    }
    
    /**
     * 获取所有可用模块
     */
    public function getAvailableModules() {
        return self::WINDOWS_MODULES;
    }

    /**
     * 检查并处理过期权限
     */
    public function processExpiredPermissions() {
        try {
            $sql = "UPDATE user_app_permissions
                    SET is_active = 0, updated_at = NOW()
                    WHERE expires_at IS NOT NULL AND expires_at <= NOW() AND is_active = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();

            $expiredCount = $stmt->rowCount();
            return ['success' => true, 'expired_count' => $expiredCount];

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 获取即将过期的权限（7天内）
     */
    public function getExpiringPermissions($days = 7) {
        try {
            $sql = "SELECT u.username, u.email, uap.app_type, uap.expires_at
                    FROM user_app_permissions uap
                    JOIN users u ON uap.user_id = u.id
                    WHERE uap.expires_at IS NOT NULL
                    AND uap.expires_at > NOW()
                    AND uap.expires_at <= DATE_ADD(NOW(), INTERVAL ? DAY)
                    AND uap.is_active = 1
                    ORDER BY uap.expires_at ASC";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$days]);

            return $stmt->fetchAll();

        } catch (Exception $e) {
            error_log('获取即将过期权限失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 延长权限有效期
     */
    public function extendPermission($userId, $appType, $extendDays, $grantedBy = null) {
        try {
            $sql = "UPDATE user_app_permissions
                    SET expires_at = CASE
                        WHEN expires_at IS NULL THEN DATE_ADD(NOW(), INTERVAL ? DAY)
                        WHEN expires_at > NOW() THEN DATE_ADD(expires_at, INTERVAL ? DAY)
                        ELSE DATE_ADD(NOW(), INTERVAL ? DAY)
                    END,
                    granted_by = ?, updated_at = NOW()
                    WHERE user_id = ? AND app_type = ? AND is_active = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$extendDays, $extendDays, $extendDays, $grantedBy, $userId, $appType]);

            if ($stmt->rowCount() > 0) {
                return ['success' => true, 'message' => "权限有效期已延长 {$extendDays} 天"];
            } else {
                return ['success' => false, 'message' => '未找到有效的权限记录'];
            }

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
?>
