<?php
/**
 * 管理员功能类
 * 管理系统 - management.djxs.xyz
 */

require_once __DIR__ . '/../api/config/config.php';
require_once __DIR__ . '/../api/config/database.php';

class Admin {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }

    /**
     * 管理员登录
     */
    public function login($username, $password) {
        try {
            $sql = "SELECT * FROM admin_users WHERE username = ? AND status = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$username]);
            $admin = $stmt->fetch();

            if (!$admin || !password_verify($password, $admin['password'])) {
                throw new Exception('用户名或密码错误');
            }

            // 更新最后登录时间
            $updateSql = "UPDATE admin_users SET last_login = NOW() WHERE id = ?";
            $updateStmt = $this->conn->prepare($updateSql);
            $updateStmt->execute([$admin['id']]);

            // 设置session
            session_start();
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_username'] = $admin['username'];
            $_SESSION['admin_login_time'] = time();

            return ['success' => true, 'admin' => $admin];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 检查管理员登录状态
     */
    public function checkLogin() {
        session_start();
        return isset($_SESSION['admin_id']) && !empty($_SESSION['admin_id']);
    }

    /**
     * 管理员登出
     */
    public function logout() {
        session_start();
        session_destroy();
        return true;
    }

    /**
     * 获取待审批用户列表
     */
    public function getPendingUsers($page = 1, $limit = 20) {
        try {
            $offset = ($page - 1) * $limit;

            $sql = "SELECT * FROM pending_users WHERE status = 'pending' ORDER BY created_at DESC LIMIT ? OFFSET ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$limit, $offset]);
            $users = $stmt->fetchAll();

            // 获取总数
            $countSql = "SELECT COUNT(*) FROM pending_users WHERE status = 'pending'";
            $countStmt = $this->conn->prepare($countSql);
            $countStmt->execute();
            $total = $countStmt->fetchColumn();

            return [
                'success' => true,
                'users' => $users,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 审批用户注册
     */
    public function approveUser($pendingId, $adminId, $note = '') {
        try {
            $this->conn->beginTransaction();

            // 获取待审批用户信息
            $sql = "SELECT * FROM pending_users WHERE id = ? AND status = 'pending'";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$pendingId]);
            $pendingUser = $stmt->fetch();

            if (!$pendingUser) {
                throw new Exception('待审批用户不存在');
            }

            // 移动到正式用户表
            $insertSql = "INSERT INTO users (username, password, email, phone, real_name, created_at) VALUES (?, ?, ?, ?, ?, ?)";
            $insertStmt = $this->conn->prepare($insertSql);
            $insertStmt->execute([
                $pendingUser['username'],
                $pendingUser['password'],
                $pendingUser['email'],
                $pendingUser['phone'],
                $pendingUser['real_name'],
                $pendingUser['created_at']
            ]);

            $userId = $this->conn->lastInsertId();

            // 更新待审批表状态
            $updateSql = "UPDATE pending_users SET status = 'approved', admin_id = ?, admin_note = ?, processed_at = NOW() WHERE id = ?";
            $updateStmt = $this->conn->prepare($updateSql);
            $updateStmt->execute([$adminId, $note, $pendingId]);

            // 记录操作日志
            $this->logOperation($adminId, $userId, 'approve_user', "审批通过用户注册: {$pendingUser['username']}");

            $this->conn->commit();
            return ['success' => true, 'message' => '用户审批成功'];

        } catch (Exception $e) {
            $this->conn->rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 拒绝用户注册
     */
    public function rejectUser($pendingId, $adminId, $note = '') {
        try {
            $sql = "UPDATE pending_users SET status = 'rejected', admin_id = ?, admin_note = ?, processed_at = NOW() WHERE id = ? AND status = 'pending'";
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$adminId, $note, $pendingId]);

            if ($stmt->rowCount() > 0) {
                $this->logOperation($adminId, null, 'reject_user', "拒绝用户注册申请 ID: {$pendingId}");
                return ['success' => true, 'message' => '用户申请已拒绝'];
            } else {
                return ['success' => false, 'message' => '操作失败'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 批量审批用户注册
     */
    public function batchApproveUsers($pendingIds, $adminId, $note = '') {
        try {
            $this->conn->beginTransaction();

            $successCount = 0;
            $failedCount = 0;
            $failedUsers = [];

            foreach ($pendingIds as $pendingId) {
                try {
                    // 获取待审批用户信息
                    $sql = "SELECT * FROM pending_users WHERE id = ? AND status = 'pending'";
                    $stmt = $this->conn->prepare($sql);
                    $stmt->execute([$pendingId]);
                    $pendingUser = $stmt->fetch();

                    if (!$pendingUser) {
                        $failedCount++;
                        $failedUsers[] = $pendingId;
                        continue;
                    }

                    // 移动到正式用户表
                    $insertSql = "INSERT INTO users (username, password, email, phone, real_name, created_at) VALUES (?, ?, ?, ?, ?, ?)";
                    $insertStmt = $this->conn->prepare($insertSql);
                    $insertStmt->execute([
                        $pendingUser['username'],
                        $pendingUser['password'],
                        $pendingUser['email'],
                        $pendingUser['phone'],
                        $pendingUser['real_name'],
                        $pendingUser['created_at']
                    ]);

                    $userId = $this->conn->lastInsertId();

                    // 更新待审批表状态
                    $updateSql = "UPDATE pending_users SET status = 'approved', admin_id = ?, admin_note = ?, processed_at = NOW() WHERE id = ?";
                    $updateStmt = $this->conn->prepare($updateSql);
                    $updateStmt->execute([$adminId, $note, $pendingId]);

                    // 记录操作日志
                    $this->logOperation($adminId, $userId, 'approve_user', "审批通过用户注册: {$pendingUser['username']}");

                    $successCount++;

                } catch (Exception $e) {
                    $failedCount++;
                    $failedUsers[] = $pendingId;
                }
            }

            if ($successCount > 0) {
                $this->logOperation($adminId, null, 'batch_approve_users',
                    "批量审批通过 {$successCount} 个用户，失败 {$failedCount} 个");
            }

            $this->conn->commit();

            if ($failedCount === 0) {
                return ['success' => true, 'message' => "成功批量通过 {$successCount} 个用户申请"];
            } else {
                return ['success' => true, 'message' => "批量操作完成：成功 {$successCount} 个，失败 {$failedCount} 个"];
            }

        } catch (Exception $e) {
            $this->conn->rollBack();
            return ['success' => false, 'message' => '批量操作失败: ' . $e->getMessage()];
        }
    }

    /**
     * 批量拒绝用户注册
     */
    public function batchRejectUsers($pendingIds, $adminId, $note = '') {
        try {
            $this->conn->beginTransaction();

            $successCount = 0;
            $failedCount = 0;

            foreach ($pendingIds as $pendingId) {
                try {
                    $sql = "UPDATE pending_users SET status = 'rejected', admin_id = ?, admin_note = ?, processed_at = NOW() WHERE id = ? AND status = 'pending'";
                    $stmt = $this->conn->prepare($sql);
                    $result = $stmt->execute([$adminId, $note, $pendingId]);

                    if ($stmt->rowCount() > 0) {
                        $this->logOperation($adminId, null, 'reject_user', "拒绝用户注册申请 ID: {$pendingId}");
                        $successCount++;
                    } else {
                        $failedCount++;
                    }
                } catch (Exception $e) {
                    $failedCount++;
                }
            }

            if ($successCount > 0) {
                $this->logOperation($adminId, null, 'batch_reject_users',
                    "批量拒绝 {$successCount} 个用户申请，失败 {$failedCount} 个");
            }

            $this->conn->commit();

            if ($failedCount === 0) {
                return ['success' => true, 'message' => "成功批量拒绝 {$successCount} 个用户申请"];
            } else {
                return ['success' => true, 'message' => "批量操作完成：成功 {$successCount} 个，失败 {$failedCount} 个"];
            }

        } catch (Exception $e) {
            $this->conn->rollBack();
            return ['success' => false, 'message' => '批量操作失败: ' . $e->getMessage()];
        }
    }

    /**
     * 通过所有待审批用户
     */
    public function approveAllUsers($adminId, $note = '') {
        try {
            $this->conn->beginTransaction();

            // 获取所有待审批用户
            $sql = "SELECT id FROM pending_users WHERE status = 'pending'";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $pendingIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (empty($pendingIds)) {
                return ['success' => false, 'message' => '没有待审批的用户'];
            }

            $result = $this->batchApproveUsers($pendingIds, $adminId, $note);

            $this->conn->commit();
            return $result;

        } catch (Exception $e) {
            $this->conn->rollBack();
            return ['success' => false, 'message' => '全部通过操作失败: ' . $e->getMessage()];
        }
    }

    /**
     * 拒绝所有待审批用户
     */
    public function rejectAllUsers($adminId, $note = '') {
        try {
            $this->conn->beginTransaction();

            // 获取所有待审批用户
            $sql = "SELECT id FROM pending_users WHERE status = 'pending'";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $pendingIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (empty($pendingIds)) {
                return ['success' => false, 'message' => '没有待审批的用户'];
            }

            $result = $this->batchRejectUsers($pendingIds, $adminId, $note);

            $this->conn->commit();
            return $result;

        } catch (Exception $e) {
            $this->conn->rollBack();
            return ['success' => false, 'message' => '全部拒绝操作失败: ' . $e->getMessage()];
        }
    }

    /**
     * 获取用户列表
     */
    public function getUsers($page = 1, $limit = 20, $status = '') {
        try {
            $offset = ($page - 1) * $limit;
            $whereClause = '';
            $params = [$limit, $offset];

            if (!empty($status)) {
                $whereClause = "WHERE status = ?";
                array_unshift($params, $status);
            }

            $sql = "SELECT * FROM users {$whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            $users = $stmt->fetchAll();

            // 获取总数
            $countSql = "SELECT COUNT(*) FROM users {$whereClause}";
            $countStmt = $this->conn->prepare($countSql);
            if (!empty($status)) {
                $countStmt->execute([$status]);
            } else {
                $countStmt->execute();
            }
            $total = $countStmt->fetchColumn();

            return [
                'success' => true,
                'users' => $users,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 封禁用户
     */
    public function banUser($userId, $adminId, $reason = '') {
        try {
            $sql = "UPDATE users SET status = 'banned', banned_at = NOW(), banned_reason = ?, banned_by = ? WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$reason, $adminId, $userId]);

            if ($stmt->rowCount() > 0) {
                // 清除用户所有session
                $clearSessionSql = "UPDATE user_sessions SET is_active = 0 WHERE user_id = ?";
                $clearStmt = $this->conn->prepare($clearSessionSql);
                $clearStmt->execute([$userId]);

                $this->logOperation($adminId, $userId, 'ban_user', "封禁用户，原因: {$reason}");
                return ['success' => true, 'message' => '用户已封禁'];
            } else {
                return ['success' => false, 'message' => '操作失败'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 解封用户
     */
    public function unbanUser($userId, $adminId) {
        try {
            $sql = "UPDATE users SET status = 'active', banned_at = NULL, banned_reason = NULL, banned_by = NULL WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$userId]);

            if ($stmt->rowCount() > 0) {
                $this->logOperation($adminId, $userId, 'unban_user', "解封用户");
                return ['success' => true, 'message' => '用户已解封'];
            } else {
                return ['success' => false, 'message' => '操作失败'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 批量封禁用户
     */
    public function batchBanUsers($userIds, $adminId, $reason = '') {
        try {
            $this->conn->beginTransaction();

            $successCount = 0;
            $failedCount = 0;

            foreach ($userIds as $userId) {
                $result = $this->banUser($userId, $adminId, $reason);
                if ($result['success']) {
                    $successCount++;
                } else {
                    $failedCount++;
                }
            }

            if ($successCount > 0) {
                $this->logOperation($adminId, null, 'batch_ban_users',
                    "批量封禁 {$successCount} 个用户，失败 {$failedCount} 个，原因: {$reason}");
            }

            $this->conn->commit();

            if ($failedCount === 0) {
                return ['success' => true, 'message' => "成功批量封禁 {$successCount} 个用户"];
            } else {
                return ['success' => true, 'message' => "批量操作完成：成功 {$successCount} 个，失败 {$failedCount} 个"];
            }

        } catch (Exception $e) {
            $this->conn->rollBack();
            return ['success' => false, 'message' => '批量封禁失败: ' . $e->getMessage()];
        }
    }

    /**
     * 批量解封用户
     */
    public function batchUnbanUsers($userIds, $adminId) {
        try {
            $this->conn->beginTransaction();

            $successCount = 0;
            $failedCount = 0;

            foreach ($userIds as $userId) {
                $result = $this->unbanUser($userId, $adminId);
                if ($result['success']) {
                    $successCount++;
                } else {
                    $failedCount++;
                }
            }

            if ($successCount > 0) {
                $this->logOperation($adminId, null, 'batch_unban_users',
                    "批量解封 {$successCount} 个用户，失败 {$failedCount} 个");
            }

            $this->conn->commit();

            if ($failedCount === 0) {
                return ['success' => true, 'message' => "成功批量解封 {$successCount} 个用户"];
            } else {
                return ['success' => true, 'message' => "批量操作完成：成功 {$successCount} 个，失败 {$failedCount} 个"];
            }

        } catch (Exception $e) {
            $this->conn->rollBack();
            return ['success' => false, 'message' => '批量解封失败: ' . $e->getMessage()];
        }
    }

    /**
     * 删除用户（物理删除）
     */
    public function deleteUser($userId, $adminId, $reason = '') {
        try {
            $this->conn->beginTransaction();

            // 先检查用户是否存在，获取用户名和邮箱
            $checkSql = "SELECT username, email FROM users WHERE id = ?";
            $checkStmt = $this->conn->prepare($checkSql);
            $checkStmt->execute([$userId]);
            $user = $checkStmt->fetch();

            if (!$user) {
                $this->conn->rollBack();
                return ['success' => false, 'message' => '用户不存在'];
            }

            // 记录删除日志（在删除前记录）
            $this->logOperation($adminId, $userId, 'delete_user', "彻底删除用户: {$user['username']}，原因: {$reason}");

            // 删除用户相关的所有数据

            // 1. 删除用户会话
            $deleteSessionSql = "DELETE FROM user_sessions WHERE user_id = ?";
            $deleteSessionStmt = $this->conn->prepare($deleteSessionSql);
            $deleteSessionStmt->execute([$userId]);

            // 2. 删除用户应用权限
            $deleteAppPermSql = "DELETE FROM user_app_permissions WHERE user_id = ?";
            $deleteAppPermStmt = $this->conn->prepare($deleteAppPermSql);
            $deleteAppPermStmt->execute([$userId]);

            // 3. 删除用户模块权限
            $deleteModPermSql = "DELETE FROM user_module_permissions WHERE user_id = ?";
            $deleteModPermStmt = $this->conn->prepare($deleteModPermSql);
            $deleteModPermStmt->execute([$userId]);

            // 4. 删除pending_users表中的相关记录（根据用户名和邮箱，清理所有相关记录）
            $deletePendingSql = "DELETE FROM pending_users WHERE username = ? OR email = ?";
            $deletePendingStmt = $this->conn->prepare($deletePendingSql);
            $deletePendingStmt->execute([$user['username'], $user['email']]);
            $pendingDeleted = $deletePendingStmt->rowCount();

            // 5. 删除用户主记录
            $deleteUserSql = "DELETE FROM users WHERE id = ?";
            $deleteUserStmt = $this->conn->prepare($deleteUserSql);
            $result = $deleteUserStmt->execute([$userId]);

            if ($deleteUserStmt->rowCount() > 0) {
                $this->conn->commit();
                $message = "用户 {$user['username']} 已彻底删除";
                if ($pendingDeleted > 0) {
                    $message .= "（同时清理了 {$pendingDeleted} 条历史申请记录）";
                }
                return ['success' => true, 'message' => $message];
            } else {
                $this->conn->rollBack();
                return ['success' => false, 'message' => '删除失败'];
            }

        } catch (Exception $e) {
            $this->conn->rollBack();
            return ['success' => false, 'message' => '删除失败: ' . $e->getMessage()];
        }
    }



    /**
     * 批量删除用户
     */
    public function batchDeleteUsers($userIds, $adminId, $reason = '') {
        try {
            $this->conn->beginTransaction();

            $successCount = 0;
            $failedCount = 0;

            foreach ($userIds as $userId) {
                $result = $this->deleteUser($userId, $adminId, $reason);
                if ($result['success']) {
                    $successCount++;
                } else {
                    $failedCount++;
                }
            }

            if ($successCount > 0) {
                $this->logOperation($adminId, null, 'batch_delete_users',
                    "批量删除 {$successCount} 个用户，失败 {$failedCount} 个，原因: {$reason}");
            }

            $this->conn->commit();

            if ($failedCount === 0) {
                return ['success' => true, 'message' => "成功批量删除 {$successCount} 个用户"];
            } else {
                return ['success' => true, 'message' => "批量操作完成：成功 {$successCount} 个，失败 {$failedCount} 个"];
            }

        } catch (Exception $e) {
            $this->conn->rollBack();
            return ['success' => false, 'message' => '批量删除失败: ' . $e->getMessage()];
        }
    }

    /**
     * 记录操作日志
     */
    private function logOperation($adminId, $userId, $action, $description) {
        try {
            $sql = "INSERT INTO operation_logs (admin_id, user_id, action, description, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([
                $adminId,
                $userId,
                $action,
                $description,
                getClientIP(),
                getUserAgent()
            ]);
        } catch (Exception $e) {
            logMessage('ERROR', '记录操作日志失败', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 获取用户详情
     */
    public function getUserDetail($userId) {
        try {
            // 获取用户基本信息
            $sql = "SELECT * FROM users WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);
            $user = $stmt->fetch();

            if (!$user) {
                return ['success' => false, 'message' => '用户不存在'];
            }

            // 获取用户会话信息
            $sessionSql = "SELECT COUNT(*) as total_sessions, MAX(created_at) as last_session
                          FROM user_sessions WHERE user_id = ?";
            $sessionStmt = $this->conn->prepare($sessionSql);
            $sessionStmt->execute([$userId]);
            $sessionInfo = $sessionStmt->fetch();

            // 获取活跃会话数
            $activeSql = "SELECT COUNT(*) FROM user_sessions WHERE user_id = ? AND is_active = 1 AND expires_at > NOW()";
            $activeStmt = $this->conn->prepare($activeSql);
            $activeStmt->execute([$userId]);
            $activeSessions = $activeStmt->fetchColumn();

            // 获取操作日志（最近10条）
            $logSql = "SELECT * FROM operation_logs WHERE user_id = ? ORDER BY created_at DESC LIMIT 10";
            $logStmt = $this->conn->prepare($logSql);
            $logStmt->execute([$userId]);
            $logs = $logStmt->fetchAll();

            // 如果用户被封禁，获取封禁信息
            $banInfo = null;
            if ($user['status'] === 'banned' && $user['banned_by']) {
                $banSql = "SELECT username FROM admin_users WHERE id = ?";
                $banStmt = $this->conn->prepare($banSql);
                $banStmt->execute([$user['banned_by']]);
                $banAdmin = $banStmt->fetch();

                $banInfo = [
                    'banned_at' => $user['banned_at'],
                    'banned_reason' => $user['banned_reason'],
                    'banned_by_admin' => $banAdmin ? $banAdmin['username'] : '未知'
                ];
            }

            return [
                'success' => true,
                'user' => $user,
                'session_info' => $sessionInfo,
                'active_sessions' => $activeSessions,
                'recent_logs' => $logs,
                'ban_info' => $banInfo
            ];

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 获取操作日志
     */
    public function getOperationLogs($page = 1, $limit = 50, $adminId = null, $action = '', $userId = null) {
        try {
            $offset = ($page - 1) * $limit;
            $whereConditions = [];
            $params = [];

            // 构建查询条件
            if ($adminId) {
                $whereConditions[] = "ol.admin_id = ?";
                $params[] = $adminId;
            }

            if (!empty($action)) {
                $whereConditions[] = "ol.action = ?";
                $params[] = $action;
            }

            if ($userId) {
                $whereConditions[] = "ol.user_id = ?";
                $params[] = $userId;
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // 获取日志列表
            $sql = "SELECT ol.*,
                           au.username as admin_username,
                           u.username as target_username
                    FROM operation_logs ol
                    LEFT JOIN admin_users au ON ol.admin_id = au.id
                    LEFT JOIN users u ON ol.user_id = u.id
                    {$whereClause}
                    ORDER BY ol.created_at DESC
                    LIMIT ? OFFSET ?";

            $params[] = $limit;
            $params[] = $offset;

            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            $logs = $stmt->fetchAll();

            // 获取总数
            $countSql = "SELECT COUNT(*) FROM operation_logs ol {$whereClause}";
            $countParams = array_slice($params, 0, -2); // 移除limit和offset参数
            $countStmt = $this->conn->prepare($countSql);
            $countStmt->execute($countParams);
            $total = $countStmt->fetchColumn();

            return [
                'success' => true,
                'logs' => $logs,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ];

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 获取数据库连接
     */
    public function getConnection() {
        return $this->conn;
    }

    /**
     * 获取管理员信息
     */
    public function getAdminInfo($adminId) {
        try {
            $sql = "SELECT * FROM admin_users WHERE id = ? AND status = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$adminId]);
            return $stmt->fetch();
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 获取统计数据
     */
    public function getStats() {
        try {
            $stats = [];

            // 总用户数
            $sql = "SELECT COUNT(*) FROM users";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['total_users'] = $stmt->fetchColumn();

            // 活跃用户数
            $sql = "SELECT COUNT(*) FROM users WHERE status = 'active'";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['active_users'] = $stmt->fetchColumn();

            // 被封禁用户数
            $sql = "SELECT COUNT(*) FROM users WHERE status = 'banned'";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['banned_users'] = $stmt->fetchColumn();

            // 待审批用户数
            $sql = "SELECT COUNT(*) FROM pending_users WHERE status = 'pending'";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['pending_users'] = $stmt->fetchColumn();

            // 今日注册数
            $sql = "SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['today_registrations'] = $stmt->fetchColumn();

            // 今日登录数
            $sql = "SELECT COUNT(DISTINCT user_id) FROM user_sessions WHERE DATE(created_at) = CURDATE()";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['today_logins'] = $stmt->fetchColumn();

            // 本周新增用户
            $sql = "SELECT COUNT(*) FROM users WHERE YEARWEEK(created_at) = YEARWEEK(NOW())";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['week_registrations'] = $stmt->fetchColumn();

            // 在线用户数（活跃会话）
            $sql = "SELECT COUNT(DISTINCT user_id) FROM user_sessions WHERE is_active = 1 AND expires_at > NOW()";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['online_users'] = $stmt->fetchColumn();

            return ['success' => true, 'stats' => $stats];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
?>
