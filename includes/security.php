<?php
/**
 * 安全管理类 - IP监控和安全事件处理
 * 管理系统 - management.djxs.xyz
 */

require_once __DIR__ . '/../api/config/config.php';
require_once __DIR__ . '/../api/config/database.php';

class SecurityManager {
    private $db;
    private $conn;
    private $configs;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
        $this->loadConfigs();
    }

    /**
     * 加载系统安全配置
     */
    private function loadConfigs() {
        try {
            $sql = "SELECT config_key, config_value FROM system_configs";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $configs = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            $this->configs = [
                'max_ip_per_user' => intval($configs['max_ip_per_user'] ?? 3),
                'ip_check_enabled' => boolval($configs['ip_check_enabled'] ?? 1),
                'auto_ban_enabled' => boolval($configs['auto_ban_enabled'] ?? 1),
                'ip_check_window' => intval($configs['ip_check_window'] ?? 24),
                'suspicious_login_threshold' => intval($configs['suspicious_login_threshold'] ?? 5),
                'geo_check_enabled' => boolval($configs['geo_check_enabled'] ?? 0)
            ];
        } catch (Exception $e) {
            // 使用默认配置
            $this->configs = [
                'max_ip_per_user' => 3,
                'ip_check_enabled' => true,
                'auto_ban_enabled' => true,
                'ip_check_window' => 24,
                'suspicious_login_threshold' => 5,
                'geo_check_enabled' => false
            ];
        }
    }

    /**
     * 检查用户登录IP
     */
    public function checkUserIP($userId, $ipAddress, $userAgent = '') {
        if (!$this->configs['ip_check_enabled']) {
            return ['allowed' => true, 'reason' => 'IP检查已禁用'];
        }

        try {
            // 记录或更新IP信息
            $this->recordIPAccess($userId, $ipAddress, $userAgent);

            // 检查IP是否被阻止
            if ($this->isIPBlocked($userId, $ipAddress)) {
                $this->logSecurityEvent($userId, 'blocked_ip_access', 'high', $ipAddress, 
                    '尝试使用被阻止的IP地址登录');
                return ['allowed' => false, 'reason' => 'IP地址已被阻止'];
            }

            // 检查用户的IP数量
            $ipCount = $this->getUserIPCount($userId);
            if ($ipCount > $this->configs['max_ip_per_user']) {
                // 触发多IP警告
                $this->handleMultipleIPViolation($userId, $ipAddress, $ipCount);
                return ['allowed' => false, 'reason' => '检测到异常多IP登录，账户已被保护性封禁'];
            }

            // 检查是否为新IP
            if ($this->isNewIP($userId, $ipAddress)) {
                $this->logSecurityEvent($userId, 'new_ip_login', 'medium', $ipAddress, 
                    '检测到新IP地址登录');
            }

            return ['allowed' => true, 'reason' => 'IP检查通过'];

        } catch (Exception $e) {
            logMessage('ERROR', 'IP检查失败', [
                'user_id' => $userId,
                'ip' => $ipAddress,
                'error' => $e->getMessage()
            ]);
            return ['allowed' => true, 'reason' => 'IP检查异常，允许登录'];
        }
    }

    /**
     * 记录IP访问（升级版 - 支持丰富的IP信息）
     */
    private function recordIPAccess($userId, $ipAddress, $userAgent) {
        $deviceInfo = $this->parseUserAgent($userAgent);

        try {
            // 使用新的IP查询服务获取详细信息
            require_once __DIR__ . '/IPQueryService.php';
            $ipService = new IPQueryService($this->conn);

            $result = $ipService->queryIP($ipAddress);

            if ($result['success']) {
                $ipData = $result['data'];

                // 检查是否需要自动阻止高风险IP
                $autoBlockHighRisk = $this->configs['ip_auto_block_high_risk'] ?? '0';
                $highRiskThreshold = intval($this->configs['ip_risk_threshold_high'] ?? '70');
                $isBlocked = 0;

                if ($autoBlockHighRisk === '1' && ($ipData['risk_score'] ?? 0) >= $highRiskThreshold) {
                    $isBlocked = 1;

                    // 记录自动阻止事件
                    $this->logSecurityEvent($userId, 'auto_block_high_risk_ip', 'high', $ipAddress,
                        "自动阻止高风险IP，风险评分: {$ipData['risk_score']}",
                        ['risk_data' => $ipData]);
                }

                $sql = "INSERT INTO ip_monitors (
                            user_id, ip_address, device_info, location, continent, country,
                            country_code, province, city, district, street, isp, latitude,
                            longitude, area_code, zip_code, time_zone, ip_int, street_history,
                            risk_score, risk_level, is_proxy, proxy_type, risk_tag,
                            api_source, is_blocked
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ON DUPLICATE KEY UPDATE
                            last_seen = CURRENT_TIMESTAMP,
                            login_count = login_count + 1,
                            device_info = VALUES(device_info),
                            location = VALUES(location),
                            continent = VALUES(continent),
                            country = VALUES(country),
                            country_code = VALUES(country_code),
                            province = VALUES(province),
                            city = VALUES(city),
                            district = VALUES(district),
                            street = VALUES(street),
                            isp = VALUES(isp),
                            latitude = VALUES(latitude),
                            longitude = VALUES(longitude),
                            area_code = VALUES(area_code),
                            zip_code = VALUES(zip_code),
                            time_zone = VALUES(time_zone),
                            ip_int = VALUES(ip_int),
                            street_history = VALUES(street_history),
                            risk_score = VALUES(risk_score),
                            risk_level = VALUES(risk_level),
                            is_proxy = VALUES(is_proxy),
                            proxy_type = VALUES(proxy_type),
                            risk_tag = VALUES(risk_tag),
                            api_source = VALUES(api_source),
                            last_updated = CURRENT_TIMESTAMP";

                $stmt = $this->conn->prepare($sql);
                $stmt->execute([
                    $userId, $ipAddress, $deviceInfo, $ipData['location'] ?? '未知位置',
                    $ipData['continent'] ?? null, $ipData['country'] ?? null, $ipData['country_code'] ?? null,
                    $ipData['province'] ?? null, $ipData['city'] ?? null, $ipData['district'] ?? null,
                    $ipData['street'] ?? null, $ipData['isp'] ?? null, $ipData['latitude'] ?? null,
                    $ipData['longitude'] ?? null, $ipData['area_code'] ?? null, $ipData['zip_code'] ?? null,
                    $ipData['time_zone'] ?? null, $ipData['ip_int'] ?? null, $ipData['street_history'] ?? null,
                    $ipData['risk_score'] ?? 0, $ipData['risk_level'] ?? 'unknown', $ipData['is_proxy'] ?? 0,
                    $ipData['proxy_type'] ?? null, $ipData['risk_tag'] ?? null, $ipData['api_source'] ?? 'unknown',
                    $isBlocked
                ]);

            } else {
                // 如果IP查询失败，使用简化版本
                $this->recordIPAccessFallback($userId, $ipAddress, $deviceInfo);
            }

        } catch (Exception $e) {
            // 如果出现异常，使用简化版本
            error_log("IP访问记录错误: " . $e->getMessage());
            $this->recordIPAccessFallback($userId, $ipAddress, $deviceInfo);
        }
    }

    /**
     * 简化版IP访问记录（备用方案）
     */
    private function recordIPAccessFallback($userId, $ipAddress, $deviceInfo) {
        $location = $this->getIPLocationSimple($ipAddress);

        $sql = "INSERT INTO ip_monitors (user_id, ip_address, device_info, location)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                last_seen = CURRENT_TIMESTAMP,
                login_count = login_count + 1,
                device_info = VALUES(device_info)";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$userId, $ipAddress, $deviceInfo, $location]);
    }

    /**
     * 简化版IP位置查询
     */
    private function getIPLocationSimple($ipAddress) {
        if (filter_var($ipAddress, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
            return '内网IP';
        }
        return '未知位置';
    }

    /**
     * 检查IP是否被阻止
     */
    private function isIPBlocked($userId, $ipAddress) {
        $sql = "SELECT is_blocked FROM ip_monitors WHERE user_id = ? AND ip_address = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$userId, $ipAddress]);
        $result = $stmt->fetch();
        
        return $result ? boolval($result['is_blocked']) : false;
    }

    /**
     * 获取用户IP数量
     */
    private function getUserIPCount($userId) {
        $windowHours = $this->configs['ip_check_window'];
        
        $sql = "SELECT COUNT(DISTINCT ip_address) 
                FROM ip_monitors 
                WHERE user_id = ? 
                AND last_seen >= DATE_SUB(NOW(), INTERVAL ? HOUR)
                AND is_blocked = 0";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$userId, $windowHours]);
        
        return intval($stmt->fetchColumn());
    }

    /**
     * 检查是否为新IP
     */
    private function isNewIP($userId, $ipAddress) {
        $sql = "SELECT COUNT(*) FROM ip_monitors WHERE user_id = ? AND ip_address = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$userId, $ipAddress]);
        
        return intval($stmt->fetchColumn()) <= 1; // 第一次记录
    }

    /**
     * 处理多IP违规
     */
    private function handleMultipleIPViolation($userId, $ipAddress, $ipCount) {
        if (!$this->configs['auto_ban_enabled']) {
            return;
        }

        try {
            $this->conn->beginTransaction();

            // 封禁用户
            $banReason = "检测到异常多IP登录行为（{$ipCount}个IP地址），系统自动封禁";
            $banSql = "UPDATE users SET status = 'banned', banned_at = NOW(), banned_reason = ?, banned_by = NULL WHERE id = ?";
            $banStmt = $this->conn->prepare($banSql);
            $banStmt->execute([$banReason, $userId]);

            // 清除用户所有会话
            $clearSql = "UPDATE user_sessions SET is_active = 0 WHERE user_id = ?";
            $clearStmt = $this->conn->prepare($clearSql);
            $clearStmt->execute([$userId]);

            // 记录安全事件
            $this->logSecurityEvent($userId, 'auto_ban_multi_ip', 'critical', $ipAddress, 
                $banReason, ['ip_count' => $ipCount]);

            // 记录操作日志
            $this->logOperation(null, $userId, 'auto_ban_user', $banReason);

            $this->conn->commit();

            logMessage('SECURITY', '自动封禁用户', [
                'user_id' => $userId,
                'ip_count' => $ipCount,
                'trigger_ip' => $ipAddress
            ]);

        } catch (Exception $e) {
            $this->conn->rollBack();
            logMessage('ERROR', '自动封禁失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 记录安全事件
     */
    public function logSecurityEvent($userId, $eventType, $severity, $ipAddress, $description, $details = null) {
        try {
            $sql = "INSERT INTO security_events (user_id, event_type, severity, ip_address, description, details) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([
                $userId,
                $eventType,
                $severity,
                $ipAddress,
                $description,
                $details ? json_encode($details) : null
            ]);
        } catch (Exception $e) {
            logMessage('ERROR', '记录安全事件失败', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 记录操作日志
     */
    private function logOperation($adminId, $userId, $action, $description) {
        try {
            $sql = "INSERT INTO operation_logs (admin_id, user_id, action, description, ip_address, user_agent) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([
                $adminId,
                $userId,
                $action,
                $description,
                getClientIP(),
                getUserAgent()
            ]);
        } catch (Exception $e) {
            logMessage('ERROR', '记录操作日志失败', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 解析User Agent
     */
    private function parseUserAgent($userAgent) {
        if (empty($userAgent)) {
            return '未知设备';
        }

        // 简单的设备识别
        $devices = [
            'iPhone' => 'iPhone',
            'iPad' => 'iPad',
            'Android' => 'Android',
            'Windows' => 'Windows',
            'Macintosh' => 'Mac',
            'Linux' => 'Linux'
        ];

        foreach ($devices as $pattern => $device) {
            if (stripos($userAgent, $pattern) !== false) {
                return $device;
            }
        }

        return '未知设备';
    }

    /**
     * 获取IP地理位置（升级版 - 集成强大的IP查询服务）
     */
    private function getIPLocation($ipAddress) {
        // 检查是否为内网IP
        if (filter_var($ipAddress, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
            return '内网IP';
        }

        try {
            // 使用新的IP查询服务
            require_once __DIR__ . '/IPQueryService.php';
            $ipService = new IPQueryService($this->conn);

            $result = $ipService->queryIP($ipAddress);

            if ($result['success']) {
                return $result['data']['location'] ?? '未知位置';
            } else {
                return '查询失败';
            }
        } catch (Exception $e) {
            // 记录错误但不影响主流程
            error_log("IP查询服务错误: " . $e->getMessage());
            return '未知位置';
        }
    }

    /**
     * 获取用户IP列表
     */
    public function getUserIPs($userId) {
        try {
            $sql = "SELECT * FROM ip_monitors WHERE user_id = ? ORDER BY last_seen DESC";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * 获取安全事件
     */
    public function getSecurityEvents($page = 1, $limit = 50, $severity = '', $eventType = '', $userId = null) {
        try {
            $offset = ($page - 1) * $limit;
            $whereConditions = [];
            $params = [];

            if (!empty($severity)) {
                $whereConditions[] = "severity = ?";
                $params[] = $severity;
            }

            if (!empty($eventType)) {
                $whereConditions[] = "event_type = ?";
                $params[] = $eventType;
            }

            if ($userId) {
                $whereConditions[] = "user_id = ?";
                $params[] = $userId;
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            $sql = "SELECT se.*, u.username 
                    FROM security_events se
                    LEFT JOIN users u ON se.user_id = u.id
                    {$whereClause}
                    ORDER BY se.created_at DESC 
                    LIMIT ? OFFSET ?";

            $params[] = $limit;
            $params[] = $offset;

            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            $events = $stmt->fetchAll();

            // 获取总数
            $countSql = "SELECT COUNT(*) FROM security_events se {$whereClause}";
            $countParams = array_slice($params, 0, -2);
            $countStmt = $this->conn->prepare($countSql);
            $countStmt->execute($countParams);
            $total = $countStmt->fetchColumn();

            return [
                'success' => true,
                'events' => $events,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ];

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 信任IP地址
     */
    public function trustIP($userId, $ipAddress, $adminId = null) {
        try {
            // 如果IP不存在，先创建记录
            $checkSql = "SELECT COUNT(*) FROM ip_monitors WHERE user_id = ? AND ip_address = ?";
            $checkStmt = $this->conn->prepare($checkSql);
            $checkStmt->execute([$userId, $ipAddress]);

            if ($checkStmt->fetchColumn() == 0) {
                // 创建新的IP记录
                $insertSql = "INSERT INTO ip_monitors (user_id, ip_address, is_trusted, device_info, location)
                             VALUES (?, ?, 1, '手动添加', '未知位置')";
                $insertStmt = $this->conn->prepare($insertSql);
                $insertStmt->execute([$userId, $ipAddress]);
            } else {
                // 更新现有记录
                $sql = "UPDATE ip_monitors SET is_trusted = 1, is_blocked = 0 WHERE user_id = ? AND ip_address = ?";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$userId, $ipAddress]);
            }

            // 记录操作日志
            if ($adminId) {
                $this->logOperation($adminId, $userId, 'trust_ip', "设置IP {$ipAddress} 为可信");
            }

            // 记录安全事件
            $this->logSecurityEvent($userId, 'ip_trusted', 'low', $ipAddress,
                "IP地址被设为可信", ['admin_id' => $adminId]);

            return ['success' => true, 'message' => 'IP地址已设为可信'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 取消信任IP地址
     */
    public function untrustIP($userId, $ipAddress, $adminId = null) {
        try {
            $sql = "UPDATE ip_monitors SET is_trusted = 0 WHERE user_id = ? AND ip_address = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId, $ipAddress]);

            if ($stmt->rowCount() > 0) {
                // 记录操作日志
                if ($adminId) {
                    $this->logOperation($adminId, $userId, 'untrust_ip', "取消IP {$ipAddress} 的可信状态");
                }

                // 记录安全事件
                $this->logSecurityEvent($userId, 'ip_untrusted', 'low', $ipAddress,
                    "IP地址可信状态被取消", ['admin_id' => $adminId]);

                return ['success' => true, 'message' => 'IP地址可信状态已取消'];
            } else {
                return ['success' => false, 'message' => 'IP地址不存在或未设为可信'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 阻止IP地址
     */
    public function blockIP($userId, $ipAddress, $adminId = null) {
        try {
            // 如果IP不存在，先创建记录
            $checkSql = "SELECT COUNT(*) FROM ip_monitors WHERE user_id = ? AND ip_address = ?";
            $checkStmt = $this->conn->prepare($checkSql);
            $checkStmt->execute([$userId, $ipAddress]);

            if ($checkStmt->fetchColumn() == 0) {
                // 创建新的IP记录
                $insertSql = "INSERT INTO ip_monitors (user_id, ip_address, is_blocked, device_info, location)
                             VALUES (?, ?, 1, '手动添加', '未知位置')";
                $insertStmt = $this->conn->prepare($insertSql);
                $insertStmt->execute([$userId, $ipAddress]);
            } else {
                // 更新现有记录
                $sql = "UPDATE ip_monitors SET is_blocked = 1, is_trusted = 0 WHERE user_id = ? AND ip_address = ?";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$userId, $ipAddress]);
            }

            // 记录操作日志
            if ($adminId) {
                $this->logOperation($adminId, $userId, 'block_ip', "阻止IP {$ipAddress}");
            }

            // 记录安全事件
            $this->logSecurityEvent($userId, 'ip_blocked', 'medium', $ipAddress,
                "IP地址被阻止", ['admin_id' => $adminId]);

            return ['success' => true, 'message' => 'IP地址已被阻止'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 解除阻止IP地址
     */
    public function unblockIP($userId, $ipAddress, $adminId = null) {
        try {
            $sql = "UPDATE ip_monitors SET is_blocked = 0 WHERE user_id = ? AND ip_address = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId, $ipAddress]);

            if ($stmt->rowCount() > 0) {
                // 记录操作日志
                if ($adminId) {
                    $this->logOperation($adminId, $userId, 'unblock_ip', "解除阻止IP {$ipAddress}");
                }

                // 记录安全事件
                $this->logSecurityEvent($userId, 'ip_unblocked', 'low', $ipAddress,
                    "IP地址阻止状态被解除", ['admin_id' => $adminId]);

                return ['success' => true, 'message' => 'IP地址阻止状态已解除'];
            } else {
                return ['success' => false, 'message' => 'IP地址不存在或未被阻止'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 获取系统配置
     */
    public function getConfigs() {
        return $this->configs;
    }

    /**
     * 更新系统配置
     */
    public function updateConfig($key, $value, $adminId = null) {
        try {
            $sql = "UPDATE system_configs SET config_value = ?, updated_by = ? WHERE config_key = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$value, $adminId, $key]);

            // 重新加载配置
            $this->loadConfigs();

            return ['success' => true, 'message' => '配置更新成功'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 清除安全事件
     */
    public function clearSecurityEvents($adminId, $eventType = '', $severity = '', $beforeDate = '') {
        try {
            $whereConditions = [];
            $params = [];

            if (!empty($eventType)) {
                $whereConditions[] = "event_type = ?";
                $params[] = $eventType;
            }

            if (!empty($severity)) {
                $whereConditions[] = "severity = ?";
                $params[] = $severity;
            }

            if (!empty($beforeDate)) {
                $whereConditions[] = "created_at < ?";
                $params[] = $beforeDate;
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // 获取要删除的事件数量
            $countSql = "SELECT COUNT(*) FROM security_events {$whereClause}";
            $countStmt = $this->conn->prepare($countSql);
            $countStmt->execute($params);
            $deleteCount = $countStmt->fetchColumn();

            if ($deleteCount == 0) {
                return ['success' => false, 'message' => '没有找到符合条件的安全事件'];
            }

            // 删除安全事件
            $deleteSql = "DELETE FROM security_events {$whereClause}";
            $deleteStmt = $this->conn->prepare($deleteSql);
            $deleteStmt->execute($params);

            // 记录操作日志
            $this->logOperation($adminId, null, 'clear_security_events',
                "清除了 {$deleteCount} 条安全事件记录");

            return ['success' => true, 'message' => "成功清除 {$deleteCount} 条安全事件记录"];

        } catch (Exception $e) {
            return ['success' => false, 'message' => '清除失败: ' . $e->getMessage()];
        }
    }

    /**
     * 标记安全事件为已处理
     */
    public function resolveSecurityEvent($eventId, $adminId) {
        try {
            $sql = "UPDATE security_events SET is_resolved = 1, resolved_by = ?, resolved_at = NOW() WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$adminId, $eventId]);

            if ($stmt->rowCount() > 0) {
                $this->logOperation($adminId, null, 'resolve_security_event',
                    "标记安全事件 #{$eventId} 为已处理");
                return ['success' => true, 'message' => '安全事件已标记为已处理'];
            } else {
                return ['success' => false, 'message' => '安全事件不存在'];
            }

        } catch (Exception $e) {
            return ['success' => false, 'message' => '操作失败: ' . $e->getMessage()];
        }
    }

    /**
     * 批量处理安全事件
     */
    public function batchResolveEvents($eventIds, $adminId) {
        try {
            if (empty($eventIds)) {
                return ['success' => false, 'message' => '请选择要处理的事件'];
            }

            $placeholders = str_repeat('?,', count($eventIds) - 1) . '?';
            $sql = "UPDATE security_events SET is_resolved = 1, resolved_by = ?, resolved_at = NOW()
                    WHERE id IN ({$placeholders})";

            $params = array_merge([$adminId], $eventIds);
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);

            $resolvedCount = $stmt->rowCount();

            $this->logOperation($adminId, null, 'batch_resolve_events',
                "批量处理了 {$resolvedCount} 个安全事件");

            return ['success' => true, 'message' => "成功处理 {$resolvedCount} 个安全事件"];

        } catch (Exception $e) {
            return ['success' => false, 'message' => '批量处理失败: ' . $e->getMessage()];
        }
    }

    /**
     * 清理过期IP记录
     */
    public function cleanupOldIPs($adminId, $daysBefore = 30) {
        try {
            $sql = "DELETE FROM ip_monitors WHERE last_seen < DATE_SUB(NOW(), INTERVAL ? DAY) AND is_trusted = 0";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$daysBefore]);

            $deletedCount = $stmt->rowCount();

            $this->logOperation($adminId, null, 'cleanup_old_ips',
                "清理了 {$deletedCount} 条过期IP记录（{$daysBefore}天前）");

            return ['success' => true, 'message' => "成功清理 {$deletedCount} 条过期IP记录"];

        } catch (Exception $e) {
            return ['success' => false, 'message' => '清理失败: ' . $e->getMessage()];
        }
    }

    /**
     * 重置用户IP记录
     */
    public function resetUserIPs($userId, $adminId) {
        try {
            // 删除用户所有IP记录（除了可信IP）
            $sql = "DELETE FROM ip_monitors WHERE user_id = ? AND is_trusted = 0";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);

            $deletedCount = $stmt->rowCount();

            // 记录安全事件
            $this->logSecurityEvent($userId, 'ip_reset', 'medium', getClientIP(),
                "管理员重置了用户IP记录");

            $this->logOperation($adminId, $userId, 'reset_user_ips',
                "重置了用户IP记录，删除 {$deletedCount} 条记录");

            return ['success' => true, 'message' => "成功重置用户IP记录"];

        } catch (Exception $e) {
            return ['success' => false, 'message' => '重置失败: ' . $e->getMessage()];
        }
    }
}
?>
