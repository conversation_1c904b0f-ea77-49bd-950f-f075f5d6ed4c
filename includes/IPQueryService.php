<?php
/**
 * 强大的IP查询服务类
 * 集成api.ip77.net等多个IP查询接口
 * 提供地理位置、风险评估、代理检测等功能
 * 
 * <AUTHOR> 💖
 * @version 1.0.0
 */

class IPQueryService {
    private $conn;
    private $configs;
    private $apiEndpoints;
    
    public function __construct($database) {
        $this->conn = $database;
        $this->loadConfigs();
        $this->initApiEndpoints();
    }
    
    /**
     * 加载系统配置
     */
    private function loadConfigs() {
        $sql = "SELECT config_key, config_value FROM system_configs 
                WHERE config_key LIKE 'ip_%'";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        
        $this->configs = [];
        while ($row = $stmt->fetch()) {
            $this->configs[$row['config_key']] = $row['config_value'];
        }
        
        // 设置默认值
        $defaults = [
            'ip_api_enabled' => '1',
            'ip_api_primary' => 'api.ip77.net',
            'ip_api_cache_days' => '7',
            'ip_risk_threshold_medium' => '40',
            'ip_risk_threshold_high' => '70',
            'ip_auto_block_high_risk' => '0',
            'ip_proxy_detection_enabled' => '1'
        ];
        
        foreach ($defaults as $key => $value) {
            if (!isset($this->configs[$key])) {
                $this->configs[$key] = $value;
            }
        }
    }
    
    /**
     * 初始化API端点配置
     */
    private function initApiEndpoints() {
        $this->apiEndpoints = [
            'api.ip77.net' => [
                'url' => 'https://api.ip77.net/ip2/v4/',
                'method' => 'POST',
                'format' => 'form',
                'timeout' => 10,
                'priority' => 1
            ],
            'ipinfo.io' => [
                'url' => 'https://ipinfo.io/{ip}/json',
                'method' => 'GET',
                'format' => 'json',
                'timeout' => 8,
                'priority' => 2
            ],
            'ip-api.com' => [
                'url' => 'http://ip-api.com/json/{ip}?lang=zh-CN',
                'method' => 'GET',
                'format' => 'json',
                'timeout' => 8,
                'priority' => 3
            ],
            'qq.com' => [
                'url' => 'https://r.inews.qq.com/api/ip2city',
                'method' => 'GET',
                'format' => 'json',
                'timeout' => 5,
                'priority' => 4
            ]
        ];
    }
    
    /**
     * 查询IP信息（主入口）
     */
    public function queryIP($ipAddress, $forceRefresh = false) {
        // 验证IP地址
        if (!filter_var($ipAddress, FILTER_VALIDATE_IP)) {
            return $this->createErrorResponse('无效的IP地址');
        }
        
        // 检查是否为内网IP
        if (filter_var($ipAddress, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
            return $this->createInternalIPResponse($ipAddress);
        }
        
        // 检查缓存
        if (!$forceRefresh) {
            $cached = $this->getCachedResult($ipAddress);
            if ($cached) {
                return $cached;
            }
        }
        
        // 调用API查询
        $result = $this->queryFromAPI($ipAddress);
        
        // 缓存结果
        if ($result['success']) {
            $this->cacheResult($ipAddress, $result['data']);
        }
        
        return $result;
    }
    
    /**
     * 从API查询IP信息
     */
    private function queryFromAPI($ipAddress) {
        $primaryApi = $this->configs['ip_api_primary'];
        
        // 优先使用主要API
        if (isset($this->apiEndpoints[$primaryApi])) {
            $result = $this->callAPI($primaryApi, $ipAddress);
            if ($result['success']) {
                return $result;
            }
        }
        
        // 备用API轮询
        $sortedApis = $this->apiEndpoints;
        uasort($sortedApis, function($a, $b) {
            return $a['priority'] - $b['priority'];
        });
        
        foreach ($sortedApis as $apiName => $config) {
            if ($apiName === $primaryApi) continue; // 跳过已尝试的主API
            
            $result = $this->callAPI($apiName, $ipAddress);
            if ($result['success']) {
                return $result;
            }
        }
        
        return $this->createErrorResponse('所有API查询失败');
    }
    
    /**
     * 调用指定API
     */
    private function callAPI($apiName, $ipAddress) {
        $config = $this->apiEndpoints[$apiName];
        $startTime = microtime(true);
        
        try {
            $url = str_replace('{ip}', $ipAddress, $config['url']);
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => $config['timeout'],
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]);
            
            // 设置请求方法和数据
            if ($config['method'] === 'POST') {
                curl_setopt($ch, CURLOPT_POST, true);
                if ($config['format'] === 'form') {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, "ip=" . $ipAddress);
                }
            }
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $responseTime = round((microtime(true) - $startTime) * 1000);
            
            curl_close($ch);
            
            // 记录API调用日志
            $this->logAPICall($ipAddress, $apiName, $url, $response, $httpCode, $responseTime, $httpCode === 200);
            
            if ($httpCode !== 200 || !$response) {
                throw new Exception("HTTP错误: {$httpCode}");
            }
            
            $data = json_decode($response, true);
            if (!$data) {
                throw new Exception("JSON解析失败");
            }
            
            // 根据不同API格式化数据
            $formatted = $this->formatAPIResponse($apiName, $data);
            
            return [
                'success' => true,
                'data' => $formatted,
                'api_source' => $apiName,
                'response_time' => $responseTime
            ];
            
        } catch (Exception $e) {
            $this->logAPICall($ipAddress, $apiName, $url ?? '', '', 0, 0, false, $e->getMessage());
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'api_source' => $apiName
            ];
        }
    }
    
    /**
     * 格式化不同API的响应数据
     */
    private function formatAPIResponse($apiName, $data) {
        switch ($apiName) {
            case 'api.ip77.net':
                return $this->formatIP77Response($data);
            case 'ipinfo.io':
                return $this->formatIPInfoResponse($data);
            case 'ip-api.com':
                return $this->formatIPAPIResponse($data);
            case 'qq.com':
                return $this->formatQQResponse($data);
            default:
                return $data;
        }
    }
    
    /**
     * 格式化api.ip77.net响应
     */
    private function formatIP77Response($data) {
        if ($data['code'] !== 0) {
            throw new Exception($data['error'] ?? 'API返回错误');
        }
        
        $ipData = $data['data'];
        
        return [
            'ip_address' => $ipData['ip'],
            'ip_int' => $ipData['ip_int'],
            'location' => $ipData['location'],
            'continent' => $ipData['continent'],
            'country' => $ipData['country'],
            'country_code' => $ipData['country_code'],
            'province' => $ipData['province'],
            'city' => $ipData['city'],
            'district' => $ipData['district'],
            'street' => $ipData['street'],
            'isp' => $ipData['isp'],
            'latitude' => floatval($ipData['latitude']),
            'longitude' => floatval($ipData['longitude']),
            'area_code' => $ipData['area_code'],
            'zip_code' => $ipData['zip_code'],
            'time_zone' => $ipData['time_zone'],
            'street_history' => json_encode($ipData['street_history']),
            'risk_score' => intval($ipData['risk']['risk_score']),
            'risk_level' => $ipData['risk']['risk_level'],
            'is_proxy' => $ipData['risk']['is_proxy'] === '是' ? 1 : 0,
            'proxy_type' => $ipData['risk']['proxy_type'],
            'risk_tag' => $ipData['risk']['risk_tag'],
            'api_source' => 'api.ip77.net'
        ];
    }
    
    /**
     * 格式化ipinfo.io响应
     */
    private function formatIPInfoResponse($data) {
        $coords = explode(',', $data['loc'] ?? '0,0');
        
        return [
            'ip_address' => $data['ip'],
            'location' => ($data['city'] ?? '') . ' ' . ($data['region'] ?? '') . ' ' . ($data['country'] ?? ''),
            'country' => $data['country'] ?? '',
            'city' => $data['city'] ?? '',
            'province' => $data['region'] ?? '',
            'isp' => $data['org'] ?? '',
            'latitude' => floatval($coords[0] ?? 0),
            'longitude' => floatval($coords[1] ?? 0),
            'zip_code' => $data['postal'] ?? '',
            'time_zone' => $data['timezone'] ?? '',
            'risk_score' => 0,
            'risk_level' => 'unknown',
            'is_proxy' => 0,
            'api_source' => 'ipinfo.io'
        ];
    }
    
    /**
     * 创建内网IP响应
     */
    private function createInternalIPResponse($ipAddress) {
        return [
            'success' => true,
            'data' => [
                'ip_address' => $ipAddress,
                'location' => '内网IP',
                'country' => '内网',
                'risk_score' => 0,
                'risk_level' => '无风险',
                'is_proxy' => 0,
                'api_source' => 'internal'
            ]
        ];
    }
    
    /**
     * 创建错误响应
     */
    private function createErrorResponse($message) {
        return [
            'success' => false,
            'error' => $message
        ];
    }

    /**
     * 获取缓存结果
     */
    private function getCachedResult($ipAddress) {
        $sql = "SELECT * FROM ip_query_cache
                WHERE ip_address = ? AND expires_at > NOW()";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$ipAddress]);
        $cached = $stmt->fetch();

        if ($cached) {
            // 更新查询次数
            $updateSql = "UPDATE ip_query_cache SET query_count = query_count + 1 WHERE id = ?";
            $updateStmt = $this->conn->prepare($updateSql);
            $updateStmt->execute([$cached['id']]);

            return [
                'success' => true,
                'data' => $this->arrayFromCache($cached),
                'cached' => true
            ];
        }

        return null;
    }

    /**
     * 缓存查询结果
     */
    private function cacheResult($ipAddress, $data) {
        $expiresDays = intval($this->configs['ip_api_cache_days']);

        $sql = "INSERT INTO ip_query_cache (
                    ip_address, ip_int, continent, country, country_code, province, city,
                    district, street, isp, latitude, longitude, area_code, zip_code,
                    time_zone, location, street_history, risk_score, risk_level,
                    is_proxy, proxy_type, risk_tag, api_source, expires_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL ? DAY))
                ON DUPLICATE KEY UPDATE
                    ip_int = VALUES(ip_int),
                    continent = VALUES(continent),
                    country = VALUES(country),
                    country_code = VALUES(country_code),
                    province = VALUES(province),
                    city = VALUES(city),
                    district = VALUES(district),
                    street = VALUES(street),
                    isp = VALUES(isp),
                    latitude = VALUES(latitude),
                    longitude = VALUES(longitude),
                    area_code = VALUES(area_code),
                    zip_code = VALUES(zip_code),
                    time_zone = VALUES(time_zone),
                    location = VALUES(location),
                    street_history = VALUES(street_history),
                    risk_score = VALUES(risk_score),
                    risk_level = VALUES(risk_level),
                    is_proxy = VALUES(is_proxy),
                    proxy_type = VALUES(proxy_type),
                    risk_tag = VALUES(risk_tag),
                    api_source = VALUES(api_source),
                    query_count = query_count + 1,
                    updated_at = CURRENT_TIMESTAMP,
                    expires_at = VALUES(expires_at)";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([
            $data['ip_address'], $data['ip_int'] ?? null, $data['continent'] ?? null,
            $data['country'] ?? null, $data['country_code'] ?? null, $data['province'] ?? null,
            $data['city'] ?? null, $data['district'] ?? null, $data['street'] ?? null,
            $data['isp'] ?? null, $data['latitude'] ?? null, $data['longitude'] ?? null,
            $data['area_code'] ?? null, $data['zip_code'] ?? null, $data['time_zone'] ?? null,
            $data['location'] ?? null, $data['street_history'] ?? null, $data['risk_score'] ?? 0,
            $data['risk_level'] ?? 'unknown', $data['is_proxy'] ?? 0, $data['proxy_type'] ?? null,
            $data['risk_tag'] ?? null, $data['api_source'] ?? 'unknown', $expiresDays
        ]);
    }

    /**
     * 从缓存数组转换为标准格式
     */
    private function arrayFromCache($cached) {
        return [
            'ip_address' => $cached['ip_address'],
            'ip_int' => $cached['ip_int'],
            'location' => $cached['location'],
            'continent' => $cached['continent'],
            'country' => $cached['country'],
            'country_code' => $cached['country_code'],
            'province' => $cached['province'],
            'city' => $cached['city'],
            'district' => $cached['district'],
            'street' => $cached['street'],
            'isp' => $cached['isp'],
            'latitude' => $cached['latitude'],
            'longitude' => $cached['longitude'],
            'area_code' => $cached['area_code'],
            'zip_code' => $cached['zip_code'],
            'time_zone' => $cached['time_zone'],
            'street_history' => $cached['street_history'],
            'risk_score' => $cached['risk_score'],
            'risk_level' => $cached['risk_level'],
            'is_proxy' => $cached['is_proxy'],
            'proxy_type' => $cached['proxy_type'],
            'risk_tag' => $cached['risk_tag'],
            'api_source' => $cached['api_source']
        ];
    }

    /**
     * 记录API调用日志
     */
    private function logAPICall($ipAddress, $apiName, $url, $response, $httpCode, $responseTime, $success, $error = null) {
        $sql = "INSERT INTO ip_api_logs (
                    ip_address, api_endpoint, request_data, response_data,
                    response_code, response_time_ms, success, error_message
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([
            $ipAddress,
            $apiName,
            $url,
            substr($response, 0, 1000), // 限制响应数据长度
            $httpCode,
            $responseTime,
            $success ? 1 : 0,
            $error
        ]);
    }

    /**
     * 批量查询多个IP
     */
    public function batchQueryIPs($ipAddresses, $forceRefresh = false) {
        $results = [];

        foreach ($ipAddresses as $ip) {
            $results[$ip] = $this->queryIP($ip, $forceRefresh);

            // 避免API调用过于频繁
            if (!$results[$ip]['cached'] ?? false) {
                usleep(200000); // 200ms延迟
            }
        }

        return $results;
    }

    /**
     * 获取风险统计
     */
    public function getRiskStatistics($days = 7) {
        $sql = "SELECT
                    DATE(last_seen) as date,
                    COUNT(*) as total_ips,
                    SUM(CASE WHEN risk_score < ? THEN 1 ELSE 0 END) as low_risk,
                    SUM(CASE WHEN risk_score >= ? AND risk_score < ? THEN 1 ELSE 0 END) as medium_risk,
                    SUM(CASE WHEN risk_score >= ? THEN 1 ELSE 0 END) as high_risk,
                    SUM(CASE WHEN is_proxy = 1 THEN 1 ELSE 0 END) as proxy_count
                FROM ip_monitors
                WHERE last_seen >= DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY DATE(last_seen)
                ORDER BY date DESC";

        $mediumThreshold = intval($this->configs['ip_risk_threshold_medium']);
        $highThreshold = intval($this->configs['ip_risk_threshold_high']);

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$mediumThreshold, $mediumThreshold, $highThreshold, $highThreshold, $days]);

        return $stmt->fetchAll();
    }

    /**
     * 清理过期缓存
     */
    public function cleanExpiredCache() {
        $sql = "DELETE FROM ip_query_cache WHERE expires_at < NOW()";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute();

        return $stmt->rowCount();
    }
}
