<?php
/**
 * JWT助手类
 * 简化的JWT处理
 */

class JWTHelper {
    private $secret_key;
    private $conn;

    public function __construct() {
        $this->secret_key = 'djxs_management_secret_key_2024';

        // 获取数据库连接
        require_once __DIR__ . '/../api/config/database.php';
        $database = new Database();
        $this->conn = $database->getConnection();
    }
    
    /**
     * 验证Token
     */
    public function validateToken($token) {
        try {
            // 分解Token
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return false;
            }

            $header = $parts[0];
            $payload = $parts[1];
            $signature = $parts[2];

            // 验证签名
            $expected_signature = base64_encode(hash_hmac('sha256', $header . '.' . $payload, $this->secret_key, true));
            if (!hash_equals($signature, $expected_signature)) {
                error_log("JWT签名验证失败 - 期望: $expected_signature, 实际: $signature");
                return false;
            }

            // 解码payload
            $payload_data = json_decode(base64_decode($payload), true);
            if (!$payload_data) {
                return false;
            }

            // 检查过期时间
            if (isset($payload_data['exp']) && $payload_data['exp'] < time()) {
                return false;
            }

            // 检查必需字段
            if (!isset($payload_data['user_id'])) {
                return false;
            }

            // 检查用户是否还存在于数据库中
            if (!$this->checkUserExists($payload_data['user_id'])) {
                error_log('JWT验证失败: 用户不存在或已被删除 - User ID: ' . $payload_data['user_id']);
                return false;
            }

            return $payload_data;
        } catch (Exception $e) {
            error_log('JWT验证失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 生成Token
     */
    public function generateToken($payload) {
        try {
            // 添加标准JWT字段
            $payload['iat'] = time(); // 签发时间
            $payload['exp'] = time() + (24 * 60 * 60); // 24小时过期

            // 确保有user_id
            if (!isset($payload['user_id'])) {
                throw new Exception('缺少user_id字段');
            }

            $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
            $payload_json = json_encode($payload);

            $base64_header = base64_encode($header);
            $base64_payload = base64_encode($payload_json);

            $signature = hash_hmac('sha256', $base64_header . '.' . $base64_payload, $this->secret_key, true);
            $base64_signature = base64_encode($signature);

            return $base64_header . '.' . $base64_payload . '.' . $base64_signature;
        } catch (Exception $e) {
            error_log('JWT生成失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查用户是否存在且状态正常
     */
    private function checkUserExists($userId) {
        try {
            $sql = "SELECT id, status FROM users WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);
            $user = $stmt->fetch();

            if (!$user) {
                return false; // 用户不存在
            }

            // 检查用户状态
            if ($user['status'] === 'banned') {
                error_log('JWT验证失败: 用户已被封禁 - User ID: ' . $userId);
                return false; // 用户被封禁
            }

            return true; // 用户存在且状态正常
        } catch (Exception $e) {
            error_log('检查用户存在性失败: ' . $e->getMessage());
            return false;
        }
    }
}
?>
