<?php
/**
 * 认证类
 * 管理系统 - management.djxs.xyz
 */

require_once __DIR__ . '/../api/config/config.php';
require_once __DIR__ . '/../api/config/database.php';
require_once __DIR__ . '/security.php';

class Auth {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }

    /**
     * 用户注册（提交到待审批表）
     */
    public function register($username, $password, $email, $phone = '', $realName = '', $reason = '', $verificationCode = '') {
        try {
            // 验证输入
            if (empty($username) || empty($password) || empty($email)) {
                throw new Exception('用户名、密码和邮箱不能为空');
            }

            if (strlen($username) < 3 || strlen($username) > 20) {
                throw new Exception('用户名长度必须在3-20个字符之间');
            }

            if (strlen($password) < 6) {
                throw new Exception('密码长度不能少于6个字符');
            }

            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('邮箱格式不正确');
            }

            // 验证邮箱验证码
            if (!empty($verificationCode)) {
                $verifyResult = $this->verifyEmailCode($email, $verificationCode, 'register');
                if (!$verifyResult['success']) {
                    throw new Exception($verifyResult['message']);
                }
            } else {
                throw new Exception('请先获取邮箱验证码');
            }

            // 检查用户名是否已存在（只检查正式用户和待审批的申请）
            $checkSql = "SELECT COUNT(*) FROM pending_users WHERE username = ? AND status = 'pending'
                        UNION ALL
                        SELECT COUNT(*) FROM users WHERE username = ?";
            $checkStmt = $this->conn->prepare($checkSql);
            $checkStmt->execute([$username, $username]);
            $results = $checkStmt->fetchAll(PDO::FETCH_COLUMN);

            if (array_sum($results) > 0) {
                throw new Exception('用户名已存在');
            }

            // 检查邮箱是否已存在（只检查正式用户和待审批的申请）
            $checkEmailSql = "SELECT COUNT(*) FROM pending_users WHERE email = ? AND status = 'pending'
                             UNION ALL
                             SELECT COUNT(*) FROM users WHERE email = ?";
            $checkEmailStmt = $this->conn->prepare($checkEmailSql);
            $checkEmailStmt->execute([$email, $email]);
            $emailResults = $checkEmailStmt->fetchAll(PDO::FETCH_COLUMN);

            if (array_sum($emailResults) > 0) {
                throw new Exception('邮箱已存在');
            }

            // 插入到待审批表
            $sql = "INSERT INTO pending_users (username, password, email, phone, real_name, reason, ip_address, user_agent) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->conn->prepare($sql);
            $hashedPassword = hashPassword($password);
            $ipAddress = getClientIP();
            $userAgent = getUserAgent();
            
            $result = $stmt->execute([
                $username,
                $hashedPassword,
                $email,
                $phone,
                $realName,
                $reason,
                $ipAddress,
                $userAgent
            ]);

            if ($result) {
                logMessage('INFO', '用户注册申请提交', [
                    'username' => $username,
                    'email' => $email,
                    'ip' => $ipAddress
                ]);
                return ['success' => true, 'message' => '注册申请已提交，请等待管理员审批'];
            } else {
                throw new Exception('注册申请提交失败');
            }

        } catch (Exception $e) {
            logMessage('ERROR', '用户注册失败', [
                'username' => $username ?? '',
                'email' => $email ?? '',
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 发送邮箱验证码
     */
    public function sendEmailVerification($email, $type = 'register') {
        try {
            $url = 'https://yxyz.66ds.de/api/send-verification.php';
            $data = json_encode([
                'email' => $email,
                'type' => $type
            ]);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($data)
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                throw new Exception('网络请求失败: ' . $error);
            }

            if ($httpCode !== 200) {
                throw new Exception('验证码发送失败，HTTP状态码: ' . $httpCode);
            }

            // 处理响应中可能包含的PHP警告信息
            $jsonStart = strpos($response, '{"success"');
            if ($jsonStart !== false) {
                $jsonResponse = substr($response, $jsonStart);
                $result = json_decode($jsonResponse, true);
            } else {
                $result = json_decode($response, true);
            }

            if (json_last_error() !== JSON_ERROR_NONE) {
                // 如果JSON解析失败，但响应包含success:true，认为发送成功
                if (strpos($response, '"success":true') !== false) {
                    return ['success' => true, 'message' => '验证码已发送'];
                }
                throw new Exception('响应数据格式错误');
            }

            return $result;

        } catch (Exception $e) {
            logMessage('ERROR', '发送邮箱验证码失败', [
                'email' => $email,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 验证邮箱验证码
     */
    public function verifyEmailCode($email, $code, $type = 'register') {
        try {
            $url = 'https://yxyz.66ds.de/api/verify-code.php';
            $data = json_encode([
                'email' => $email,
                'code' => $code,
                'type' => $type
            ]);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($data)
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                throw new Exception('网络请求失败: ' . $error);
            }

            if ($httpCode !== 200) {
                throw new Exception('验证码验证失败，HTTP状态码: ' . $httpCode);
            }

            // 处理响应中可能包含的PHP警告信息
            $jsonStart = strpos($response, '{"success"');
            if ($jsonStart !== false) {
                $jsonResponse = substr($response, $jsonStart);
                $result = json_decode($jsonResponse, true);
            } else {
                $result = json_decode($response, true);
            }

            if (json_last_error() !== JSON_ERROR_NONE) {
                // 如果JSON解析失败，但响应包含success:true，认为验证成功
                if (strpos($response, '"success":true') !== false) {
                    return ['success' => true, 'message' => '验证码验证成功'];
                }
                throw new Exception('响应数据格式错误');
            }

            return $result;

        } catch (Exception $e) {
            logMessage('ERROR', '验证邮箱验证码失败', [
                'email' => $email,
                'code' => $code,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 用户登录
     */
    public function login($username, $password) {
        try {
            if (empty($username) || empty($password)) {
                throw new Exception('用户名和密码不能为空');
            }

            // 查询用户信息
            $sql = "SELECT * FROM users WHERE username = ? AND status = 'active'";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$username]);
            $user = $stmt->fetch();

            if (!$user) {
                throw new Exception('用户不存在或已被禁用');
            }

            // 验证密码
            if (!verifyPassword($password, $user['password'])) {
                throw new Exception('密码错误');
            }

            // IP安全检查
            $security = new SecurityManager();
            $ipCheck = $security->checkUserIP($user['id'], getClientIP(), getUserAgent());

            if (!$ipCheck['allowed']) {
                // 记录安全事件
                $security->logSecurityEvent(
                    $user['id'],
                    'login_blocked',
                    'high',
                    getClientIP(),
                    '登录被IP安全策略阻止: ' . $ipCheck['reason']
                );

                logMessage('SECURITY', '用户登录被IP策略阻止', [
                    'user_id' => $user['id'],
                    'username' => $username,
                    'ip' => getClientIP(),
                    'reason' => $ipCheck['reason']
                ]);

                throw new Exception($ipCheck['reason']);
            }

            // 生成session token
            $sessionToken = $this->generateUserToken($user['id']);

            // 生成JWT token
            require_once __DIR__ . '/jwt_helper.php';
            $jwtHelper = new JWTHelper();
            $jwtToken = $jwtHelper->generateToken([
                'user_id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email']
            ]);

            // 更新登录信息
            $updateSql = "UPDATE users SET last_login = NOW(), last_ip = ?, login_count = login_count + 1 WHERE id = ?";
            $updateStmt = $this->conn->prepare($updateSql);
            $updateStmt->execute([getClientIP(), $user['id']]);

            // 记录成功登录事件
            $security->logSecurityEvent(
                $user['id'],
                'successful_login',
                'low',
                getClientIP(),
                '用户成功登录'
            );

            logMessage('INFO', '用户登录成功', [
                'user_id' => $user['id'],
                'username' => $username,
                'ip' => getClientIP()
            ]);

            return [
                'success' => true,
                'message' => '登录成功',
                'token' => $jwtToken, // 返回JWT token
                'session_token' => $sessionToken, // 保留session token用于其他用途
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'real_name' => $user['real_name'],
                    'last_login' => $user['last_login']
                ]
            ];

        } catch (Exception $e) {
            logMessage('ERROR', '用户登录失败', [
                'username' => $username,
                'ip' => getClientIP(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 验证token
     */
    public function verifyToken($token) {
        try {
            if (empty($token)) {
                throw new Exception('Token不能为空');
            }

            $sql = "SELECT s.*, u.username, u.email, u.status as user_status
                    FROM user_sessions s
                    JOIN users u ON s.user_id = u.id
                    WHERE s.token = ? AND s.is_active = 1 AND s.expires_at > NOW()";

            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$token]);
            $session = $stmt->fetch();

            if (!$session) {
                throw new Exception('Token无效或已过期');
            }

            // 检查用户状态
            if ($session['user_status'] !== 'active') {
                // 如果用户被封禁或删除，清除session
                $clearSql = "UPDATE user_sessions SET is_active = 0 WHERE user_id = ?";
                $clearStmt = $this->conn->prepare($clearSql);
                $clearStmt->execute([$session['user_id']]);

                throw new Exception('用户账号已被禁用或删除');
            }

            // 更新最后活动时间
            $updateSql = "UPDATE user_sessions SET last_activity = NOW() WHERE id = ?";
            $updateStmt = $this->conn->prepare($updateSql);
            $updateStmt->execute([$session['id']]);

            return [
                'success' => true,
                'user' => [
                    'id' => $session['user_id'],
                    'username' => $session['username'],
                    'email' => $session['email']
                ]
            ];

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 生成用户token
     */
    private function generateUserToken($userId) {
        // 清理过期的token
        $cleanSql = "DELETE FROM user_sessions WHERE expires_at < NOW() OR user_id = ?";
        $cleanStmt = $this->conn->prepare($cleanSql);
        $cleanStmt->execute([$userId]);

        // 生成新token
        $token = generateToken(64);
        $expiresAt = date('Y-m-d H:i:s', time() + TOKEN_EXPIRE_TIME);
        
        $sql = "INSERT INTO user_sessions (user_id, token, ip_address, user_agent, expires_at) VALUES (?, ?, ?, ?, ?)";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([
            $userId,
            $token,
            getClientIP(),
            getUserAgent(),
            $expiresAt
        ]);

        return $token;
    }

    /**
     * 登出
     */
    public function logout($token) {
        try {
            $sql = "UPDATE user_sessions SET is_active = 0 WHERE token = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$token]);
            
            return ['success' => true, 'message' => '登出成功'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => '登出失败'];
        }
    }
}
?>
