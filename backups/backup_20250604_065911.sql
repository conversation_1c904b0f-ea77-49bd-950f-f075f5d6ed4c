/*M!999999\- enable the sandbox mode */ 
-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.11.13-MariaDB, for debian-linux-gnu (x86_64)
--
-- Host: localhost    Database: management
-- ------------------------------------------------------
-- Server version	5.7.44-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admin_users`
--

DROP TABLE IF EXISTS `admin_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1:启用 0:禁用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='管理员用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_users`
--

LOCK TABLES `admin_users` WRITE;
/*!40000 ALTER TABLE `admin_users` DISABLE KEYS */;
INSERT INTO `admin_users` VALUES
(1,'admin','$2y$10$0lN/3v.dV8jyzEgtdez/peoeLakO8eMvysSUgJSt2doUK0SPhUF1S','<EMAIL>','2025-06-03 17:22:22','2025-06-04 06:06:49',1);
/*!40000 ALTER TABLE `admin_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ip_monitors`
--

DROP TABLE IF EXISTS `ip_monitors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `ip_monitors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `first_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `login_count` int(11) NOT NULL DEFAULT '1',
  `is_trusted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为可信IP',
  `is_blocked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被阻止',
  `location` varchar(100) DEFAULT NULL COMMENT 'IP地理位置',
  `device_info` text COMMENT '设备信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_ip` (`user_id`,`ip_address`),
  KEY `user_id` (`user_id`),
  KEY `ip_address` (`ip_address`),
  KEY `is_trusted` (`is_trusted`),
  KEY `is_blocked` (`is_blocked`),
  CONSTRAINT `ip_monitors_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COMMENT='IP监控表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ip_monitors`
--

LOCK TABLES `ip_monitors` WRITE;
/*!40000 ALTER TABLE `ip_monitors` DISABLE KEYS */;
INSERT INTO `ip_monitors` VALUES
(1,1,'*************','2025-06-03 18:37:08','2025-06-03 18:37:22',12,1,1,'内网IP','未知设备'),
(3,1,'*********','2025-06-03 18:37:08','2025-06-03 19:00:30',6,0,1,'内网IP','未知设备'),
(4,1,'***********','2025-06-03 18:37:08','2025-06-03 18:37:22',6,0,0,'内网IP','未知设备'),
(5,1,'************','2025-06-03 18:37:08','2025-06-03 18:37:22',6,0,0,'未知位置','未知设备'),
(6,1,'*******','2025-06-03 18:37:08','2025-06-03 18:37:22',6,0,0,'未知位置','未知设备'),
(37,1,'************','2025-06-03 18:59:43','2025-06-03 18:59:43',1,0,0,'未知位置','未知设备'),
(38,1,'*************','2025-06-03 19:02:31','2025-06-04 04:49:51',2,0,0,'未知位置','未知设备');
/*!40000 ALTER TABLE `ip_monitors` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `operation_logs`
--

DROP TABLE IF EXISTS `operation_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `operation_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `description` text COMMENT '操作描述',
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`),
  KEY `user_id` (`user_id`),
  KEY `action` (`action`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `operation_logs`
--

LOCK TABLES `operation_logs` WRITE;
/*!40000 ALTER TABLE `operation_logs` DISABLE KEYS */;
INSERT INTO `operation_logs` VALUES
(1,1,1,'approve_user','审批通过用户注册: xiyewuqiu','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 17:48:39'),
(2,1,1,'ban_user','封禁用户，原因: 6','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 17:49:28'),
(3,1,1,'unban_user','解封用户','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:02:31'),
(4,1,NULL,'reject_user','拒绝用户注册申请 ID: 2','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:21:25'),
(5,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:08'),
(6,NULL,1,'auto_ban_user','检测到异常多IP登录行为（5个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:08'),
(7,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:19'),
(8,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:19'),
(9,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:19'),
(10,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:19'),
(11,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:21'),
(12,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:21'),
(13,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:21'),
(14,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:21'),
(15,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:22'),
(16,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:22'),
(17,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:22'),
(18,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:22'),
(19,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:22'),
(20,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:22'),
(21,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:22'),
(22,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:22'),
(23,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:22'),
(24,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:22'),
(25,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:22'),
(26,NULL,1,'auto_ban_user','检测到异常多IP登录行为（4个IP地址），系统自动封禁','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:37:22'),
(27,1,1,'unban_user','解封用户','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:38:09'),
(28,1,NULL,'clear_security_events','清除了 42 条安全事件记录','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:52:24'),
(29,1,NULL,'cleanup_old_ips','清理了 0 条过期IP记录（30天前）','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:52:29'),
(30,NULL,1,'auto_ban_user','检测到异常多IP登录行为（5个IP地址），系统自动封禁','************','AuthClient-Python/1.0.0','2025-06-03 18:59:43'),
(31,1,1,'block_ip','阻止IP *********','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 19:00:30'),
(32,1,1,'unban_user','解封用户','*************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 19:02:26'),
(33,NULL,1,'auto_ban_user','检测到异常多IP登录行为（5个IP地址），系统自动封禁','*************','AuthClient-Python/1.0.0','2025-06-03 19:02:31'),
(34,1,1,'unban_user','解封用户','*************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 19:10:09'),
(35,NULL,1,'auto_ban_user','检测到异常多IP登录行为（5个IP地址），系统自动封禁','*************','AuthClient-Python/1.0.0','2025-06-04 04:49:51');
/*!40000 ALTER TABLE `operation_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pending_users`
--

DROP TABLE IF EXISTS `pending_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `pending_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `reason` text COMMENT '申请理由',
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
  `admin_id` int(11) DEFAULT NULL COMMENT '审批管理员ID',
  `admin_note` text COMMENT '管理员备注',
  `processed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='待审批用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pending_users`
--

LOCK TABLES `pending_users` WRITE;
/*!40000 ALTER TABLE `pending_users` DISABLE KEYS */;
INSERT INTO `pending_users` VALUES
(1,'xiyewuqiu','$2y$10$C0SpXAD8TR6qdH5mw.hEOOgEf0Qv1pzEMVOlEygufcwNR7rZksY1a','<EMAIL>','','','1234567899990-','************','AuthClient-Python/1.0.0','2025-06-03 17:48:17','approved',1,'','2025-06-03 17:48:39'),
(2,'test_user','$2y$10$dk2L2iRhjgqlLKBvXrtRpOkSXRnPGfaTy/INGvMUVBeIzVpY9MetC','<EMAIL>','','','','************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','2025-06-03 18:20:05','rejected',1,'','2025-06-03 18:21:25');
/*!40000 ALTER TABLE `pending_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `security_events`
--

DROP TABLE IF EXISTS `security_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `security_events` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `event_type` varchar(50) NOT NULL COMMENT '事件类型',
  `severity` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium',
  `ip_address` varchar(45) DEFAULT NULL,
  `description` text NOT NULL,
  `details` json DEFAULT NULL COMMENT '事件详细信息',
  `is_resolved` tinyint(1) NOT NULL DEFAULT '0',
  `resolved_by` int(11) DEFAULT NULL COMMENT '处理管理员ID',
  `resolved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `event_type` (`event_type`),
  KEY `severity` (`severity`),
  KEY `ip_address` (`ip_address`),
  KEY `is_resolved` (`is_resolved`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8mb4 COMMENT='安全事件表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `security_events`
--

LOCK TABLES `security_events` WRITE;
/*!40000 ALTER TABLE `security_events` DISABLE KEYS */;
INSERT INTO `security_events` VALUES
(43,1,'auto_ban_multi_ip','critical','************','检测到异常多IP登录行为（5个IP地址），系统自动封禁','{\"ip_count\": 5}',0,NULL,NULL,'2025-06-03 18:59:43'),
(44,1,'login_blocked','high','************','登录被IP安全策略阻止: 检测到异常多IP登录，账户已被保护性封禁',NULL,0,NULL,NULL,'2025-06-03 18:59:43'),
(45,1,'ip_blocked','medium','*********','IP地址被阻止','{\"admin_id\": 1}',0,NULL,NULL,'2025-06-03 19:00:30'),
(46,1,'auto_ban_multi_ip','critical','*************','检测到异常多IP登录行为（5个IP地址），系统自动封禁','{\"ip_count\": 5}',0,NULL,NULL,'2025-06-03 19:02:31'),
(47,1,'login_blocked','high','*************','登录被IP安全策略阻止: 检测到异常多IP登录，账户已被保护性封禁',NULL,0,NULL,NULL,'2025-06-03 19:02:31'),
(48,1,'auto_ban_multi_ip','critical','*************','检测到异常多IP登录行为（5个IP地址），系统自动封禁','{\"ip_count\": 5}',0,NULL,NULL,'2025-06-04 04:49:51'),
(49,1,'login_blocked','high','*************','登录被IP安全策略阻止: 检测到异常多IP登录，账户已被保护性封禁',NULL,0,NULL,NULL,'2025-06-04 04:49:51');
/*!40000 ALTER TABLE `security_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_configs`
--

DROP TABLE IF EXISTS `system_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `system_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL,
  `config_value` text NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_configs`
--

LOCK TABLES `system_configs` WRITE;
/*!40000 ALTER TABLE `system_configs` DISABLE KEYS */;
INSERT INTO `system_configs` VALUES
(1,'max_ip_per_user','3','每个用户允许的最大IP数量','2025-06-03 18:36:10',NULL),
(2,'ip_check_enabled','1','是否启用IP监控','2025-06-03 18:36:10',NULL),
(3,'auto_ban_enabled','1','是否启用自动封禁','2025-06-03 18:36:10',NULL),
(4,'ip_check_window','24','IP检查时间窗口（小时）','2025-06-03 18:36:10',NULL),
(5,'suspicious_login_threshold','5','可疑登录阈值','2025-06-03 18:36:10',NULL),
(6,'geo_check_enabled','0','是否启用地理位置检查','2025-06-03 18:36:10',NULL);
/*!40000 ALTER TABLE `system_configs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_sessions`
--

DROP TABLE IF EXISTS `user_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NOT NULL,
  `last_activity` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `user_id` (`user_id`),
  KEY `expires_at` (`expires_at`),
  KEY `is_active` (`is_active`),
  CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='用户会话表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_sessions`
--

LOCK TABLES `user_sessions` WRITE;
/*!40000 ALTER TABLE `user_sessions` DISABLE KEYS */;
INSERT INTO `user_sessions` VALUES
(2,1,'8cd5d0e20b287c92b77aa941a4f16a86e45bfc39879860f1703b7a4d2ef3df9fadc2186d23f50336d0e259518d9aa09523bec186a63b4ff40ab465ef0397eb43','************','AuthClient-Python/1.0.0','2025-06-03 18:02:35','2025-06-04 04:02:35','2025-06-03 18:37:08',0);
/*!40000 ALTER TABLE `user_sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `status` enum('active','banned','suspended') NOT NULL DEFAULT 'active',
  `last_login` timestamp NULL DEFAULT NULL,
  `last_ip` varchar(45) DEFAULT NULL,
  `login_count` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `banned_at` timestamp NULL DEFAULT NULL,
  `banned_reason` text,
  `banned_by` int(11) DEFAULT NULL COMMENT '封禁操作管理员ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `status` (`status`),
  KEY `last_login` (`last_login`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='正式用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES
(1,'xiyewuqiu','$2y$10$C0SpXAD8TR6qdH5mw.hEOOgEf0Qv1pzEMVOlEygufcwNR7rZksY1a','<EMAIL>','','',NULL,'banned','2025-06-03 18:02:35','************',2,'2025-06-03 17:48:17','2025-06-04 04:49:51','2025-06-04 04:49:51','检测到异常多IP登录行为（5个IP地址），系统自动封禁',NULL);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-04  6:59:11
