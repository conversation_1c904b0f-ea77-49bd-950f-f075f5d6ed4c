-- 升级IP监控系统 - 集成api.ip77.net强大功能
-- 执行时间：2025-07-11

-- 1. 备份现有数据
CREATE TABLE ip_monitors_backup AS SELECT * FROM ip_monitors;

-- 2. 添加新字段以支持api.ip77.net的丰富数据
ALTER TABLE ip_monitors 
ADD COLUMN continent VARCHAR(50) DEFAULT NULL COMMENT '大洲',
ADD COLUMN country VARCHAR(50) DEFAULT NULL COMMENT '国家',
ADD COLUMN country_code VARCHAR(10) DEFAULT NULL COMMENT '国家代码',
ADD COLUMN province VARCHAR(50) DEFAULT NULL COMMENT '省份',
ADD COLUMN city VARCHAR(50) DEFAULT NULL COMMENT '城市',
ADD COLUMN district VARCHAR(50) DEFAULT NULL COMMENT '区县',
ADD COLUMN street VARCHAR(100) DEFAULT NULL COMMENT '街道',
ADD COLUMN isp VARCHAR(100) DEFAULT NULL COMMENT 'ISP运营商',
ADD COLUMN latitude DECIMAL(10,8) DEFAULT NULL COMMENT '纬度',
ADD COLUMN longitude DECIMAL(11,8) DEFAULT NULL COMMENT '经度',
ADD COLUMN area_code VARCHAR(20) DEFAULT NULL COMMENT '行政区划代码',
ADD COLUMN zip_code VARCHAR(20) DEFAULT NULL COMMENT '邮政编码',
ADD COLUMN time_zone VARCHAR(50) DEFAULT NULL COMMENT '时区',
ADD COLUMN ip_int BIGINT UNSIGNED DEFAULT NULL COMMENT 'IP整数值',
ADD COLUMN street_history JSON DEFAULT NULL COMMENT '历史位置记录',
ADD COLUMN risk_score INT DEFAULT 0 COMMENT '风险评分(0-100)',
ADD COLUMN risk_level VARCHAR(20) DEFAULT 'unknown' COMMENT '风险等级',
ADD COLUMN is_proxy TINYINT(1) DEFAULT 0 COMMENT '是否代理IP',
ADD COLUMN proxy_type VARCHAR(50) DEFAULT NULL COMMENT '代理类型',
ADD COLUMN risk_tag VARCHAR(100) DEFAULT NULL COMMENT '风险标签',
ADD COLUMN api_source VARCHAR(50) DEFAULT 'manual' COMMENT 'API数据源',
ADD COLUMN last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间';

-- 3. 修改现有字段长度以适应更详细的位置信息
ALTER TABLE ip_monitors 
MODIFY COLUMN location VARCHAR(200) DEFAULT NULL COMMENT 'IP详细位置';

-- 4. 添加索引优化查询性能
ALTER TABLE ip_monitors 
ADD INDEX idx_country_code (country_code),
ADD INDEX idx_province (province),
ADD INDEX idx_city (city),
ADD INDEX idx_risk_score (risk_score),
ADD INDEX idx_is_proxy (is_proxy),
ADD INDEX idx_api_source (api_source),
ADD INDEX idx_last_updated (last_updated);

-- 5. 创建IP查询缓存表（避免重复调用API）
CREATE TABLE ip_query_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL UNIQUE,
    ip_int BIGINT UNSIGNED DEFAULT NULL,
    continent VARCHAR(50) DEFAULT NULL,
    country VARCHAR(50) DEFAULT NULL,
    country_code VARCHAR(10) DEFAULT NULL,
    province VARCHAR(50) DEFAULT NULL,
    city VARCHAR(50) DEFAULT NULL,
    district VARCHAR(50) DEFAULT NULL,
    street VARCHAR(100) DEFAULT NULL,
    isp VARCHAR(100) DEFAULT NULL,
    latitude DECIMAL(10,8) DEFAULT NULL,
    longitude DECIMAL(11,8) DEFAULT NULL,
    area_code VARCHAR(20) DEFAULT NULL,
    zip_code VARCHAR(20) DEFAULT NULL,
    time_zone VARCHAR(50) DEFAULT NULL,
    location VARCHAR(200) DEFAULT NULL,
    street_history JSON DEFAULT NULL,
    risk_score INT DEFAULT 0,
    risk_level VARCHAR(20) DEFAULT 'unknown',
    is_proxy TINYINT(1) DEFAULT 0,
    proxy_type VARCHAR(50) DEFAULT NULL,
    risk_tag VARCHAR(100) DEFAULT NULL,
    api_source VARCHAR(50) DEFAULT 'api.ip77.net',
    query_count INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP DEFAULT (DATE_ADD(CURRENT_TIMESTAMP, INTERVAL 7 DAY)),
    
    INDEX idx_ip_address (ip_address),
    INDEX idx_country_code (country_code),
    INDEX idx_risk_score (risk_score),
    INDEX idx_is_proxy (is_proxy),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP查询缓存表';

-- 6. 创建IP风险统计表
CREATE TABLE ip_risk_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date_key DATE NOT NULL,
    total_ips INT DEFAULT 0,
    low_risk_ips INT DEFAULT 0,
    medium_risk_ips INT DEFAULT 0,
    high_risk_ips INT DEFAULT 0,
    proxy_ips INT DEFAULT 0,
    datacenter_ips INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_date (date_key),
    INDEX idx_date_key (date_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP风险统计表';

-- 7. 创建API调用日志表
CREATE TABLE ip_api_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    api_endpoint VARCHAR(100) NOT NULL,
    request_data TEXT DEFAULT NULL,
    response_data TEXT DEFAULT NULL,
    response_code INT DEFAULT NULL,
    response_time_ms INT DEFAULT NULL,
    success TINYINT(1) DEFAULT 0,
    error_message TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_ip_address (ip_address),
    INDEX idx_api_endpoint (api_endpoint),
    INDEX idx_success (success),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP API调用日志表';

-- 8. 插入初始配置数据
INSERT INTO system_configs (config_key, config_value, description) VALUES
('ip_api_enabled', '1', '是否启用IP API查询'),
('ip_api_primary', 'api.ip77.net', '主要IP API服务'),
('ip_api_cache_days', '7', 'IP查询缓存天数'),
('ip_risk_threshold_medium', '40', '中风险阈值'),
('ip_risk_threshold_high', '70', '高风险阈值'),
('ip_auto_block_high_risk', '0', '是否自动阻止高风险IP'),
('ip_proxy_detection_enabled', '1', '是否启用代理检测')
ON DUPLICATE KEY UPDATE 
config_value = VALUES(config_value),
description = VALUES(description);

-- 9. 创建清理过期缓存的存储过程
DELIMITER //
CREATE PROCEDURE CleanExpiredIPCache()
BEGIN
    DELETE FROM ip_query_cache WHERE expires_at < NOW();

    -- 记录清理结果
    INSERT INTO operation_logs (admin_id, target_type, target_id, action, details)
    VALUES (0, 'system', 0, 'clean_ip_cache', CONCAT('清理过期IP缓存，删除 ', ROW_COUNT(), ' 条记录'));
END //
DELIMITER ;

-- 10. 创建更新风险统计的存储过程
DELIMITER //
CREATE PROCEDURE UpdateIPRiskStats()
BEGIN
    DECLARE today DATE DEFAULT CURDATE();

    INSERT INTO ip_risk_stats (
        date_key, total_ips, low_risk_ips, medium_risk_ips, high_risk_ips, proxy_ips, datacenter_ips
    )
    SELECT
        today,
        COUNT(*) as total_ips,
        SUM(CASE WHEN risk_score < 40 THEN 1 ELSE 0 END) as low_risk_ips,
        SUM(CASE WHEN risk_score >= 40 AND risk_score < 70 THEN 1 ELSE 0 END) as medium_risk_ips,
        SUM(CASE WHEN risk_score >= 70 THEN 1 ELSE 0 END) as high_risk_ips,
        SUM(CASE WHEN is_proxy = 1 THEN 1 ELSE 0 END) as proxy_ips,
        SUM(CASE WHEN risk_tag LIKE '%数据中心%' THEN 1 ELSE 0 END) as datacenter_ips
    FROM ip_monitors
    WHERE DATE(last_seen) = today
    ON DUPLICATE KEY UPDATE
        total_ips = VALUES(total_ips),
        low_risk_ips = VALUES(low_risk_ips),
        medium_risk_ips = VALUES(medium_risk_ips),
        high_risk_ips = VALUES(high_risk_ips),
        proxy_ips = VALUES(proxy_ips),
        datacenter_ips = VALUES(datacenter_ips),
        updated_at = CURRENT_TIMESTAMP;
END //
DELIMITER ;

-- 升级完成提示
SELECT 'IP监控系统升级完成！新增了强大的地理位置、风险评估和代理检测功能。' as message;
