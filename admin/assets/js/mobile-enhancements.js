/**
 * 移动端增强功能 💖
 * 提供更好的移动设备体验
 */

class MobileEnhancements {
    constructor() {
        this.init();
    }

    init() {
        this.setupTouchGestures();
        this.setupMobileOptimizations();
        this.setupAccessibility();
        this.setupPerformanceOptimizations();
    }

    // 触摸手势支持
    setupTouchGestures() {
        let touchStartX = 0;
        let touchStartY = 0;
        let touchEndX = 0;
        let touchEndY = 0;

        document.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
            touchStartY = e.changedTouches[0].screenY;
        });

        document.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            touchEndY = e.changedTouches[0].screenY;
            this.handleSwipe(touchStartX, touchStartY, touchEndX, touchEndY);
        });
    }

    handleSwipe(startX, startY, endX, endY) {
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        const minSwipeDistance = 50;

        // 水平滑动
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
            if (deltaX > 0) {
                this.onSwipeRight();
            } else {
                this.onSwipeLeft();
            }
        }
        
        // 垂直滑动
        if (Math.abs(deltaY) > Math.abs(deltaX) && Math.abs(deltaY) > minSwipeDistance) {
            if (deltaY > 0) {
                this.onSwipeDown();
            } else {
                this.onSwipeUp();
            }
        }
    }

    onSwipeRight() {
        // 右滑打开侧边栏
        if (window.innerWidth <= 768) {
            const sidebar = document.querySelector('.sidebar');
            if (sidebar && !sidebar.classList.contains('show')) {
                if (typeof toggleMobileSidebar === 'function') {
                    toggleMobileSidebar();
                }
            }
        }
    }

    onSwipeLeft() {
        // 左滑关闭侧边栏
        if (window.innerWidth <= 768) {
            const sidebar = document.querySelector('.sidebar');
            if (sidebar && sidebar.classList.contains('show')) {
                if (typeof toggleMobileSidebar === 'function') {
                    toggleMobileSidebar();
                }
            }
        }
    }

    onSwipeDown() {
        // 下滑刷新（在顶部时）
        if (window.scrollY === 0) {
            this.showPullToRefresh();
        }
    }

    onSwipeUp() {
        // 上滑隐藏移动端菜单按钮
        this.hideMobileMenuButton();
    }

    // 移动端优化
    setupMobileOptimizations() {
        // 防止双击缩放
        this.preventDoubleClickZoom();
        
        // 优化表格滚动
        this.optimizeTableScroll();
        
        // 优化模态框
        this.optimizeModals();
        
        // 优化按钮点击
        this.optimizeButtonClicks();
    }

    preventDoubleClickZoom() {
        let lastTouchEnd = 0;
        document.addEventListener('touchend', (e) => {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                e.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    }

    optimizeTableScroll() {
        const tables = document.querySelectorAll('.table-responsive');
        tables.forEach(table => {
            // 添加滚动指示器
            const scrollIndicator = document.createElement('div');
            scrollIndicator.className = 'scroll-indicator';
            scrollIndicator.innerHTML = '<i class="bi bi-arrow-left-right"></i> 左右滑动查看更多';
            scrollIndicator.style.cssText = `
                text-align: center;
                padding: 0.5rem;
                background: rgba(102, 126, 234, 0.1);
                color: #667eea;
                font-size: 0.8rem;
                border-radius: 0 0 8px 8px;
                display: none;
            `;
            
            table.parentNode.insertBefore(scrollIndicator, table.nextSibling);
            
            // 检查是否需要滚动
            const checkScroll = () => {
                if (table.scrollWidth > table.clientWidth) {
                    scrollIndicator.style.display = 'block';
                } else {
                    scrollIndicator.style.display = 'none';
                }
            };
            
            checkScroll();
            window.addEventListener('resize', checkScroll);
        });
    }

    optimizeModals() {
        // 模态框打开时禁止背景滚动
        document.addEventListener('shown.bs.modal', () => {
            document.body.style.overflow = 'hidden';
        });
        
        document.addEventListener('hidden.bs.modal', () => {
            document.body.style.overflow = '';
        });
    }

    optimizeButtonClicks() {
        // 为所有按钮添加触摸反馈
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(button => {
            button.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });
            
            button.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    }

    // 无障碍功能
    setupAccessibility() {
        // 增大点击区域
        this.enlargeClickAreas();
        
        // 添加焦点指示器
        this.addFocusIndicators();
        
        // 优化键盘导航
        this.optimizeKeyboardNavigation();
    }

    enlargeClickAreas() {
        const smallElements = document.querySelectorAll('.btn-sm, .badge, .close');
        smallElements.forEach(element => {
            element.style.minHeight = '44px';
            element.style.minWidth = '44px';
            element.style.display = 'inline-flex';
            element.style.alignItems = 'center';
            element.style.justifyContent = 'center';
        });
    }

    addFocusIndicators() {
        const style = document.createElement('style');
        style.textContent = `
            @media (max-width: 768px) {
                *:focus {
                    outline: 3px solid #667eea !important;
                    outline-offset: 2px !important;
                }
            }
        `;
        document.head.appendChild(style);
    }

    optimizeKeyboardNavigation() {
        // Tab键导航优化
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });
        
        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });
    }

    // 性能优化
    setupPerformanceOptimizations() {
        // 图片懒加载
        this.setupLazyLoading();
        
        // 防抖滚动事件
        this.debounceScrollEvents();
        
        // 优化动画
        this.optimizeAnimations();
    }

    setupLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    }

    debounceScrollEvents() {
        let scrollTimeout;
        const originalScroll = window.onscroll;
        
        window.onscroll = function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                if (originalScroll) originalScroll.apply(this, arguments);
            }, 16); // 60fps
        };
    }

    optimizeAnimations() {
        // 检查用户是否偏好减少动画
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            const style = document.createElement('style');
            style.textContent = `
                *, *::before, *::after {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            `;
            document.head.appendChild(style);
        }
    }

    // 下拉刷新
    showPullToRefresh() {
        if (document.querySelector('.pull-to-refresh')) return;
        
        const refreshIndicator = document.createElement('div');
        refreshIndicator.className = 'pull-to-refresh';
        refreshIndicator.innerHTML = `
            <div style="text-align: center; padding: 1rem; background: #667eea; color: white; position: fixed; top: 0; left: 0; right: 0; z-index: 9999;">
                <i class="bi bi-arrow-clockwise spin"></i> 松开刷新
            </div>
        `;
        
        document.body.appendChild(refreshIndicator);
        
        setTimeout(() => {
            refreshIndicator.remove();
            location.reload();
        }, 1000);
    }

    // 隐藏移动端菜单按钮
    hideMobileMenuButton() {
        const menuButton = document.querySelector('.d-md-none .btn');
        if (menuButton) {
            menuButton.style.transform = 'translateY(-100%)';
            setTimeout(() => {
                menuButton.style.transform = '';
            }, 2000);
        }
    }
}

// 初始化移动端增强功能
document.addEventListener('DOMContentLoaded', () => {
    if (window.innerWidth <= 768) {
        new MobileEnhancements();
    }
});

// 导出供其他脚本使用
window.MobileEnhancements = MobileEnhancements;
