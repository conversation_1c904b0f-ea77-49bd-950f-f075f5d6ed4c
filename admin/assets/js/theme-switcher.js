/**
 * 主题切换器 💖
 * 支持白天/黑夜模式切换
 */

class ThemeSwitcher {
    constructor() {
        this.currentTheme = this.getStoredTheme() || this.getPreferredTheme();
        this.init();
    }

    init() {
        this.createToggleButton();
        this.applyTheme(this.currentTheme);
        this.setupEventListeners();
    }

    // 获取存储的主题
    getStoredTheme() {
        return localStorage.getItem('admin-theme');
    }

    // 获取系统偏好主题
    getPreferredTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }

    // 存储主题设置
    setStoredTheme(theme) {
        localStorage.setItem('admin-theme', theme);
    }

    // 创建切换按钮
    createToggleButton() {
        const toggleButton = document.createElement('button');
        toggleButton.className = 'theme-toggle';
        toggleButton.id = 'themeToggle';
        toggleButton.setAttribute('aria-label', '切换主题');
        toggleButton.setAttribute('title', '切换白天/黑夜模式');
        
        this.updateButtonIcon(toggleButton);
        
        document.body.appendChild(toggleButton);
    }

    // 更新按钮图标
    updateButtonIcon(button) {
        const icon = this.currentTheme === 'dark' ? 
            '<i class="bi bi-sun-fill"></i>' : 
            '<i class="bi bi-moon-fill"></i>';
        button.innerHTML = icon;
    }

    // 应用主题
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        this.currentTheme = theme;
        this.setStoredTheme(theme);
        
        const toggleButton = document.getElementById('themeToggle');
        if (toggleButton) {
            this.updateButtonIcon(toggleButton);
        }

        // 触发主题变化事件
        this.dispatchThemeChangeEvent(theme);
    }

    // 切换主题
    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
        
        // 添加切换动画
        this.addToggleAnimation();
    }

    // 添加切换动画
    addToggleAnimation() {
        const toggleButton = document.getElementById('themeToggle');
        if (toggleButton) {
            toggleButton.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                toggleButton.style.transform = '';
            }, 300);
        }

        // 页面过渡动画
        document.body.style.transition = 'all 0.3s ease';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
    }

    // 设置事件监听器
    setupEventListeners() {
        // 按钮点击事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('#themeToggle')) {
                this.toggleTheme();
            }
        });

        // 键盘快捷键 (Ctrl + Shift + T)
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                this.toggleTheme();
            }
        });

        // 监听系统主题变化
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (!this.getStoredTheme()) {
                    this.applyTheme(e.matches ? 'dark' : 'light');
                }
            });
        }
    }

    // 触发主题变化事件
    dispatchThemeChangeEvent(theme) {
        const event = new CustomEvent('themeChanged', {
            detail: { theme: theme }
        });
        document.dispatchEvent(event);
    }

    // 获取当前主题
    getCurrentTheme() {
        return this.currentTheme;
    }

    // 设置特定主题
    setTheme(theme) {
        if (theme === 'light' || theme === 'dark') {
            this.applyTheme(theme);
        }
    }
}

// 主题切换器实例
let themeSwitcher;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    themeSwitcher = new ThemeSwitcher();
    
    // 添加主题变化监听器
    document.addEventListener('themeChanged', (e) => {
        console.log('主题已切换到:', e.detail.theme);
        
        // 可以在这里添加主题切换后的额外处理
        updateChartsTheme(e.detail.theme);
        updateTableTheme(e.detail.theme);
    });
});

// 更新图表主题
function updateChartsTheme(theme) {
    // 如果页面有Chart.js图表，更新其主题
    if (typeof Chart !== 'undefined') {
        Chart.defaults.color = theme === 'dark' ? '#e0e0e0' : '#333';
        Chart.defaults.borderColor = theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)';
        
        // 重新渲染所有图表
        Chart.instances.forEach(chart => {
            chart.update();
        });
    }
}

// 更新表格主题
function updateTableTheme(theme) {
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        if (theme === 'dark') {
            table.classList.add('table-dark');
        } else {
            table.classList.remove('table-dark');
        }
    });
}

// 导出主题切换器供其他脚本使用
window.ThemeSwitcher = ThemeSwitcher;
window.themeSwitcher = themeSwitcher;

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    .theme-toggle {
        transition: all 0.3s ease !important;
    }
    
    .theme-toggle:hover {
        animation: pulse 1s infinite;
    }
    
    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
        100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
    }
    
    /* 主题切换过渡动画 */
    * {
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
    }
    
    /* 深色模式下的滚动条样式 */
    [data-theme="dark"] ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }
    
    [data-theme="dark"] ::-webkit-scrollbar-track {
        background: #2d3748;
    }
    
    [data-theme="dark"] ::-webkit-scrollbar-thumb {
        background: #4a5568;
        border-radius: 4px;
    }
    
    [data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
        background: #667eea;
    }
`;
document.head.appendChild(style);
