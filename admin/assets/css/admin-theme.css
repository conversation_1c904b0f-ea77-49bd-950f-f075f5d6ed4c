/**
 * 管理后台统一主题样式 💖
 * 现代化、美观的管理界面样式
 * Version: 2.0
 */

:root {
    /* 重新设计的现代化配色方案 */
    --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --primary-gradient-hover: linear-gradient(135deg, #5b5bd6 0%, #7c3aed 100%);
    --primary-color: #6366f1;
    --primary-light: #a5b4fc;
    --primary-dark: #4f46e5;

    /* 功能色 - 更加柔和的配色 */
    --success-gradient: linear-gradient(135deg, #10b981 0%, #34d399 100%);
    --success-color: #10b981;
    --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    --warning-color: #f59e0b;
    --danger-gradient: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
    --danger-color: #ef4444;
    --info-gradient: linear-gradient(135deg, #06b6d4 0%, #67e8f9 100%);
    --info-color: #06b6d4;

    /* 背景系统 */
    --bg-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --card-bg: rgba(255, 255, 255, 0.8);
    --card-bg-hover: rgba(255, 255, 255, 0.95);

    /* 阴影系统 - 更加细腻 */
    --shadow-xs: 0 1px 2px rgba(0,0,0,0.05);
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.1), 0 1px 2px rgba(0,0,0,0.06);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.07), 0 2px 4px rgba(0,0,0,0.06);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.05);
    --shadow-xl: 0 20px 25px rgba(0,0,0,0.1), 0 10px 10px rgba(0,0,0,0.04);
    --shadow-2xl: 0 25px 50px rgba(0,0,0,0.25);
    
    /* 圆角 */
    --border-radius-sm: 8px;
    --border-radius-md: 15px;
    --border-radius-lg: 20px;
    --border-radius-xl: 25px;
    --border-radius-pill: 50px;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* 字体 */
    --font-family: 'Segoe UI', 'Microsoft YaHei', Tahoma, Geneva, Verdana, sans-serif;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* 过渡动画 */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* 全局样式 */
body {
    background: var(--bg-gradient);
    font-family: var(--font-family);
    min-height: 100vh;
    line-height: 1.6;
    color: #374151;
}

/* 改进的页面布局 */
.main-container {
    background: var(--bg-gradient);
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    min-height: 100vh;
    background: var(--primary-gradient);
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    border-radius: var(--border-radius-sm);
    margin: 5px 10px;
    padding: 12px 20px;
    transition: var(--transition-normal);
    font-weight: var(--font-weight-medium);
    position: relative;
    overflow: hidden;
}

.sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: var(--transition-normal);
}

.sidebar .nav-link:hover::before {
    left: 100%;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background: rgba(255,255,255,0.15);
    color: white;
    transform: translateX(5px);
    box-shadow: var(--shadow-light);
}

.sidebar .nav-link.active {
    background: rgba(255,255,255,0.2);
    font-weight: var(--font-weight-semibold);
}

.sidebar .nav-link i {
    margin-right: 8px;
    width: 20px;
    text-align: center;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-medium);
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
    background: var(--card-bg);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: var(--spacing-lg);
    font-weight: var(--font-weight-semibold);
}

.card-body {
    padding: var(--spacing-lg);
}

/* 重新设计的统计卡片 */
.stats-card {
    background: var(--card-bg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 24px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
    text-align: left;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    border-radius: 16px 16px 0 0;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: var(--card-bg-hover);
}

/* 统计卡片主题色 */
.stats-card.primary::before { background: var(--primary-gradient); }
.stats-card.success::before { background: var(--success-gradient); }
.stats-card.warning::before { background: var(--warning-gradient); }
.stats-card.danger::before { background: var(--danger-gradient); }
.stats-card.info::before { background: var(--info-gradient); }

/* 统计卡片图标 */
.stats-card .stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 24px;
    color: white;
}

.stats-card.primary .stats-icon { background: var(--primary-gradient); }
.stats-card.success .stats-icon { background: var(--success-gradient); }
.stats-card.warning .stats-icon { background: var(--warning-gradient); }
.stats-card.danger .stats-icon { background: var(--danger-gradient); }
.stats-card.info .stats-icon { background: var(--info-gradient); }

.stats-number {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: #1f2937;
    line-height: 1.2;
}

.stats-label {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 0;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* 按钮样式 */
.btn-gradient {
    background: var(--primary-gradient);
    border: none;
    color: white;
    border-radius: var(--border-radius-pill);
    padding: 0.75rem 2rem;
    transition: var(--transition-normal);
    font-weight: var(--font-weight-medium);
    position: relative;
    overflow: hidden;
}

.btn-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition-normal);
}

.btn-gradient:hover::before {
    left: 100%;
}

.btn-gradient:hover {
    background: var(--primary-gradient-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
    color: white;
}

.btn-gradient:active {
    transform: translateY(0);
}

/* 现代化表格样式 */
.table {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    border: none;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #374151;
    font-weight: 600;
    padding: 16px 20px;
    text-align: left;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid #e5e7eb;
}

.table th:first-child {
    border-top-left-radius: 16px;
}

.table th:last-child {
    border-top-right-radius: 16px;
}

.table td {
    padding: 16px 20px;
    vertical-align: middle;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    color: #374151;
    font-size: 0.875rem;
}

.table tbody tr {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.5);
}

.table tbody tr:hover {
    background: rgba(99, 102, 241, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.table tbody tr:last-child td:first-child {
    border-bottom-left-radius: 16px;
}

.table tbody tr:last-child td:last-child {
    border-bottom-right-radius: 16px;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* 用户头像样式 */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
    margin-right: 12px;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

/* 状态标签样式 */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.active {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.inactive {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

/* 用户信息展示 */
.user-info {
    display: flex;
    align-items: center;
}

.user-details h6 {
    margin: 0;
    font-weight: 600;
    color: #1f2937;
    font-size: 0.875rem;
}

.user-details small {
    color: #6b7280;
    font-size: 0.75rem;
}

/* 改进的按钮样式 */
.btn-group-vertical .btn {
    border-radius: 8px !important;
    margin-bottom: 4px;
    font-size: 0.75rem;
    padding: 6px 12px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-group-vertical .btn:hover {
    transform: translateX(2px);
    box-shadow: var(--shadow-sm);
}

/* 权限标签样式 */
.permission-badge {
    font-size: 0.7rem;
    padding: 4px 8px;
    border-radius: 12px;
    margin: 2px;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 表格操作栏样式 */
.table-actions {
    background: var(--primary-gradient);
    padding: 16px 20px;
    border-radius: 16px 16px 0 0;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
}

.batch-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.batch-actions .btn {
    font-size: 0.75rem;
    padding: 6px 12px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.batch-actions .btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* 搜索框样式优化 */
.input-group {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.input-group .form-control {
    border: none;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 12px 16px;
}

.input-group .input-group-text {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 12px 16px;
}

.input-group .btn {
    border: none;
    padding: 12px 20px;
    font-weight: 500;
}

/* 筛选标签样式 */
.filter-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filter-tab {
    padding: 8px 16px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(99, 102, 241, 0.2);
    color: #6b7280;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-tab:hover,
.filter-tab.active {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* 页面加载动画 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-slide-up {
    animation: slideInUp 0.4s ease-out;
}

/* 表单样式 */
.form-control {
    border-radius: var(--border-radius-sm);
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: var(--transition-normal);
    background: rgba(255,255,255,0.9);
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.search-box {
    border-radius: var(--border-radius-pill);
    border: 2px solid transparent;
    background: rgba(255,255,255,0.9);
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
}

.search-box:focus {
    border-color: #667eea;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

/* 状态标签 */
.status-active {
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius-pill);
    font-size: 0.8rem;
    font-weight: var(--font-weight-medium);
}

.status-banned {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius-pill);
    font-size: 0.8rem;
    font-weight: var(--font-weight-medium);
}

.status-pending {
    color: #ffc107;
    background: rgba(255, 193, 7, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius-pill);
    font-size: 0.8rem;
    font-weight: var(--font-weight-medium);
}

/* 用户头像 */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: var(--font-weight-bold);
    box-shadow: var(--shadow-light);
}

/* 权限徽章 */
.permission-badge {
    margin: 2px;
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius-pill);
    font-size: 0.7rem;
    font-weight: var(--font-weight-medium);
    transition: var(--transition-fast);
}

.permission-badge:hover {
    transform: scale(1.05);
}

/* 健康状态指示器 */
.health-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
    box-shadow: 0 0 10px currentColor;
    animation: pulse-glow 2s infinite;
}

.health-healthy { 
    background: #28a745;
    color: #28a745;
}

.health-warning { 
    background: #ffc107;
    color: #ffc107;
}

.health-error { 
    background: #dc3545;
    color: #dc3545;
}

/* 动画效果 */
@keyframes shimmer {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse-glow {
    0%, 100% { 
        opacity: 1;
        box-shadow: 0 0 5px currentColor;
    }
    50% { 
        opacity: 0.7;
        box-shadow: 0 0 15px currentColor;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* 响应式设计 */
/* 移动设备优先 */
@media (max-width: 576px) {
    :root {
        --spacing-xs: 0.125rem;
        --spacing-sm: 0.25rem;
        --spacing-md: 0.5rem;
        --spacing-lg: 0.75rem;
        --spacing-xl: 1rem;
        --spacing-xxl: 1.5rem;
    }

    body {
        font-size: 14px;
    }

    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        z-index: 1050;
        transition: var(--transition-normal);
    }

    .sidebar.show {
        left: 0;
    }

    .sidebar .nav-link {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 0.9rem;
    }

    .stats-card {
        margin-bottom: var(--spacing-md);
        padding: var(--spacing-lg);
    }

    .stats-number {
        font-size: 1.8rem;
    }

    .card {
        margin-bottom: var(--spacing-md);
        border-radius: var(--border-radius-md);
    }

    .card-body {
        padding: var(--spacing-md);
    }

    .btn-gradient {
        width: 100%;
        margin-bottom: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 0.9rem;
    }

    .table {
        font-size: 0.8rem;
    }

    .table th,
    .table td {
        padding: var(--spacing-sm);
    }

    .modal-dialog {
        margin: var(--spacing-sm);
        max-width: calc(100% - 1rem);
    }

    .search-box {
        font-size: 0.9rem;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .permission-badge {
        font-size: 0.6rem;
        padding: 0.125rem 0.5rem;
    }

    .chart-container {
        height: 200px;
    }

    .metric-card {
        padding: var(--spacing-sm);
    }

    .metric-number {
        font-size: 1.5rem;
    }

    .filter-tabs {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .filter-tab {
        text-align: center;
        padding: var(--spacing-sm);
    }

    .permission-grid {
        grid-template-columns: 1fr;
    }

    .stats-overview {
        grid-template-columns: 1fr;
    }
}

/* 平板设备 */
@media (min-width: 577px) and (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 280px;
        height: 100vh;
        z-index: 1050;
        transition: var(--transition-normal);
    }

    .sidebar.show {
        left: 0;
    }

    .stats-card {
        margin-bottom: var(--spacing-md);
    }

    .card {
        margin-bottom: var(--spacing-md);
    }

    .btn-gradient {
        margin-bottom: var(--spacing-sm);
    }

    .permission-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .stats-overview {
        grid-template-columns: repeat(2, 1fr);
    }

    .chart-container {
        height: 250px;
    }
}

/* 中等屏幕 */
@media (min-width: 769px) and (max-width: 992px) {
    .permission-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .stats-overview {
        grid-template-columns: repeat(3, 1fr);
    }

    .chart-container {
        height: 280px;
    }
}

/* 大屏幕 */
@media (min-width: 993px) and (max-width: 1200px) {
    .permission-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .stats-overview {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* 超大屏幕 */
@media (min-width: 1201px) {
    .permission-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .stats-overview {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .chart-container {
        height: 350px;
    }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 600px) {
    .sidebar {
        width: 250px;
    }

    .sidebar .nav-link {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .stats-card {
        padding: var(--spacing-md);
    }

    .stats-number {
        font-size: 1.8rem;
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .card {
        box-shadow: var(--shadow-heavy);
    }

    .btn-gradient {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .table {
        border-collapse: separate;
        border-spacing: 0;
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .btn,
    .modal,
    .alert {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .table {
        border-collapse: collapse;
    }

    .table th,
    .table td {
        border: 1px solid #ddd;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* 可访问性增强 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 对比度增强 */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }

    .btn-gradient {
        border: 2px solid #000;
    }

    .table th {
        border: 2px solid #000;
    }
}

/* 主题切换系统 */
:root {
    /* 白天模式（默认） */
    --theme-bg: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    --theme-card-bg: rgba(255, 255, 255, 0.95);
    --theme-text: #333;
    --theme-text-muted: #666;
    --theme-border: rgba(0,0,0,0.1);
    --theme-shadow: rgba(0,0,0,0.1);
}

/* 深色模式 */
[data-theme="dark"] {
    --theme-bg: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    --theme-card-bg: rgba(40, 40, 40, 0.95);
    --theme-text: #e0e0e0;
    --theme-text-muted: #aaa;
    --theme-border: rgba(255,255,255,0.1);
    --theme-shadow: rgba(0,0,0,0.3);
}

/* 应用主题变量 */
body {
    background: var(--theme-bg);
    color: var(--theme-text);
}

.card {
    background: var(--theme-card-bg);
    color: var(--theme-text);
}

.table td {
    border-bottom-color: var(--theme-border);
    color: var(--theme-text);
}

.form-control {
    background: var(--theme-card-bg);
    border-color: var(--theme-border);
    color: var(--theme-text);
}

.form-control:focus {
    background: var(--theme-card-bg);
    color: var(--theme-text);
}

.text-muted {
    color: var(--theme-text-muted) !important;
}

/* 主题切换按钮 */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1060;
    background: var(--primary-gradient);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-medium);
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-heavy);
}

.theme-toggle:active {
    transform: scale(0.95);
}

/* 深色模式特定样式 */
[data-theme="dark"] .sidebar {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}

[data-theme="dark"] .table th {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}

[data-theme="dark"] .modal-content {
    background: var(--theme-card-bg);
    color: var(--theme-text);
}

[data-theme="dark"] .modal-header {
    background: var(--primary-gradient);
}

[data-theme="dark"] .alert {
    background: var(--theme-card-bg);
    border-color: var(--theme-border);
    color: var(--theme-text);
}

[data-theme="dark"] .search-box {
    background: var(--theme-card-bg);
    border-color: var(--theme-border);
    color: var(--theme-text);
}

[data-theme="dark"] .input-group-text {
    background: var(--theme-card-bg);
    border-color: var(--theme-border);
    color: var(--theme-text);
}
