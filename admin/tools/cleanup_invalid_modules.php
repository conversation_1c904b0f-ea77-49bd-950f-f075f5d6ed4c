<?php
/**
 * 清理无效模块代码工具 💖
 * 将无效的模块代码替换为有效的模块代码
 */

session_start();

// 检查管理员登录
if ((!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) &&
    (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username']))) {
    header('Location: login.php');
    exit;
}

require_once '../../api/config/database.php';
require_once '../../includes/module_permission_manager.php';

$database = new Database();
$conn = $database->getConnection();
$permissionManager = new ModulePermissionManager($conn);

$message = '';
$messageType = '';

// 定义无效模块到有效模块的映射
$moduleMapping = [
    'auto_generation' => 'anti_piracy_generator',  // 自动生成 -> 防盗文件生成
    'collaboration' => 'author_organizer',         // 协作 -> 同名作者整理
    'content_cleanup' => 'content_cleaner',        // 内容清理 -> 文件内容净化
    'duplicate_removal' => 'file_duplicate_finder', // 重复移除 -> 文件查重
    'file_management' => 'batch_rename',           // 文件管理 -> 批量重命名
    'file_search' => 'directory_tree_generator'    // 文件搜索 -> 目录树生成
];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    try {
        $conn->beginTransaction();
        
        if ($action === 'cleanup_invalid_modules') {
            $cleanupResults = [];
            
            foreach ($moduleMapping as $invalidModule => $validModule) {
                // 查找使用无效模块的用户
                $sql = "SELECT user_id, granted_by FROM user_module_permissions 
                        WHERE module_code = ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$invalidModule]);
                $users = $stmt->fetchAll();
                
                foreach ($users as $user) {
                    $userId = $user['user_id'];
                    $grantedBy = $user['granted_by'];
                    
                    // 删除无效模块权限
                    $sql = "DELETE FROM user_module_permissions 
                            WHERE user_id = ? AND module_code = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->execute([$userId, $invalidModule]);
                    
                    // 检查是否已有有效模块权限
                    $sql = "SELECT id FROM user_module_permissions 
                            WHERE user_id = ? AND module_code = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->execute([$userId, $validModule]);
                    
                    if (!$stmt->fetch()) {
                        // 添加有效模块权限
                        $sql = "INSERT INTO user_module_permissions 
                                (user_id, module_code, is_active, granted_by) 
                                VALUES (?, ?, 1, ?)";
                        $stmt = $conn->prepare($sql);
                        $stmt->execute([$userId, $validModule, $grantedBy]);
                        
                        $cleanupResults[] = "用户ID {$userId}: {$invalidModule} -> {$validModule}";
                    } else {
                        $cleanupResults[] = "用户ID {$userId}: 删除 {$invalidModule} (已有 {$validModule})";
                    }
                }
            }
            
            $conn->commit();
            $message = "清理完成！处理了 " . count($cleanupResults) . " 个无效模块权限：<br>" . implode('<br>', $cleanupResults);
            $messageType = 'success';
            
        } elseif ($action === 'delete_invalid_modules') {
            // 直接删除所有无效模块权限
            $invalidModules = array_keys($moduleMapping);
            $placeholders = str_repeat('?,', count($invalidModules) - 1) . '?';
            
            $sql = "DELETE FROM user_module_permissions 
                    WHERE module_code IN ($placeholders)";
            $stmt = $conn->prepare($sql);
            $stmt->execute($invalidModules);
            
            $deletedCount = $stmt->rowCount();
            $conn->commit();
            
            $message = "已删除 {$deletedCount} 个无效模块权限记录";
            $messageType = 'success';
        }
        
    } catch (Exception $e) {
        $conn->rollBack();
        $message = '操作失败: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// 获取当前无效模块统计
$sql = "SELECT ump.module_code, u.username, ump.is_active, ump.granted_by
        FROM user_module_permissions ump
        JOIN users u ON ump.user_id = u.id
        WHERE ump.module_code IN ('" . implode("','", array_keys($moduleMapping)) . "')
        ORDER BY ump.module_code, u.username";
$stmt = $conn->prepare($sql);
$stmt->execute();
$invalidModuleData = $stmt->fetchAll();

// 获取有效模块列表
$availableModules = $permissionManager->getAvailableModules();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清理无效模块 💖 - 管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                    <h1 class="h2">
                        <i class="bi bi-tools text-warning"></i>
                        清理无效模块代码 💖
                    </h1>
                    <a href="../monitoring/sync_check.php" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left"></i> 返回同步检查
                    </a>
                </div>

                <!-- 消息提示 -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                    <i class="bi bi-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- 模块映射说明 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-arrow-left-right"></i> 模块代码映射关系</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($moduleMapping as $invalid => $valid): ?>
                            <div class="col-md-6 mb-2">
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-danger me-2"><?php echo $invalid; ?></span>
                                    <i class="bi bi-arrow-right mx-2"></i>
                                    <span class="badge bg-success"><?php echo $valid; ?></span>
                                    <small class="text-muted ms-2"><?php echo $availableModules[$valid] ?? ''; ?></small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- 当前无效模块数据 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-exclamation-triangle text-warning"></i> 当前无效模块权限 (<?php echo count($invalidModuleData); ?> 条)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($invalidModuleData)): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle"></i> 太棒了！没有发现无效模块权限！
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>用户名</th>
                                            <th>无效模块代码</th>
                                            <th>状态</th>
                                            <th>授权者ID</th>
                                            <th>建议替换为</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($invalidModuleData as $data): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($data['username']); ?></td>
                                            <td><span class="badge bg-danger"><?php echo $data['module_code']; ?></span></td>
                                            <td>
                                                <?php if ($data['is_active']): ?>
                                                    <span class="badge bg-warning">活跃</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">非活跃</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $data['granted_by'] ?: '未知'; ?></td>
                                            <td><span class="badge bg-success"><?php echo $moduleMapping[$data['module_code']]; ?></span></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 清理操作 -->
                <?php if (!empty($invalidModuleData)): ?>
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-gear"></i> 清理操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <form method="POST" onsubmit="return confirm('确定要智能替换无效模块吗？这将把无效模块替换为对应的有效模块。')">
                                    <input type="hidden" name="action" value="cleanup_invalid_modules">
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="bi bi-magic"></i> 智能替换无效模块
                                    </button>
                                    <small class="text-muted d-block mt-2">
                                        将无效模块替换为对应的有效模块，保留用户权限
                                    </small>
                                </form>
                            </div>
                            <div class="col-md-6">
                                <form method="POST" onsubmit="return confirm('确定要直接删除所有无效模块吗？此操作不可恢复！')">
                                    <input type="hidden" name="action" value="delete_invalid_modules">
                                    <button type="submit" class="btn btn-danger w-100">
                                        <i class="bi bi-trash"></i> 直接删除无效模块
                                    </button>
                                    <small class="text-muted d-block mt-2">
                                        直接删除所有无效模块权限，不进行替换
                                    </small>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
