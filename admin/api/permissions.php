<?php
/**
 * 管理员权限管理API
 * 智能权限管理系统 💖
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../../includes/admin.php';
require_once '../../includes/module_permission_manager.php';
require_once '../config/database.php';

try {
    session_start();
    
    // 检查管理员登录状态
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        throw new Exception('管理员未登录');
    }
    
    $admin = new Admin();
    $database = new Database();
    $conn = $database->getConnection();
    $permissionManager = new ModulePermissionManager($conn);
    $adminId = $_SESSION['admin_id'];
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // 处理权限操作
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('请求数据格式错误');
        }
        
        $action = $input['action'] ?? '';
        $userId = (int)($input['user_id'] ?? 0);
        
        if (!$userId) {
            throw new Exception('用户ID不能为空');
        }
        
        $result = ['success' => false, 'message' => '未知操作'];
        
        switch ($action) {
            case 'grant_app_permission':
                $appType = $input['app_type'] ?? '';
                $downloadCount = (int)($input['download_count'] ?? 0);
                $duration = (int)($input['duration'] ?? 0);
                $notes = $input['notes'] ?? '';
                
                $expiresAt = null;
                if ($duration > 0) {
                    $expiresAt = date('Y-m-d H:i:s', strtotime("+{$duration} days"));
                }
                
                $result = $permissionManager->grantAppAccess(
                    $userId, $appType, $downloadCount, $expiresAt, $adminId, $notes
                );
                break;
                
            case 'revoke_app_permission':
                $appType = $input['app_type'] ?? '';
                $result = $permissionManager->revokeAppAccess($userId, $appType, $adminId);
                break;
                
            case 'grant_module_permission':
                $moduleCode = $input['module_code'] ?? '';
                $result = $permissionManager->grantModuleAccess($userId, $moduleCode, $adminId);
                break;
                
            case 'revoke_module_permission':
                $moduleCode = $input['module_code'] ?? '';
                $result = $permissionManager->revokeModuleAccess($userId, $moduleCode, $adminId);
                break;
                
            case 'extend_permission':
                $appType = $input['app_type'] ?? '';
                $days = (int)($input['days'] ?? 0);
                $result = $permissionManager->extendPermission($userId, $appType, $days, $adminId);
                break;
                
            case 'apply_template':
                $template = $input['template'] ?? '';
                $result = applyPermissionTemplate($userId, $template, $permissionManager, $adminId);
                break;
                
            default:
                throw new Exception('无效的操作类型');
        }
        
        echo json_encode([
            'code' => $result['success'] ? 200 : 400,
            'success' => $result['success'],
            'message' => $result['message'],
            'data' => $result['data'] ?? null
        ], JSON_UNESCAPED_UNICODE);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // 获取权限信息
        $userId = (int)($_GET['user_id'] ?? 0);
        
        if (!$userId) {
            throw new Exception('用户ID不能为空');
        }
        
        $permissions = $permissionManager->getUserPermissions($userId);
        $availableModules = $permissionManager->getAvailableModules();
        
        echo json_encode([
            'code' => 200,
            'success' => true,
            'message' => '权限信息获取成功',
            'data' => [
                'user_permissions' => $permissions,
                'available_modules' => $availableModules
            ]
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'code' => 400,
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}

// 权限模板应用函数
function applyPermissionTemplate($userId, $template, $permissionManager, $adminId) {
    try {
        switch ($template) {
            case 'trial_user':
                // 试用用户：Android 3次，7天有效，基础模块
                $permissionManager->grantAppAccess($userId, 'android', 3, date('Y-m-d H:i:s', strtotime('+7 days')), $adminId, '试用用户模板');
                $trialModules = ['batch_rename'];
                foreach ($trialModules as $module) {
                    $permissionManager->grantModuleAccess($userId, $module, $adminId);
                }
                return ['success' => true, 'message' => '已应用试用用户权限模板（7天有效期）'];
                
            case 'basic_user':
                // 基础用户：Android 5次，永久，基础模块
                $permissionManager->grantAppAccess($userId, 'android', 5, null, $adminId, '基础用户模板');
                $basicModules = ['batch_rename', 'file_management'];
                foreach ($basicModules as $module) {
                    $permissionManager->grantModuleAccess($userId, $module, $adminId);
                }
                return ['success' => true, 'message' => '已应用基础用户权限模板'];
                
            case 'premium_user':
                // 高级用户：Android 20次 + Windows + 大部分模块
                $permissionManager->grantAppAccess($userId, 'android', 20, null, $adminId, '高级用户模板');
                $permissionManager->grantAppAccess($userId, 'windows', 0, null, $adminId, '高级用户模板');
                $premiumModules = ['batch_rename', 'file_management', 'duplicate_removal', 'collaboration', 'content_cleanup', 'file_search'];
                foreach ($premiumModules as $module) {
                    $permissionManager->grantModuleAccess($userId, $module, $adminId);
                }
                return ['success' => true, 'message' => '已应用高级用户权限模板'];
                
            case 'vip_user':
                // VIP用户：Android 50次 + Windows + 全部模块
                $permissionManager->grantAppAccess($userId, 'android', 50, null, $adminId, 'VIP用户模板');
                $permissionManager->grantAppAccess($userId, 'windows', 0, null, $adminId, 'VIP用户模板');
                $availableModules = $permissionManager->getAvailableModules();
                foreach ($availableModules as $moduleCode => $moduleName) {
                    $permissionManager->grantModuleAccess($userId, $moduleCode, $adminId);
                }
                return ['success' => true, 'message' => '已应用VIP用户权限模板'];
                
            default:
                return ['success' => false, 'message' => '未知的权限模板'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => '应用权限模板失败：' . $e->getMessage()];
    }
}
?>
