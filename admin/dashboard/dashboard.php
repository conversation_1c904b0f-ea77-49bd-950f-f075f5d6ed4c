<?php
/**
 * 智能管理后台仪表板 💖
 * 全新设计的现代化管理界面
 */

session_start();

// 检查管理员登录
if ((!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) &&
    (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username']))) {
    header('Location: ../auth/login.php');
    exit;
}

require_once '../../api/config/config.php';
require_once '../../api/config/database.php';
require_once '../../includes/admin.php';
require_once '../../includes/module_permission_manager.php';

$database = new Database();
$conn = $database->getConnection();
$admin = new Admin();
$permissionManager = new ModulePermissionManager($conn);

// 获取全面统计数据
try {
    // 用户统计
    $sql = "SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_users,
        SUM(CASE WHEN status = 'banned' THEN 1 ELSE 0 END) as banned_users
        FROM users WHERE status != 'deleted'";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats = $stmt->fetch();

    // 权限统计
    $sql = "SELECT 
        COUNT(DISTINCT user_id) as android_users,
        SUM(download_count) as total_downloads
        FROM user_app_permissions 
        WHERE app_type = 'android' AND is_active = 1 
        AND (expires_at IS NULL OR expires_at > NOW())";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $androidStats = $stmt->fetch();

    $sql = "SELECT COUNT(DISTINCT user_id) as windows_users
        FROM user_app_permissions 
        WHERE app_type = 'windows' AND is_active = 1
        AND (expires_at IS NULL OR expires_at > NOW())";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $windowsStats = $stmt->fetch();

    // 模块权限统计
    $sql = "SELECT COUNT(DISTINCT user_id) as module_users,
        COUNT(*) as total_module_permissions
        FROM user_module_permissions WHERE is_active = 1";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $moduleStats = $stmt->fetch();

    // 即将过期权限
    $sql = "SELECT COUNT(*) as expiring_soon
        FROM user_app_permissions 
        WHERE expires_at IS NOT NULL 
        AND expires_at > NOW() 
        AND expires_at <= DATE_ADD(NOW(), INTERVAL 7 DAY)
        AND is_active = 1";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $expiringStats = $stmt->fetch();

    // 今日活动统计
    $sql = "SELECT 
        COUNT(DISTINCT user_id) as today_logins,
        COUNT(*) as total_sessions
        FROM user_sessions 
        WHERE DATE(created_at) = CURDATE()";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $todayStats = $stmt->fetch();

    // 最近注册用户
    $sql = "SELECT username, email, created_at 
        FROM users 
        WHERE status = 'active' 
        ORDER BY created_at DESC 
        LIMIT 5";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $recentUsers = $stmt->fetchAll();

    // 系统健康状态
    $systemHealth = [
        'database' => 'healthy',
        'permissions' => 'healthy',
        'storage' => 'healthy'
    ];

} catch (Exception $e) {
    $stats = ['total_users' => 0, 'active_users' => 0, 'pending_users' => 0, 'banned_users' => 0];
    $androidStats = ['android_users' => 0, 'total_downloads' => 0];
    $windowsStats = ['windows_users' => 0];
    $moduleStats = ['module_users' => 0, 'total_module_permissions' => 0];
    $expiringStats = ['expiring_soon' => 0];
    $todayStats = ['today_logins' => 0, 'total_sessions' => 0];
    $recentUsers = [];
    $systemHealth = ['database' => 'error', 'permissions' => 'error', 'storage' => 'error'];
}

$adminUsername = $_SESSION['admin_username'] ?? 'Admin';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能管理仪表板 💖 - <?php echo SYSTEM_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <link href="../assets/css/admin-theme.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 仪表板特定样式 */
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }

        /* 仪表板响应式优化 */
        @media (max-width: 576px) {
            .chart-container {
                height: 200px;
            }

            .progress-ring {
                width: 80px;
                height: 80px;
            }

            .progress-ring svg {
                width: 80px;
                height: 80px;
            }

            .stats-card {
                text-align: center;
                padding: 1rem;
            }

            .stats-number {
                font-size: 1.8rem;
            }

            .metric-card {
                margin-bottom: 0.5rem;
            }

            .activity-timeline {
                padding-left: 1rem;
            }

            .timeline-item {
                padding: 0.75rem;
                margin-bottom: 1rem;
            }

            .btn-toolbar {
                flex-direction: column;
                gap: 0.5rem;
            }

            .btn-group {
                width: 100%;
            }

            .btn-group .btn {
                flex: 1;
            }
        }

        @media (max-width: 768px) {
            .chart-container {
                height: 250px;
            }

            .metric-number {
                font-size: 1.5rem;
            }
        }

        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }

        .progress-ring circle {
            transition: stroke-dasharray 0.5s ease-in-out;
        }

        .metric-card {
            background: var(--card-bg);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-heavy);
        }

        .metric-number {
            font-size: 2rem;
            font-weight: var(--font-weight-bold);
            color: #333;
            margin: 0.5rem 0;
        }

        .metric-label {
            color: #666;
            font-size: 0.9rem;
            font-weight: var(--font-weight-medium);
        }

        .trend-indicator {
            display: inline-flex;
            align-items: center;
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }

        .trend-up {
            color: #28a745;
        }

        .trend-down {
            color: #dc3545;
        }

        .data-visualization {
            background: var(--card-bg);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        .mini-chart {
            height: 60px;
            width: 100%;
        }

        .activity-timeline {
            position: relative;
            padding-left: 2rem;
        }

        .activity-timeline::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--primary-gradient);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 1.5rem;
            background: var(--card-bg);
            padding: 1rem;
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-light);
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -1.75rem;
            top: 1rem;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--primary-gradient);
            border: 3px solid white;
            box-shadow: 0 0 0 3px var(--primary-gradient);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <?php include '../includes/sidebar.php'; ?>
            
            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 顶部导航 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2 animate__animated animate__fadeInDown">
                        <i class="bi bi-speedometer2 text-primary"></i>
                        智能管理仪表板 💖
                        <small class="text-muted fs-6">欢迎回来，<?php echo htmlspecialchars($adminUsername); ?></small>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-gradient" onclick="refreshDashboard()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新数据
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="exportReport()">
                                <i class="bi bi-download"></i> 导出报告
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="stats-card animate__animated animate__fadeInUp">
                            <div class="stats-number"><?php echo $stats['total_users']; ?></div>
                            <div><i class="bi bi-people"></i> 总用户数</div>
                            <small>活跃: <?php echo $stats['active_users']; ?> | 待审: <?php echo $stats['pending_users']; ?></small>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="stats-card success animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                            <div class="stats-number"><?php echo $androidStats['android_users']; ?></div>
                            <div><i class="bi bi-phone"></i> Android用户</div>
                            <small>总下载: <?php echo number_format($androidStats['total_downloads']); ?></small>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="stats-card warning animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                            <div class="stats-number"><?php echo $windowsStats['windows_users']; ?></div>
                            <div><i class="bi bi-windows"></i> Windows用户</div>
                            <small>模块权限: <?php echo $moduleStats['total_module_permissions']; ?></small>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="stats-card <?php echo $expiringStats['expiring_soon'] > 0 ? 'danger pulse' : 'info'; ?> animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                            <div class="stats-number"><?php echo $expiringStats['expiring_soon']; ?></div>
                            <div><i class="bi bi-clock-history"></i> 即将过期</div>
                            <small>7天内到期权限</small>
                        </div>
                    </div>
                </div>

                <!-- 系统状态和数据可视化 -->
                <div class="row mb-4">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-activity"></i>
                                    系统状态监控 & 数据分析
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6 class="mb-3">系统健康状态</h6>
                                        <div class="d-flex align-items-center mb-3">
                                            <span class="health-indicator health-<?php echo $systemHealth['database']; ?>"></span>
                                            <strong>数据库</strong>
                                            <span class="ms-auto badge bg-success">在线</span>
                                        </div>
                                        <div class="d-flex align-items-center mb-3">
                                            <span class="health-indicator health-<?php echo $systemHealth['permissions']; ?>"></span>
                                            <strong>权限系统</strong>
                                            <span class="ms-auto badge bg-success">正常</span>
                                        </div>
                                        <div class="d-flex align-items-center mb-3">
                                            <span class="health-indicator health-<?php echo $systemHealth['storage']; ?>"></span>
                                            <strong>文件存储</strong>
                                            <span class="ms-auto badge bg-success">健康</span>
                                        </div>

                                        <!-- 系统负载环形图 -->
                                        <div class="text-center mt-4">
                                            <h6>系统负载</h6>
                                            <div class="progress-ring">
                                                <svg width="120" height="120">
                                                    <circle cx="60" cy="60" r="50" stroke="#e9ecef" stroke-width="8" fill="none"/>
                                                    <circle cx="60" cy="60" r="50" stroke="url(#gradient)" stroke-width="8" fill="none"
                                                            stroke-dasharray="314" stroke-dashoffset="251" stroke-linecap="round" id="loadCircle"/>
                                                    <defs>
                                                        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                                            <stop offset="0%" style="stop-color:#667eea"/>
                                                            <stop offset="100%" style="stop-color:#764ba2"/>
                                                        </linearGradient>
                                                    </defs>
                                                </svg>
                                                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                                    <strong id="loadPercentage">20%</strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <h6>用户活动趋势</h6>
                                        <div class="chart-container">
                                            <canvas id="activityChart"></canvas>
                                        </div>

                                        <div class="row text-center mt-3">
                                            <div class="col-4">
                                                <div class="metric-card">
                                                    <div class="metric-number"><?php echo $todayStats['today_logins']; ?></div>
                                                    <div class="metric-label">今日登录</div>
                                                    <div class="trend-indicator trend-up">
                                                        <i class="bi bi-arrow-up"></i> +12%
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="metric-card">
                                                    <div class="metric-number"><?php echo $todayStats['total_sessions']; ?></div>
                                                    <div class="metric-label">活跃会话</div>
                                                    <div class="trend-indicator trend-up">
                                                        <i class="bi bi-arrow-up"></i> +8%
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="metric-card">
                                                    <div class="metric-number"><?php echo $stats['pending_users']; ?></div>
                                                    <div class="metric-label">待处理</div>
                                                    <div class="trend-indicator trend-down">
                                                        <i class="bi bi-arrow-down"></i> -5%
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-lightning-charge"></i>
                                    快速操作
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="../users/pending.php" class="btn btn-primary">
                                        <i class="bi bi-person-plus"></i> 审批新用户
                                        <?php if ($stats['pending_users'] > 0): ?>
                                            <span class="badge bg-light text-primary ms-2"><?php echo $stats['pending_users']; ?></span>
                                        <?php endif; ?>
                                    </a>
                                    <a href="../permissions/advanced_permissions.php" class="btn btn-success">
                                        <i class="bi bi-stars"></i> 智能权限管理
                                    </a>
                                    <a href="../users/users.php" class="btn btn-info">
                                        <i class="bi bi-people"></i> 用户管理
                                    </a>
                                    <a href="../monitoring/security.php" class="btn btn-warning">
                                        <i class="bi bi-shield-check"></i> 安全监控
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近活动和用户 -->
                <div class="row mb-4">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-clock-history"></i>
                                    最近注册用户
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="recent-activity">
                                    <?php if (!empty($recentUsers)): ?>
                                        <?php foreach ($recentUsers as $user): ?>
                                        <div class="activity-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                                </div>
                                                <small class="text-muted">
                                                    <?php echo date('m-d H:i', strtotime($user['created_at'])); ?>
                                                </small>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="text-center text-muted py-4">
                                            <i class="bi bi-inbox fs-1"></i>
                                            <p>暂无最近注册用户</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-pie-chart"></i>
                                    权限分布统计
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="chart-container" style="height: 200px;">
                                            <canvas id="permissionChart"></canvas>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="row text-center">
                                            <div class="col-12 mb-3">
                                                <div class="metric-card">
                                                    <div class="metric-number"><?php echo count($permissionManager->getAvailableModules()); ?></div>
                                                    <div class="metric-label">可用模块</div>
                                                </div>
                                            </div>
                                            <div class="col-12 mb-3">
                                                <div class="metric-card">
                                                    <div class="metric-number"><?php echo $moduleStats['module_users']; ?></div>
                                                    <div class="metric-label">模块用户</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 详细统计 -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h6 class="mb-3">平台分布</h6>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span><i class="bi bi-phone text-success"></i> Android用户</span>
                                            <span class="fw-bold"><?php echo $androidStats['android_users']; ?> (<?php echo $stats['total_users'] > 0 ? round($androidStats['android_users'] / $stats['total_users'] * 100, 1) : 0; ?>%)</span>
                                        </div>
                                        <div class="progress mb-3" style="height: 8px;">
                                            <div class="progress-bar bg-success" style="width: <?php echo $stats['total_users'] > 0 ? ($androidStats['android_users'] / $stats['total_users'] * 100) : 0; ?>%"></div>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span><i class="bi bi-windows text-primary"></i> Windows用户</span>
                                            <span class="fw-bold"><?php echo $windowsStats['windows_users']; ?> (<?php echo $stats['total_users'] > 0 ? round($windowsStats['windows_users'] / $stats['total_users'] * 100, 1) : 0; ?>%)</span>
                                        </div>
                                        <div class="progress mb-3" style="height: 8px;">
                                            <div class="progress-bar bg-primary" style="width: <?php echo $stats['total_users'] > 0 ? ($windowsStats['windows_users'] / $stats['total_users'] * 100) : 0; ?>%"></div>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span><i class="bi bi-download text-info"></i> 总下载量</span>
                                            <span class="fw-bold"><?php echo number_format($androidStats['total_downloads']); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统公告 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-megaphone"></i>
                                    系统公告 & 提醒
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php if ($expiringStats['expiring_soon'] > 0): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="alert alert-warning">
                                            <i class="bi bi-exclamation-triangle"></i>
                                            <strong>权限即将过期提醒</strong>
                                            <p class="mb-0">有 <?php echo $expiringStats['expiring_soon']; ?> 个权限将在7天内过期，请及时处理。</p>
                                            <a href="advanced_permissions.php?filter=expiring" class="btn btn-sm btn-warning mt-2">立即查看</a>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if ($stats['pending_users'] > 0): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="alert alert-info">
                                            <i class="bi bi-person-plus"></i>
                                            <strong>待审核用户</strong>
                                            <p class="mb-0">有 <?php echo $stats['pending_users']; ?> 个用户等待审核。</p>
                                            <a href="pending.php" class="btn btn-sm btn-info mt-2">去审核</a>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <div class="col-md-6 mb-3">
                                        <div class="alert alert-success">
                                            <i class="bi bi-check-circle"></i>
                                            <strong>系统运行正常</strong>
                                            <p class="mb-0">所有核心服务运行正常，数据库连接稳定。</p>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <div class="alert alert-primary">
                                            <i class="bi bi-lightbulb"></i>
                                            <strong>智能提示</strong>
                                            <p class="mb-0">建议定期备份数据库，当前最新备份时间：<?php echo date('Y-m-d H:i'); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/theme-switcher.js"></script>

    <!-- 自定义JavaScript -->
    <script>
        // 图表配置
        const chartConfig = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                }
            }
        };

        // 初始化活动趋势图表
        function initActivityChart() {
            const ctx = document.getElementById('activityChart').getContext('2d');

            // 模拟7天的数据
            const labels = [];
            const loginData = [];
            const sessionData = [];

            for (let i = 6; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
                loginData.push(Math.floor(Math.random() * 50) + 20);
                sessionData.push(Math.floor(Math.random() * 80) + 30);
            }

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '登录次数',
                        data: loginData,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '活跃会话',
                        data: sessionData,
                        borderColor: '#4facfe',
                        backgroundColor: 'rgba(79, 172, 254, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    ...chartConfig,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // 初始化权限分布饼图
        function initPermissionChart() {
            const ctx = document.getElementById('permissionChart').getContext('2d');

            const androidUsers = <?php echo $androidStats['android_users']; ?>;
            const windowsUsers = <?php echo $windowsStats['windows_users']; ?>;
            const totalUsers = <?php echo $stats['total_users']; ?>;
            const otherUsers = totalUsers - androidUsers - windowsUsers;

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Android用户', 'Windows用户', '其他用户'],
                    datasets: [{
                        data: [androidUsers, windowsUsers, otherUsers],
                        backgroundColor: [
                            '#4facfe',
                            '#667eea',
                            '#fa709a'
                        ],
                        borderWidth: 0,
                        hoverOffset: 10
                    }]
                },
                options: {
                    ...chartConfig,
                    cutout: '60%',
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: {
                                    size: 12
                                }
                            }
                        }
                    }
                }
            });
        }

        // 更新系统负载环形图
        function updateLoadRing() {
            const circle = document.getElementById('loadCircle');
            const percentage = document.getElementById('loadPercentage');

            // 模拟负载数据
            const load = Math.floor(Math.random() * 30) + 15; // 15-45%
            const circumference = 2 * Math.PI * 50; // 半径50
            const offset = circumference - (load / 100) * circumference;

            circle.style.strokeDashoffset = offset;
            percentage.textContent = load + '%';

            // 根据负载调整颜色
            if (load < 30) {
                circle.style.stroke = '#28a745';
            } else if (load < 60) {
                circle.style.stroke = '#ffc107';
            } else {
                circle.style.stroke = '#dc3545';
            }
        }

        // 刷新仪表板数据
        function refreshDashboard() {
            // 添加加载动画
            const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
            const originalText = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 刷新中...';
            refreshBtn.disabled = true;

            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // 导出报告
        function exportReport() {
            const reportData = {
                timestamp: new Date().toISOString(),
                stats: {
                    totalUsers: <?php echo $stats['total_users']; ?>,
                    activeUsers: <?php echo $stats['active_users']; ?>,
                    pendingUsers: <?php echo $stats['pending_users']; ?>,
                    androidUsers: <?php echo $androidStats['android_users']; ?>,
                    windowsUsers: <?php echo $windowsStats['windows_users']; ?>,
                    totalDownloads: <?php echo $androidStats['total_downloads']; ?>,
                    expiringPermissions: <?php echo $expiringStats['expiring_soon']; ?>
                }
            };

            const blob = new Blob([JSON.stringify(reportData, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `dashboard-report-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 实时时间更新
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN');
            document.title = `智能管理仪表板 💖 - ${timeString}`;
        }

        // 添加旋转动画
        const style = document.createElement('style');
        style.textContent = `
            .spin {
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initActivityChart();
            initPermissionChart();
            updateLoadRing();

            // 添加加载完成的动画效果
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });

            // 每分钟更新一次时间
            setInterval(updateTime, 60000);

            // 每30秒更新一次负载环形图
            setInterval(updateLoadRing, 30000);
        });

        // 自动刷新数据（每5分钟）
        setInterval(function() {
            // 可以在这里添加AJAX请求来更新数据而不刷新整个页面
            console.log('自动检查数据更新...');
        }, 300000); // 5分钟
    </script>
</body>
</html>
