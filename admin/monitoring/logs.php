<?php
/**
 * 操作日志页面
 * 管理系统 - management.djxs.xyz
 */

require_once __DIR__ . '/../../includes/admin.php';

$admin = new Admin();

// 检查登录状态
if (!$admin->checkLogin()) {
    header('Location: ../auth/login.php');
    exit();
}

// 获取筛选参数
$page = intval($_GET['page'] ?? 1);
$adminId = intval($_GET['admin_id'] ?? 0) ?: null;
$action = trim($_GET['action'] ?? '');
$userId = intval($_GET['user_id'] ?? 0) ?: null;
$limit = 50;

// 获取操作日志
$logsResult = $admin->getOperationLogs($page, $limit, $adminId, $action, $userId);
$logs = $logsResult['success'] ? $logsResult['logs'] : [];
$total = $logsResult['total'] ?? 0;
$totalPages = ceil($total / $limit);

// 获取管理员列表（用于筛选）
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $adminSql = "SELECT id, username FROM admin_users WHERE status = 1 ORDER BY username";
    $adminStmt = $conn->prepare($adminSql);
    $adminStmt->execute();
    $adminList = $adminStmt->fetchAll();
} catch (Exception $e) {
    $adminList = [];
}

// 操作类型列表
$actionTypes = [
    'approve_user' => '审批用户',
    'reject_user' => '拒绝用户',
    'ban_user' => '封禁用户',
    'unban_user' => '解封用户',
    'admin_login' => '管理员登录',
    'user_login' => '用户登录',
    'user_register' => '用户注册'
];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志 - <?php echo SYSTEM_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .log-item {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.3s;
        }
        .log-item:hover {
            background-color: #f8f9fa;
        }
        .log-item:last-child {
            border-bottom: none;
        }
        .action-badge {
            font-size: 0.8em;
            padding: 0.3em 0.6em;
            border-radius: 4px;
        }
        .action-approve { background: #d4edda; color: #155724; }
        .action-reject { background: #f8d7da; color: #721c24; }
        .action-ban { background: #f8d7da; color: #721c24; }
        .action-unban { background: #d4edda; color: #155724; }
        .action-login { background: #cce7ff; color: #004085; }
        .action-register { background: #fff3cd; color: #856404; }
        .filter-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <?php
            // 获取统计数据用于侧边栏
            $stats = ['pending_users' => 0];
            try {
                // 使用admin对象的数据库连接
                $conn = $admin->getConnection();
                $pendingSql = "SELECT COUNT(*) as pending_users FROM pending_users WHERE status = 'pending'";
                $pendingStmt = $conn->prepare($pendingSql);
                $pendingStmt->execute();
                $stats = $pendingStmt->fetch();
            } catch (Exception $e) {
                // 忽略错误，使用默认值
            }
            include '../includes/sidebar.php';
            ?>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-journal-text"></i>
                        操作日志
                        <span class="badge bg-primary ms-2"><?php echo $total; ?></span>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>

                <!-- 筛选器 -->
                <div class="filter-card">
                    <form method="GET" action="" class="row g-3">
                        <div class="col-md-3">
                            <label for="admin_id" class="form-label">管理员</label>
                            <select class="form-select" name="admin_id" id="admin_id">
                                <option value="">全部管理员</option>
                                <?php foreach ($adminList as $adminUser): ?>
                                    <option value="<?php echo $adminUser['id']; ?>" 
                                            <?php echo $adminId == $adminUser['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($adminUser['username']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="action" class="form-label">操作类型</label>
                            <select class="form-select" name="action" id="action">
                                <option value="">全部操作</option>
                                <?php foreach ($actionTypes as $actionKey => $actionName): ?>
                                    <option value="<?php echo $actionKey; ?>" 
                                            <?php echo $action === $actionKey ? 'selected' : ''; ?>>
                                        <?php echo $actionName; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="user_id" class="form-label">用户ID</label>
                            <input type="number" class="form-control" name="user_id" id="user_id" 
                                   placeholder="输入用户ID" value="<?php echo $userId ?: ''; ?>">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-search"></i> 筛选
                            </button>
                            <a href="logs.php" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> 清除
                            </a>
                        </div>
                    </form>
                </div>

                <!-- 日志列表 -->
                <div class="card">
                    <div class="card-body p-0">
                        <?php if (empty($logs)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-inbox display-1 text-muted"></i>
                                <h3 class="mt-3 text-muted">暂无日志记录</h3>
                                <p class="text-muted">没有找到符合条件的操作日志</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($logs as $log): ?>
                                <div class="log-item">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-person-circle me-2 text-primary"></i>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($log['admin_username'] ?: '系统'); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo date('m-d H:i', strtotime($log['created_at'])); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <?php
                                            $actionClass = 'action-' . str_replace('_', '-', $log['action']);
                                            $actionText = $actionTypes[$log['action']] ?? $log['action'];
                                            ?>
                                            <span class="action-badge <?php echo $actionClass; ?>">
                                                <?php echo $actionText; ?>
                                            </span>
                                        </div>
                                        <div class="col-md-4">
                                            <div>
                                                <?php if ($log['target_username']): ?>
                                                    <strong>目标用户：</strong>
                                                    <a href="../users/user_detail.php?id=<?php echo $log['user_id']; ?>" class="text-decoration-none">
                                                        <?php echo htmlspecialchars($log['target_username']); ?>
                                                    </a>
                                                    <br>
                                                <?php endif; ?>
                                                <span class="text-muted"><?php echo htmlspecialchars($log['description']); ?></span>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted">
                                                <i class="bi bi-geo-alt"></i>
                                                <?php echo htmlspecialchars($log['ip_address']); ?>
                                            </small>
                                        </div>
                                        <div class="col-md-2 text-end">
                                            <small class="text-muted">
                                                <?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 分页 -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="分页导航" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&admin_id=<?php echo $adminId; ?>&action=<?php echo $action; ?>&user_id=<?php echo $userId; ?>">上一页</a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&admin_id=<?php echo $adminId; ?>&action=<?php echo $action; ?>&user_id=<?php echo $userId; ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&admin_id=<?php echo $adminId; ?>&action=<?php echo $action; ?>&user_id=<?php echo $userId; ?>">下一页</a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>

                <!-- 统计信息 -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <h5 class="text-primary"><?php echo $total; ?></h5>
                                        <small class="text-muted">总日志数</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h5 class="text-success"><?php echo count(array_filter($logs, function($log) { return strpos($log['action'], 'approve') !== false; })); ?></h5>
                                        <small class="text-muted">本页审批操作</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h5 class="text-warning"><?php echo count(array_filter($logs, function($log) { return strpos($log['action'], 'ban') !== false; })); ?></h5>
                                        <small class="text-muted">本页封禁操作</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h5 class="text-info"><?php echo count(array_filter($logs, function($log) { return strpos($log['action'], 'login') !== false; })); ?></h5>
                                        <small class="text-muted">本页登录记录</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
