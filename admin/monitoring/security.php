<?php
/**
 * 安全监控页面
 * 管理系统 - management.djxs.xyz
 */

require_once __DIR__ . '/../../includes/admin.php';
require_once __DIR__ . '/../../includes/security.php';

$admin = new Admin();
$security = new SecurityManager();

// 检查登录状态
if (!$admin->checkLogin()) {
    header('Location: ../auth/login.php');
    exit();
}

session_start();
$adminId = $_SESSION['admin_id'];
$message = '';
$messageType = '';

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'update_config') {
        $configKey = $_POST['config_key'] ?? '';
        $configValue = $_POST['config_value'] ?? '';

        if ($configKey && $configValue !== '') {
            $result = $security->updateConfig($configKey, $configValue, $adminId);
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'danger';
        }
    }

    elseif ($action === 'clear_events') {
        $eventType = $_POST['event_type'] ?? '';
        $severity = $_POST['severity'] ?? '';
        $beforeDate = $_POST['before_date'] ?? '';

        $result = $security->clearSecurityEvents($adminId, $eventType, $severity, $beforeDate);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    }

    elseif ($action === 'resolve_event') {
        $eventId = $_POST['event_id'] ?? '';

        if ($eventId) {
            $result = $security->resolveSecurityEvent($eventId, $adminId);
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'danger';
        }
    }

    elseif ($action === 'batch_resolve') {
        $eventIds = $_POST['event_ids'] ?? [];

        if (!empty($eventIds)) {
            $result = $security->batchResolveEvents($eventIds, $adminId);
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'danger';
        }
    }

    elseif ($action === 'cleanup_ips') {
        $daysBefore = $_POST['days_before'] ?? 30;

        $result = $security->cleanupOldIPs($adminId, $daysBefore);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    }
}

// 获取安全事件
$page = intval($_GET['page'] ?? 1);
$severity = $_GET['severity'] ?? '';
$eventType = $_GET['event_type'] ?? '';
$limit = 20;

$eventsResult = $security->getSecurityEvents($page, $limit, $severity, $eventType);
$events = $eventsResult['success'] ? $eventsResult['events'] : [];
$total = $eventsResult['total'] ?? 0;
$totalPages = ceil($total / $limit);

// 获取系统配置
$configs = $security->getConfigs();

// 事件类型
$eventTypes = [
    'successful_login' => '成功登录',
    'login_blocked' => '登录被阻止',
    'new_ip_login' => '新IP登录',
    'blocked_ip_access' => '被阻止IP访问',
    'auto_ban_multi_ip' => '多IP自动封禁'
];

// 严重级别
$severityLevels = [
    'low' => '低',
    'medium' => '中',
    'high' => '高',
    'critical' => '严重'
];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全监控 - <?php echo SYSTEM_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .severity-low { color: #28a745; }
        .severity-medium { color: #ffc107; }
        .severity-high { color: #fd7e14; }
        .severity-critical { color: #dc3545; }
        .event-item {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.3s;
        }
        .event-item:hover {
            background-color: #f8f9fa;
        }
        .event-item:last-child {
            border-bottom: none;
        }
        .config-item {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .config-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <?php
            // 获取统计数据用于侧边栏
            $stats = ['pending_users' => 0];
            try {
                // 使用admin对象的数据库连接
                $conn = $admin->getConnection();
                $pendingSql = "SELECT COUNT(*) as pending_users FROM pending_users WHERE status = 'pending'";
                $pendingStmt = $conn->prepare($pendingSql);
                $pendingStmt->execute();
                $stats = $pendingStmt->fetch();
            } catch (Exception $e) {
                // 忽略错误，使用默认值
            }
            include '../includes/sidebar.php';
            ?>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-shield-exclamation"></i>
                        安全监控
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="showClearModal()">
                                <i class="bi bi-trash"></i> 清除事件
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="showCleanupModal()">
                                <i class="bi bi-broom"></i> 清理IP
                            </button>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <i class="bi bi-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- 安全配置 -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-gear"></i>
                                    安全配置
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="config-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>IP监控</strong>
                                        <br><small class="text-muted">启用IP地址监控</small>
                                    </div>
                                    <div>
                                        <span class="badge bg-<?php echo $configs['ip_check_enabled'] ? 'success' : 'danger'; ?>">
                                            <?php echo $configs['ip_check_enabled'] ? '启用' : '禁用'; ?>
                                        </span>
                                    </div>
                                </div>

                                <div class="config-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>自动封禁</strong>
                                        <br><small class="text-muted">检测到异常时自动封禁</small>
                                    </div>
                                    <div>
                                        <span class="badge bg-<?php echo $configs['auto_ban_enabled'] ? 'success' : 'danger'; ?>">
                                            <?php echo $configs['auto_ban_enabled'] ? '启用' : 'danger'; ?>
                                        </span>
                                    </div>
                                </div>

                                <div class="config-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>最大IP数</strong>
                                        <br><small class="text-muted">每用户允许的IP数量</small>
                                    </div>
                                    <div>
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="showConfigModal('max_ip_per_user', '<?php echo $configs['max_ip_per_user']; ?>')">
                                            <?php echo $configs['max_ip_per_user']; ?>
                                        </button>
                                    </div>
                                </div>

                                <div class="config-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>检查窗口</strong>
                                        <br><small class="text-muted">IP检查时间窗口（小时）</small>
                                    </div>
                                    <div>
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="showConfigModal('ip_check_window', '<?php echo $configs['ip_check_window']; ?>')">
                                            <?php echo $configs['ip_check_window']; ?>h
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 快速统计 -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-graph-up"></i>
                                    安全统计
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h4 class="text-danger"><?php echo count(array_filter($events, function($e) { return $e['severity'] === 'critical'; })); ?></h4>
                                        <small class="text-muted">严重事件</small>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-warning"><?php echo count(array_filter($events, function($e) { return $e['severity'] === 'high'; })); ?></h4>
                                        <small class="text-muted">高危事件</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 安全事件 -->
                    <div class="col-lg-8">
                        <!-- 筛选器 -->
                        <div class="card">
                            <div class="card-body">
                                <form method="GET" action="" class="row g-3">
                                    <div class="col-md-4">
                                        <select class="form-select" name="severity">
                                            <option value="">全部严重级别</option>
                                            <?php foreach ($severityLevels as $level => $name): ?>
                                                <option value="<?php echo $level; ?>" <?php echo $severity === $level ? 'selected' : ''; ?>>
                                                    <?php echo $name; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <select class="form-select" name="event_type">
                                            <option value="">全部事件类型</option>
                                            <?php foreach ($eventTypes as $type => $name): ?>
                                                <option value="<?php echo $type; ?>" <?php echo $eventType === $type ? 'selected' : ''; ?>>
                                                    <?php echo $name; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="bi bi-search"></i> 筛选
                                        </button>
                                        <a href="security.php" class="btn btn-outline-secondary">
                                            <i class="bi bi-x-circle"></i> 清除
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- 事件列表 -->
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    安全事件
                                    <span class="badge bg-primary ms-2"><?php echo $total; ?></span>
                                </h5>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllEvents()">
                                        <i class="bi bi-check-all"></i> 全选
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="batchResolve()">
                                        <i class="bi bi-check-circle"></i> 批量处理
                                    </button>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <?php if (empty($events)): ?>
                                    <div class="text-center py-5">
                                        <i class="bi bi-shield-check display-1 text-success"></i>
                                        <h3 class="mt-3 text-muted">暂无安全事件</h3>
                                        <p class="text-muted">系统运行正常</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($events as $event): ?>
                                        <div class="event-item">
                                            <div class="row align-items-center">
                                                <div class="col-md-2">
                                                    <?php
                                                    $severityClass = 'severity-' . $event['severity'];
                                                    $severityIcon = [
                                                        'low' => 'info-circle',
                                                        'medium' => 'exclamation-circle',
                                                        'high' => 'exclamation-triangle',
                                                        'critical' => 'x-octagon'
                                                    ][$event['severity']] ?? 'info-circle';
                                                    ?>
                                                    <span class="<?php echo $severityClass; ?>">
                                                        <i class="bi bi-<?php echo $severityIcon; ?>"></i>
                                                        <?php echo $severityLevels[$event['severity']] ?? $event['severity']; ?>
                                                    </span>
                                                </div>
                                                <div class="col-md-3">
                                                    <strong><?php echo $eventTypes[$event['event_type']] ?? $event['event_type']; ?></strong>
                                                    <?php if ($event['username']): ?>
                                                        <br><small class="text-muted">用户: <?php echo htmlspecialchars($event['username']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="col-md-4">
                                                    <span><?php echo htmlspecialchars($event['description']); ?></span>
                                                    <?php if ($event['ip_address']): ?>
                                                        <br><small class="text-muted">IP: <?php echo htmlspecialchars($event['ip_address']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="col-md-2 text-end">
                                                    <small class="text-muted d-block">
                                                        <?php echo date('Y-m-d H:i:s', strtotime($event['created_at'])); ?>
                                                    </small>
                                                    <?php if (!$event['is_resolved']): ?>
                                                        <button class="btn btn-sm btn-outline-success mt-1"
                                                                onclick="resolveEvent(<?php echo $event['id']; ?>)"
                                                                title="标记为已处理">
                                                            <i class="bi bi-check"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <span class="badge bg-success mt-1">已处理</span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="col-md-1 text-end">
                                                    <input type="checkbox" class="form-check-input event-checkbox"
                                                           value="<?php echo $event['id']; ?>"
                                                           title="选择此事件">
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- 分页 -->
                        <?php if ($totalPages > 1): ?>
                            <nav aria-label="分页导航">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&severity=<?php echo $severity; ?>&event_type=<?php echo $eventType; ?>">上一页</a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&severity=<?php echo $severity; ?>&event_type=<?php echo $eventType; ?>"><?php echo $i; ?></a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&severity=<?php echo $severity; ?>&event_type=<?php echo $eventType; ?>">下一页</a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 配置修改模态框 -->
    <div class="modal fade" id="configModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">修改配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_config">
                        <input type="hidden" name="config_key" id="configKey">

                        <div class="mb-3">
                            <label for="configValue" class="form-label">配置值</label>
                            <input type="number" class="form-control" name="config_value" id="configValue" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 清除事件模态框 -->
    <div class="modal fade" id="clearModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-trash text-danger"></i>
                        清除安全事件
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="clear_events">

                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>警告：</strong>此操作将永久删除选定的安全事件记录，无法恢复！
                        </div>

                        <div class="mb-3">
                            <label for="clearEventType" class="form-label">事件类型（可选）</label>
                            <select class="form-select" name="event_type" id="clearEventType">
                                <option value="">全部类型</option>
                                <?php foreach ($eventTypes as $type => $name): ?>
                                    <option value="<?php echo $type; ?>"><?php echo $name; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="clearSeverity" class="form-label">严重级别（可选）</label>
                            <select class="form-select" name="severity" id="clearSeverity">
                                <option value="">全部级别</option>
                                <?php foreach ($severityLevels as $level => $name): ?>
                                    <option value="<?php echo $level; ?>"><?php echo $name; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="beforeDate" class="form-label">清除此日期之前的记录（可选）</label>
                            <input type="datetime-local" class="form-control" name="before_date" id="beforeDate">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-danger">确认清除</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 清理IP模态框 -->
    <div class="modal fade" id="cleanupModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-broom text-warning"></i>
                        清理过期IP记录
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="cleanup_ips">

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            此操作将清理指定天数前的非可信IP记录，有助于保持数据库整洁。
                        </div>

                        <div class="mb-3">
                            <label for="daysBefore" class="form-label">清理多少天前的记录</label>
                            <select class="form-select" name="days_before" id="daysBefore">
                                <option value="7">7天前</option>
                                <option value="15">15天前</option>
                                <option value="30" selected>30天前</option>
                                <option value="60">60天前</option>
                                <option value="90">90天前</option>
                            </select>
                            <div class="form-text">只会删除非可信IP记录，可信IP不会被清理</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-warning">开始清理</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showConfigModal(key, value) {
            document.getElementById('configKey').value = key;
            document.getElementById('configValue').value = value;
            new bootstrap.Modal(document.getElementById('configModal')).show();
        }

        function showClearModal() {
            new bootstrap.Modal(document.getElementById('clearModal')).show();
        }

        function showCleanupModal() {
            new bootstrap.Modal(document.getElementById('cleanupModal')).show();
        }

        function resolveEvent(eventId) {
            if (confirm('确定要标记此事件为已处理吗？')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="resolve_event">
                    <input type="hidden" name="event_id" value="${eventId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function selectAllEvents() {
            const checkboxes = document.querySelectorAll('.event-checkbox');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
            });
        }

        function batchResolve() {
            const checkedBoxes = document.querySelectorAll('.event-checkbox:checked');

            if (checkedBoxes.length === 0) {
                alert('请先选择要处理的事件');
                return;
            }

            if (confirm(`确定要批量处理 ${checkedBoxes.length} 个事件吗？`)) {
                const form = document.createElement('form');
                form.method = 'POST';

                let formHTML = '<input type="hidden" name="action" value="batch_resolve">';
                checkedBoxes.forEach(cb => {
                    formHTML += `<input type="hidden" name="event_ids[]" value="${cb.value}">`;
                });

                form.innerHTML = formHTML;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
