<?php
/**
 * 数据库同步检查工具 💖
 * 确保界面显示和数据库数据完全一致
 */

session_start();

// 检查管理员登录
if ((!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) &&
    (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username']))) {
    header('Location: ../auth/login.php');
    exit;
}

require_once '../../api/config/database.php';
require_once '../../includes/module_permission_manager.php';

$database = new Database();
$conn = $database->getConnection();
$permissionManager = new ModulePermissionManager($conn);

// 执行同步检查
$syncResults = [];

// 1. 检查用户权限数据一致性
$sql = "SELECT u.id, u.username, u.email,
        COUNT(DISTINCT CASE WHEN uap.is_active = 1 AND (uap.expires_at IS NULL OR uap.expires_at > NOW()) THEN uap.app_type END) as active_apps,
        COUNT(DISTINCT CASE WHEN ump.is_active = 1 THEN ump.module_code END) as active_modules,
        GROUP_CONCAT(DISTINCT CASE WHEN uap.is_active = 1 AND (uap.expires_at IS NULL OR uap.expires_at > NOW()) THEN uap.app_type END) as app_list,
        GROUP_CONCAT(DISTINCT CASE WHEN ump.is_active = 1 THEN ump.module_code END) as module_list
        FROM users u
        LEFT JOIN user_app_permissions uap ON u.id = uap.user_id
        LEFT JOIN user_module_permissions ump ON u.id = ump.user_id
        WHERE u.status = 'active'
        GROUP BY u.id, u.username, u.email
        ORDER BY u.username";
$stmt = $conn->prepare($sql);
$stmt->execute();
$users = $stmt->fetchAll();

// 2. 检查过期权限清理
$sql = "SELECT COUNT(*) as expired_count FROM user_app_permissions 
        WHERE expires_at IS NOT NULL AND expires_at <= NOW() AND is_active = 1";
$stmt = $conn->prepare($sql);
$stmt->execute();
$expiredPermissions = $stmt->fetch();

// 3. 检查模块代码一致性
$availableModules = $permissionManager->getAvailableModules();
$sql = "SELECT DISTINCT module_code FROM user_module_permissions";
$stmt = $conn->prepare($sql);
$stmt->execute();
$dbModules = $stmt->fetchAll(PDO::FETCH_COLUMN);

$invalidModules = array_diff($dbModules, array_keys($availableModules));

// 4. 检查数据库表结构
$sql = "DESCRIBE user_app_permissions";
$stmt = $conn->prepare($sql);
$stmt->execute();
$appPermissionFields = $stmt->fetchAll();

$sql = "DESCRIBE user_module_permissions";
$stmt = $conn->prepare($sql);
$stmt->execute();
$modulePermissionFields = $stmt->fetchAll();

?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库同步检查 💖 - 管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                    <h1 class="h2">
                        <i class="bi bi-database-check text-primary"></i>
                        数据库同步检查 💖
                    </h1>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="bi bi-arrow-clockwise"></i> 重新检查
                    </button>
                </div>

                <!-- 总体状态 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="status-good"><?php echo count($users); ?></h3>
                                <p class="mb-0">活跃用户</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="<?php echo $expiredPermissions['expired_count'] > 0 ? 'status-warning' : 'status-good'; ?>">
                                    <?php echo $expiredPermissions['expired_count']; ?>
                                </h3>
                                <p class="mb-0">过期权限</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="<?php echo count($invalidModules) > 0 ? 'status-error' : 'status-good'; ?>">
                                    <?php echo count($invalidModules); ?>
                                </h3>
                                <p class="mb-0">无效模块</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="status-good"><?php echo count($availableModules); ?></h3>
                                <p class="mb-0">可用模块</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户权限详情 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-people"></i> 用户权限统计</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>用户ID</th>
                                        <th>用户名</th>
                                        <th>邮箱</th>
                                        <th>应用权限</th>
                                        <th>模块权限</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td><?php echo $user['id']; ?></td>
                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo $user['active_apps']; ?> 个应用</span>
                                            <?php if ($user['app_list']): ?>
                                                <br><small class="text-muted"><?php echo $user['app_list']; ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $user['active_modules']; ?> 个模块</span>
                                            <?php if ($user['module_list']): ?>
                                                <br><small class="text-muted"><?php echo substr($user['module_list'], 0, 50) . (strlen($user['module_list']) > 50 ? '...' : ''); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($user['active_apps'] > 0 || $user['active_modules'] > 0): ?>
                                                <span class="badge bg-success">正常</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">无权限</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 模块检查 -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-puzzle"></i> 可用模块列表</h5>
                            </div>
                            <div class="card-body">
                                <?php foreach ($availableModules as $code => $name): ?>
                                    <span class="badge bg-success me-1 mb-1"><?php echo $code; ?></span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-exclamation-triangle"></i> 数据库检查结果</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($expiredPermissions['expired_count'] > 0): ?>
                                    <div class="alert alert-warning">
                                        <i class="bi bi-clock"></i> 发现 <?php echo $expiredPermissions['expired_count']; ?> 个过期权限需要清理
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (count($invalidModules) > 0): ?>
                                    <div class="alert alert-danger">
                                        <i class="bi bi-x-circle"></i> 发现无效模块代码：
                                        <?php foreach ($invalidModules as $module): ?>
                                            <span class="badge bg-danger"><?php echo $module; ?></span>
                                        <?php endforeach; ?>
                                        <hr>
                                        <a href="../tools/cleanup_invalid_modules.php" class="btn btn-warning btn-sm">
                                            <i class="bi bi-tools"></i> 立即清理无效模块
                                        </a>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($expiredPermissions['expired_count'] == 0 && count($invalidModules) == 0): ?>
                                    <div class="alert alert-success">
                                        <i class="bi bi-check-circle"></i> 数据库状态良好，所有数据同步正常！
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
