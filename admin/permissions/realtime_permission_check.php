<?php
/**
 * 实时权限状态检查工具 💖
 * 确保界面显示和数据库完全同步
 */

session_start();

// 检查管理员登录
if ((!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) &&
    (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username']))) {
    header('Content-Type: application/json');
    echo json_encode(['error' => '未登录']);
    exit;
}

require_once '../../api/config/database.php';
require_once '../../includes/module_permission_manager.php';

$database = new Database();
$conn = $database->getConnection();
$permissionManager = new ModulePermissionManager($conn);

// 如果是AJAX请求，返回JSON数据
if (isset($_GET['ajax']) && $_GET['ajax'] === '1') {
    header('Content-Type: application/json');
    
    $userId = (int)($_GET['user_id'] ?? 0);
    if ($userId <= 0) {
        echo json_encode(['error' => '无效的用户ID']);
        exit;
    }
    
    try {
        // 获取用户基本信息
        $sql = "SELECT username, email FROM users WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if (!$user) {
            echo json_encode(['error' => '用户不存在']);
            exit;
        }
        
        // 获取应用权限
        $sql = "SELECT app_type, is_active, download_count, expires_at, granted_by, notes, created_at, updated_at
                FROM user_app_permissions 
                WHERE user_id = ?
                ORDER BY app_type";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$userId]);
        $appPermissions = $stmt->fetchAll();
        
        // 获取模块权限
        $sql = "SELECT module_code, is_active, granted_by, created_at, updated_at
                FROM user_module_permissions 
                WHERE user_id = ?
                ORDER BY module_code";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$userId]);
        $modulePermissions = $stmt->fetchAll();
        
        // 获取可用模块列表
        $availableModules = $permissionManager->getAvailableModules();
        
        $response = [
            'user' => $user,
            'app_permissions' => $appPermissions,
            'module_permissions' => $modulePermissions,
            'available_modules' => $availableModules,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

// 获取所有用户列表
$sql = "SELECT id, username, email FROM users WHERE status = 'active' ORDER BY username";
$stmt = $conn->prepare($sql);
$stmt->execute();
$users = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时权限检查 💖 - 管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
        .permission-status {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        .status-active { color: #28a745; font-weight: bold; }
        .status-inactive { color: #dc3545; }
        .auto-refresh {
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                    <h1 class="h2">
                        <i class="bi bi-activity text-success"></i>
                        实时权限状态检查 💖
                    </h1>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" onclick="toggleAutoRefresh()">
                            <i class="bi bi-arrow-clockwise" id="refreshIcon"></i> 
                            <span id="refreshText">开启自动刷新</span>
                        </button>
                        <a href="advanced_permissions.php" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回权限管理
                        </a>
                    </div>
                </div>

                <!-- 用户选择 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-person-check"></i> 选择用户检查权限状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <select class="form-select" id="userSelect" onchange="checkUserPermissions()">
                                    <option value="">选择用户...</option>
                                    <?php foreach ($users as $user): ?>
                                        <option value="<?php echo $user['id']; ?>">
                                            <?php echo htmlspecialchars($user['username']); ?> 
                                            (ID: <?php echo $user['id']; ?>, <?php echo htmlspecialchars($user['email']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-primary w-100" onclick="checkUserPermissions()">
                                    <i class="bi bi-search"></i> 检查权限状态
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 权限状态显示 -->
                <div id="permissionResults" style="display: none;">
                    <!-- 用户信息 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="bi bi-person-circle"></i> 用户信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>用户名:</strong> <span id="userName"></span>
                                </div>
                                <div class="col-md-6">
                                    <strong>邮箱:</strong> <span id="userEmail"></span>
                                </div>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">最后检查时间: <span id="lastCheck"></span></small>
                            </div>
                        </div>
                    </div>

                    <!-- 应用权限状态 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="bi bi-app-indicator"></i> 应用权限状态</h5>
                        </div>
                        <div class="card-body">
                            <div id="appPermissions"></div>
                        </div>
                    </div>

                    <!-- 模块权限状态 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="bi bi-puzzle"></i> 模块权限状态</h5>
                        </div>
                        <div class="card-body">
                            <div id="modulePermissions"></div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let autoRefreshInterval = null;
        let isAutoRefresh = false;

        function checkUserPermissions() {
            const userId = document.getElementById('userSelect').value;
            if (!userId) {
                alert('请先选择一个用户！');
                return;
            }

            // 显示加载状态
            document.getElementById('permissionResults').style.display = 'block';
            document.getElementById('userName').textContent = '加载中...';
            document.getElementById('userEmail').textContent = '加载中...';
            document.getElementById('appPermissions').innerHTML = '<div class="text-center"><i class="bi bi-hourglass-split"></i> 正在检查权限状态...</div>';
            document.getElementById('modulePermissions').innerHTML = '<div class="text-center"><i class="bi bi-hourglass-split"></i> 正在检查权限状态...</div>';

            // 发送AJAX请求
            fetch(`?ajax=1&user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('错误: ' + data.error);
                        return;
                    }

                    // 更新用户信息
                    document.getElementById('userName').textContent = data.user.username;
                    document.getElementById('userEmail').textContent = data.user.email;
                    document.getElementById('lastCheck').textContent = data.timestamp;

                    // 更新应用权限
                    let appHtml = '<div class="table-responsive"><table class="table table-striped"><thead><tr><th>应用类型</th><th>状态</th><th>下载次数</th><th>过期时间</th><th>授权者</th><th>更新时间</th></tr></thead><tbody>';
                    
                    if (data.app_permissions.length === 0) {
                        appHtml += '<tr><td colspan="6" class="text-center text-muted">无应用权限</td></tr>';
                    } else {
                        data.app_permissions.forEach(perm => {
                            const statusClass = perm.is_active == 1 ? 'status-active' : 'status-inactive';
                            const statusText = perm.is_active == 1 ? '✅ 活跃' : '❌ 非活跃';
                            const expiresAt = perm.expires_at || '永不过期';
                            const grantedBy = perm.granted_by || '未知';
                            
                            appHtml += `<tr>
                                <td><i class="bi bi-${perm.app_type === 'android' ? 'phone' : 'windows'}"></i> ${perm.app_type}</td>
                                <td class="${statusClass}">${statusText}</td>
                                <td>${perm.download_count}</td>
                                <td>${expiresAt}</td>
                                <td>${grantedBy}</td>
                                <td>${perm.updated_at}</td>
                            </tr>`;
                        });
                    }
                    appHtml += '</tbody></table></div>';
                    document.getElementById('appPermissions').innerHTML = appHtml;

                    // 更新模块权限
                    let moduleHtml = '<div class="row">';
                    
                    if (data.module_permissions.length === 0) {
                        moduleHtml += '<div class="col-12 text-center text-muted">无模块权限</div>';
                    } else {
                        data.module_permissions.forEach(perm => {
                            const statusClass = perm.is_active == 1 ? 'status-active' : 'status-inactive';
                            const statusIcon = perm.is_active == 1 ? '✅' : '❌';
                            const moduleName = data.available_modules[perm.module_code] || perm.module_code;
                            const grantedBy = perm.granted_by || '未知';
                            
                            moduleHtml += `<div class="col-md-6 mb-2">
                                <div class="card">
                                    <div class="card-body p-2">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong class="${statusClass}">${statusIcon} ${moduleName}</strong>
                                                <br><small class="text-muted">代码: ${perm.module_code}</small>
                                            </div>
                                            <div class="text-end">
                                                <small class="text-muted">授权者: ${grantedBy}</small>
                                                <br><small class="text-muted">${perm.updated_at}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>`;
                        });
                    }
                    moduleHtml += '</div>';
                    document.getElementById('modulePermissions').innerHTML = moduleHtml;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('检查权限时发生错误: ' + error.message);
                });
        }

        function toggleAutoRefresh() {
            const refreshIcon = document.getElementById('refreshIcon');
            const refreshText = document.getElementById('refreshText');
            
            if (isAutoRefresh) {
                // 停止自动刷新
                clearInterval(autoRefreshInterval);
                isAutoRefresh = false;
                refreshIcon.classList.remove('auto-refresh');
                refreshText.textContent = '开启自动刷新';
            } else {
                // 开始自动刷新
                if (!document.getElementById('userSelect').value) {
                    alert('请先选择一个用户！');
                    return;
                }
                
                autoRefreshInterval = setInterval(checkUserPermissions, 3000); // 每3秒刷新一次
                isAutoRefresh = true;
                refreshIcon.classList.add('auto-refresh');
                refreshText.textContent = '停止自动刷新';
            }
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>
