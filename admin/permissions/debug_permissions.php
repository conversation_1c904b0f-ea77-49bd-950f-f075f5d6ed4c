<?php
/**
 * 权限调试工具 💖
 * 用于调试Windows权限授予问题
 */

session_start();

// 检查管理员登录
if ((!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) &&
    (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username']))) {
    header('Location: ../auth/login.php');
    exit;
}

require_once '../../api/config/database.php';
require_once '../../includes/module_permission_manager.php';

$database = new Database();
$conn = $database->getConnection();
$permissionManager = new ModulePermissionManager($conn);

$debugResults = [];
$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $userId = (int)($_POST['user_id'] ?? 0);
    $adminId = $_SESSION['admin_id'] ?? 1;
    
    try {
        if ($action === 'test_windows_grant') {
            $debugResults[] = "=== 开始测试Windows权限授予 ===";
            $debugResults[] = "用户ID: {$userId}";
            $debugResults[] = "管理员ID: {$adminId}";
            
            // 1. 检查用户是否存在
            $sql = "SELECT username FROM users WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            if (!$user) {
                $debugResults[] = "❌ 错误: 用户不存在";
            } else {
                $debugResults[] = "✅ 用户存在: " . $user['username'];
                
                // 2. 检查现有Windows权限
                $sql = "SELECT * FROM user_app_permissions WHERE user_id = ? AND app_type = 'windows'";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$userId]);
                $existingPermission = $stmt->fetch();
                
                if ($existingPermission) {
                    $debugResults[] = "📋 现有Windows权限: ";
                    $debugResults[] = "  - ID: " . $existingPermission['id'];
                    $debugResults[] = "  - 是否活跃: " . ($existingPermission['is_active'] ? '是' : '否');
                    $debugResults[] = "  - 下载次数: " . $existingPermission['download_count'];
                    $debugResults[] = "  - 过期时间: " . ($existingPermission['expires_at'] ?: '永不过期');
                    $debugResults[] = "  - 授权者: " . ($existingPermission['granted_by'] ?: '未知');
                } else {
                    $debugResults[] = "📋 无现有Windows权限";
                }
                
                // 3. 尝试授予Windows权限
                $debugResults[] = "🔧 开始授予Windows权限...";
                $result = $permissionManager->grantAppAccess($userId, 'windows', 0, null, $adminId, '调试测试');
                
                if ($result['success']) {
                    $debugResults[] = "✅ 权限授予成功: " . $result['message'];
                    
                    // 4. 验证权限是否真的被授予
                    $sql = "SELECT * FROM user_app_permissions WHERE user_id = ? AND app_type = 'windows'";
                    $stmt = $conn->prepare($sql);
                    $stmt->execute([$userId]);
                    $newPermission = $stmt->fetch();
                    
                    if ($newPermission) {
                        $debugResults[] = "✅ 验证成功，权限已存在数据库:";
                        $debugResults[] = "  - ID: " . $newPermission['id'];
                        $debugResults[] = "  - 是否活跃: " . ($newPermission['is_active'] ? '是' : '否');
                        $debugResults[] = "  - 下载次数: " . $newPermission['download_count'];
                        $debugResults[] = "  - 过期时间: " . ($newPermission['expires_at'] ?: '永不过期');
                        $debugResults[] = "  - 授权者: " . ($newPermission['granted_by'] ?: '未知');
                        $debugResults[] = "  - 创建时间: " . $newPermission['created_at'];
                        $debugResults[] = "  - 更新时间: " . $newPermission['updated_at'];
                    } else {
                        $debugResults[] = "❌ 验证失败，权限未在数据库中找到";
                    }
                } else {
                    $debugResults[] = "❌ 权限授予失败: " . $result['message'];
                }
                
                // 5. 检查用户的所有权限
                $debugResults[] = "📊 用户所有应用权限:";
                $sql = "SELECT app_type, is_active, download_count, expires_at, granted_by, created_at 
                        FROM user_app_permissions WHERE user_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$userId]);
                $allPermissions = $stmt->fetchAll();
                
                foreach ($allPermissions as $perm) {
                    $debugResults[] = "  - {$perm['app_type']}: " . 
                                     ($perm['is_active'] ? '活跃' : '非活跃') . 
                                     ", 下载次数: {$perm['download_count']}" .
                                     ", 过期: " . ($perm['expires_at'] ?: '永不') .
                                     ", 创建: {$perm['created_at']}";
                }
            }
            
            $message = "调试测试完成，请查看详细结果";
        }
        
    } catch (Exception $e) {
        $debugResults[] = "❌ 异常错误: " . $e->getMessage();
        $debugResults[] = "错误文件: " . $e->getFile() . " 行号: " . $e->getLine();
        $message = "调试过程中发生错误";
    }
}

// 获取所有用户列表
$sql = "SELECT id, username, email FROM users WHERE status = 'active' ORDER BY username";
$stmt = $conn->prepare($sql);
$stmt->execute();
$users = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限调试工具 💖 - 管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
        .debug-output {
            background: #1e1e1e;
            color: #f8f8f2;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
            max-height: 500px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                    <h1 class="h2">
                        <i class="bi bi-bug text-warning"></i>
                        权限调试工具 💖
                    </h1>
                    <a href="advanced_permissions.php" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left"></i> 返回权限管理
                    </a>
                </div>

                <!-- 消息提示 -->
                <?php if ($message): ?>
                <div class="alert alert-info alert-dismissible fade show">
                    <i class="bi bi-info-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- 调试工具 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-tools"></i> Windows权限授予调试</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="test_windows_grant">
                            <div class="row">
                                <div class="col-md-8">
                                    <label class="form-label">选择用户进行调试</label>
                                    <select name="user_id" class="form-select" required>
                                        <option value="">选择用户...</option>
                                        <?php foreach ($users as $user): ?>
                                            <option value="<?php echo $user['id']; ?>">
                                                <?php echo htmlspecialchars($user['username']); ?> 
                                                (ID: <?php echo $user['id']; ?>, <?php echo htmlspecialchars($user['email']); ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" class="btn btn-warning w-100">
                                        <i class="bi bi-bug"></i> 开始调试
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 调试结果 -->
                <?php if (!empty($debugResults)): ?>
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-terminal"></i> 调试输出</h5>
                    </div>
                    <div class="card-body">
                        <div class="debug-output p-3 rounded">
                            <?php foreach ($debugResults as $line): ?>
                                <div><?php echo htmlspecialchars($line); ?></div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- 快速数据库查询 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="bi bi-database"></i> 快速数据库查询</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>所有Windows权限</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>用户ID</th>
                                                <th>用户名</th>
                                                <th>状态</th>
                                                <th>过期时间</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            $sql = "SELECT uap.user_id, u.username, uap.is_active, uap.expires_at
                                                    FROM user_app_permissions uap
                                                    JOIN users u ON uap.user_id = u.id
                                                    WHERE uap.app_type = 'windows'
                                                    ORDER BY u.username";
                                            $stmt = $conn->prepare($sql);
                                            $stmt->execute();
                                            $windowsPermissions = $stmt->fetchAll();
                                            
                                            foreach ($windowsPermissions as $perm):
                                            ?>
                                            <tr>
                                                <td><?php echo $perm['user_id']; ?></td>
                                                <td><?php echo htmlspecialchars($perm['username']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $perm['is_active'] ? 'success' : 'secondary'; ?>">
                                                        <?php echo $perm['is_active'] ? '活跃' : '非活跃'; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo $perm['expires_at'] ?: '永不过期'; ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>权限统计</h6>
                                <?php
                                $sql = "SELECT 
                                        COUNT(CASE WHEN app_type = 'android' AND is_active = 1 THEN 1 END) as android_active,
                                        COUNT(CASE WHEN app_type = 'windows' AND is_active = 1 THEN 1 END) as windows_active,
                                        COUNT(CASE WHEN app_type = 'android' AND is_active = 0 THEN 1 END) as android_inactive,
                                        COUNT(CASE WHEN app_type = 'windows' AND is_active = 0 THEN 1 END) as windows_inactive
                                        FROM user_app_permissions";
                                $stmt = $conn->prepare($sql);
                                $stmt->execute();
                                $stats = $stmt->fetch();
                                ?>
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>活跃Android权限</span>
                                        <span class="badge bg-success"><?php echo $stats['android_active']; ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>活跃Windows权限</span>
                                        <span class="badge bg-success"><?php echo $stats['windows_active']; ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>非活跃Android权限</span>
                                        <span class="badge bg-secondary"><?php echo $stats['android_inactive']; ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>非活跃Windows权限</span>
                                        <span class="badge bg-secondary"><?php echo $stats['windows_inactive']; ?></span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
