<?php
/**
 * 高级权限管理系统 - 超级智能版本 💖
 * 支持授权时长、权限概览、批量操作等高级功能
 */

session_start();

// 检查管理员登录
if ((!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) &&
    (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username']))) {
    header('Location: ../auth/login.php');
    exit;
}

require_once '../../api/config/database.php';
require_once '../../includes/module_permission_manager.php';

$database = new Database();
$conn = $database->getConnection();
$permissionManager = new ModulePermissionManager($conn);

$message = '';
$messageType = '';
$adminId = $_SESSION['admin_id'] ?? 1;

// 处理权限操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $userId = (int)($_POST['user_id'] ?? 0);
    
    try {
        switch ($action) {
            case 'grant_app_with_duration':
                $appType = $_POST['app_type'] ?? '';
                $downloadCount = (int)($_POST['download_count'] ?? 0);
                $duration = (int)($_POST['duration'] ?? 0);
                $notes = $_POST['notes'] ?? '';
                
                $expiresAt = null;
                if ($duration > 0) {
                    $expiresAt = date('Y-m-d H:i:s', strtotime("+{$duration} days"));
                }
                
                $result = $permissionManager->grantAppAccess($userId, $appType, $downloadCount, $expiresAt, $adminId, $notes);
                break;
                
            case 'extend_permission':
                $appType = $_POST['app_type'] ?? '';
                $extendDays = (int)($_POST['extend_days'] ?? 0);
                $result = $permissionManager->extendPermission($userId, $appType, $extendDays, $adminId);
                break;
                
            case 'revoke_app':
                $appType = $_POST['app_type'] ?? '';
                $result = $permissionManager->revokeAppAccess($userId, $appType);
                break;
                
            case 'grant_module':
                $moduleCode = $_POST['module_code'] ?? '';
                $result = $permissionManager->grantModuleAccess($userId, $moduleCode, $adminId);
                break;

            case 'revoke_module':
                $moduleCode = $_POST['module_code'] ?? '';
                $result = $permissionManager->revokeModuleAccess($userId, $moduleCode);
                break;

            case 'grant_all_modules':
                $availableModules = $permissionManager->getAvailableModules();
                $successCount = 0;
                foreach ($availableModules as $moduleCode => $moduleName) {
                    $moduleResult = $permissionManager->grantModuleAccess($userId, $moduleCode, $adminId);
                    if ($moduleResult['success']) {
                        $successCount++;
                    }
                }
                $result = ['success' => true, 'message' => "成功授予 {$successCount} 个模块权限"];
                break;

            case 'revoke_all_modules':
                $availableModules = $permissionManager->getAvailableModules();
                $successCount = 0;
                foreach ($availableModules as $moduleCode => $moduleName) {
                    $moduleResult = $permissionManager->revokeModuleAccess($userId, $moduleCode);
                    if ($moduleResult['success']) {
                        $successCount++;
                    }
                }
                $result = ['success' => true, 'message' => "成功撤销 {$successCount} 个模块权限"];
                break;

            case 'adjust_downloads':
                $adjustment = (int)($_POST['adjustment'] ?? 0);
                $operation = $_POST['operation'] ?? 'add';
                $result = $permissionManager->adjustAndroidDownloadCount($userId, $adjustment, $operation);
                break;
                
            case 'batch_grant':
                $userIds = $_POST['user_ids'] ?? [];
                $appType = $_POST['batch_app_type'] ?? '';
                $downloadCount = (int)($_POST['batch_download_count'] ?? 0);
                $duration = (int)($_POST['batch_duration'] ?? 0);
                $notes = $_POST['batch_notes'] ?? '';
                
                $expiresAt = null;
                if ($duration > 0) {
                    $expiresAt = date('Y-m-d H:i:s', strtotime("+{$duration} days"));
                }
                
                $successCount = 0;
                foreach ($userIds as $uid) {
                    $batchResult = $permissionManager->grantAppAccess((int)$uid, $appType, $downloadCount, $expiresAt, $adminId, $notes);
                    if ($batchResult['success']) {
                        $successCount++;
                    }
                }
                
                $result = ['success' => true, 'message' => "批量操作完成，成功处理 {$successCount} 个用户"];
                break;

            case 'apply_template':
                $template = $_POST['template'] ?? '';
                $result = applyPermissionTemplate($userId, $template, $permissionManager, $adminId);
                break;

            default:
                throw new Exception('无效的操作');
        }
        
        if ($result['success']) {
            $message = $result['message'];
            $messageType = 'success';

            // 权限操作成功后，重定向到同一页面避免重复提交
            header("Location: " . $_SERVER['PHP_SELF'] . "?success=" . urlencode($result['message']));
            exit;
        } else {
            $message = $result['message'];
            $messageType = 'error';
        }
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// 处理GET参数中的成功消息
if (isset($_GET['success'])) {
    $message = $_GET['success'];
    $messageType = 'success';
}

// 处理过期权限
$permissionManager->processExpiredPermissions();

// 获取统计数据
$sql = "SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN u.status = 'active' THEN 1 END) as active_users,
    COUNT(CASE WHEN uap.app_type = 'android' AND uap.is_active = 1 THEN 1 END) as android_users,
    COUNT(CASE WHEN uap.app_type = 'windows' AND uap.is_active = 1 THEN 1 END) as windows_users,
    COUNT(CASE WHEN uap.expires_at IS NOT NULL AND uap.expires_at <= DATE_ADD(NOW(), INTERVAL 7 DAY) AND uap.expires_at > NOW() THEN 1 END) as expiring_soon
    FROM users u
    LEFT JOIN user_app_permissions uap ON u.id = uap.user_id";
$stmt = $conn->prepare($sql);
$stmt->execute();
$stats = $stmt->fetch();

// 获取用户列表（增强版 - 实时数据，确保数据库同步）
$sql = "SELECT u.*,
        GROUP_CONCAT(DISTINCT CONCAT(uap.app_type, '§', IFNULL(uap.expires_at, 'never'), '§', IFNULL(uap.download_count, 0), '§', uap.is_active) SEPARATOR '|') as app_permissions,
        GROUP_CONCAT(DISTINCT CASE WHEN ump.is_active = 1 THEN ump.module_code END) as module_permissions,
        COUNT(DISTINCT CASE WHEN uap.is_active = 1 AND (uap.expires_at IS NULL OR uap.expires_at > NOW()) THEN uap.app_type END) as app_count,
        COUNT(DISTINCT CASE WHEN ump.is_active = 1 THEN ump.module_code END) as module_count
        FROM users u
        LEFT JOIN user_app_permissions uap ON u.id = uap.user_id
        LEFT JOIN user_module_permissions ump ON u.id = ump.user_id AND ump.is_active = 1
        WHERE u.status = 'active'
        GROUP BY u.id, u.username, u.email, u.status, u.created_at
        ORDER BY u.created_at DESC";
$stmt = $conn->prepare($sql);
$stmt->execute();
$users = $stmt->fetchAll();

$availableModules = $permissionManager->getAvailableModules();
$expiringPermissions = $permissionManager->getExpiringPermissions(7);

// 权限模板函数
function applyPermissionTemplate($userId, $template, $permissionManager, $adminId) {
    try {
        switch ($template) {
            case 'basic_user':
                // 基础用户：Android权限 + 基础模块
                $permissionManager->grantAppAccess($userId, 'android', 5, null, $adminId, '基础用户模板');
                $basicModules = ['batch_rename', 'format_names'];
                foreach ($basicModules as $module) {
                    $permissionManager->grantModuleAccess($userId, $module, $adminId);
                }
                return ['success' => true, 'message' => '已应用基础用户权限模板'];

            case 'premium_user':
                // 高级用户：Android + Windows + 大部分模块
                $permissionManager->grantAppAccess($userId, 'android', 20, null, $adminId, '高级用户模板');
                $permissionManager->grantAppAccess($userId, 'windows', 0, null, $adminId, '高级用户模板');
                $premiumModules = ['batch_rename', 'format_names', 'folder_counter', 'author_organizer', 'content_cleaner', 'file_duplicate_finder'];
                foreach ($premiumModules as $module) {
                    $permissionManager->grantModuleAccess($userId, $module, $adminId);
                }
                return ['success' => true, 'message' => '已应用高级用户权限模板'];

            case 'vip_user':
                // VIP用户：所有权限
                $permissionManager->grantAppAccess($userId, 'android', 50, null, $adminId, 'VIP用户模板');
                $permissionManager->grantAppAccess($userId, 'windows', 0, null, $adminId, 'VIP用户模板');
                $availableModules = $permissionManager->getAvailableModules();
                foreach ($availableModules as $moduleCode => $moduleName) {
                    $permissionManager->grantModuleAccess($userId, $moduleCode, $adminId);
                }
                return ['success' => true, 'message' => '已应用VIP用户权限模板'];

            case 'trial_user':
                // 试用用户：限制权限
                $permissionManager->grantAppAccess($userId, 'android', 3, date('Y-m-d H:i:s', strtotime('+7 days')), $adminId, '试用用户模板');
                $trialModules = ['batch_rename'];
                foreach ($trialModules as $module) {
                    $permissionManager->grantModuleAccess($userId, $module, $adminId);
                }
                return ['success' => true, 'message' => '已应用试用用户权限模板（7天有效期）'];

            default:
                return ['success' => false, 'message' => '未知的权限模板'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => '应用权限模板失败：' . $e->getMessage()];
    }
}
?>


<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能权限管理中心 - 一站式权限管理 💖</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <link href="../assets/css/admin-theme.css" rel="stylesheet">
    <style>
        /* 权限管理页面特定样式 */
        .permission-wizard {
            background: var(--card-bg);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            box-shadow: var(--shadow-medium);
        }

        .wizard-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--spacing-lg);
            position: relative;
        }

        .wizard-steps::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }

        .wizard-step {
            background: white;
            border: 3px solid #e9ecef;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
            transition: var(--transition-normal);
        }

        .wizard-step.active {
            border-color: #667eea;
            background: var(--primary-gradient);
            color: white;
        }

        .wizard-step.completed {
            border-color: #28a745;
            background: #28a745;
            color: white;
        }

        .user-card {
            position: relative;
            overflow: hidden;
            transition: var(--transition-normal);
        }

        .user-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .user-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-heavy);
        }

        .permission-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .permission-item {
            background: var(--card-bg);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            border: 2px solid transparent;
            transition: var(--transition-normal);
            cursor: pointer;
        }

        .permission-item:hover {
            border-color: #667eea;
            transform: scale(1.02);
        }

        .permission-item.active {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }

        .permission-item.expired {
            border-color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
        }

        .permission-item.expiring {
            border-color: #ffc107;
            background: rgba(255, 193, 7, 0.1);
            animation: pulse-glow 2s infinite;
        }

        .quick-actions {
            background: var(--primary-gradient);
            color: white;
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        .quick-action-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            border-radius: var(--border-radius-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            margin: var(--spacing-xs);
            transition: var(--transition-normal);
        }

        .quick-action-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .permission-timeline {
            position: relative;
            padding-left: 2rem;
        }

        .permission-timeline::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--primary-gradient);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 1.5rem;
            background: var(--card-bg);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-light);
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -1.75rem;
            top: 1rem;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--primary-gradient);
            border: 3px solid white;
            box-shadow: 0 0 0 3px var(--primary-gradient);
        }

        .search-filters {
            background: var(--card-bg);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            box-shadow: var(--shadow-light);
        }

        .filter-chip {
            display: inline-block;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 0.25rem 0.75rem;
            border-radius: var(--border-radius-pill);
            font-size: 0.8rem;
            margin: 0.25rem;
            cursor: pointer;
            transition: var(--transition-fast);
        }

        .filter-chip:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: scale(1.05);
        }

        .filter-chip.active {
            background: var(--primary-gradient);
            color: white;
        }

        .modal-header {
            background: var(--primary-gradient);
            color: white;
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        /* 权限管理页面响应式优化 */
        @media (max-width: 576px) {
            .search-filters {
                padding: var(--spacing-md);
            }

            .search-filters .row {
                flex-direction: column;
            }

            .search-filters .col-md-8,
            .search-filters .col-md-4 {
                margin-bottom: var(--spacing-sm);
            }

            .input-group {
                flex-direction: column;
            }

            .input-group .form-control,
            .input-group .form-select,
            .input-group .btn {
                border-radius: var(--border-radius-sm) !important;
                margin-bottom: 2px;
            }

            .filter-chip {
                font-size: 0.7rem;
                padding: 0.125rem 0.5rem;
                margin: 0.125rem;
            }

            .permission-wizard {
                padding: var(--spacing-md);
            }

            .wizard-steps {
                margin-bottom: var(--spacing-md);
            }

            .wizard-step {
                width: 30px;
                height: 30px;
                font-size: 0.8rem;
            }

            .permission-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-sm);
            }

            .permission-item {
                padding: var(--spacing-sm);
                text-align: center;
            }

            .user-card {
                margin-bottom: var(--spacing-sm);
            }

            .user-card .card-body {
                padding: var(--spacing-md);
            }

            .permission-badge {
                font-size: 0.6rem;
                padding: 0.125rem 0.5rem;
                margin: 1px;
            }

            .quick-actions {
                padding: var(--spacing-md);
                text-align: center;
            }

            .quick-action-btn {
                display: block;
                width: 100%;
                margin: var(--spacing-xs) 0;
                text-align: center;
            }

            .permission-timeline {
                padding-left: 1rem;
            }

            .timeline-item {
                padding: var(--spacing-sm);
                margin-bottom: 1rem;
            }

            .timeline-item::before {
                left: -1.5rem;
                width: 8px;
                height: 8px;
            }

            .btn-toolbar {
                flex-direction: column;
                gap: var(--spacing-sm);
            }

            .btn-group {
                width: 100%;
                flex-direction: column;
            }

            .btn-group .btn {
                border-radius: var(--border-radius-sm) !important;
                margin-bottom: 2px;
            }
        }

        @media (max-width: 768px) {
            .permission-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .search-filters .input-group {
                flex-direction: row;
                flex-wrap: wrap;
            }

            .search-filters .form-control {
                flex: 1;
                min-width: 200px;
            }

            .quick-actions {
                text-align: left;
            }

            .quick-action-btn {
                display: inline-block;
                width: auto;
            }
        }

        /* 横屏模式优化 */
        @media (orientation: landscape) and (max-height: 600px) {
            .permission-wizard {
                padding: var(--spacing-sm);
            }

            .wizard-steps {
                margin-bottom: var(--spacing-sm);
            }

            .search-filters {
                padding: var(--spacing-sm);
            }

            .permission-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .floating-action {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
        }
        
        .search-box {
            border-radius: 50px;
            border: 2px solid transparent;
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .search-box:focus {
            border-color: #667eea;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php
            // 获取统计数据用于侧边栏
            $sql = "SELECT COUNT(*) as pending_users FROM users WHERE status = 'pending'";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $sidebarStats = $stmt->fetch();

            include '../includes/sidebar.php';
            ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 页面标题 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                    <h1 class="h2 animate__animated animate__fadeInDown">
                        <i class="bi bi-stars text-primary"></i>
                        智能权限管理中心 💖
                        <small class="text-muted fs-6">一站式权限管理解决方案</small>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-gradient" onclick="showBatchModal()">
                                <i class="bi bi-people"></i> 批量操作
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="location.reload()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 消息提示 -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show animate__animated animate__fadeInDown">
                    <i class="bi bi-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card animate__animated animate__fadeInUp">
                            <h3><?php echo $stats['total_users']; ?></h3>
                            <p class="mb-0"><i class="bi bi-people"></i> 总用户数</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card success animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                            <h3><?php echo $stats['android_users']; ?></h3>
                            <p class="mb-0"><i class="bi bi-phone"></i> Android用户</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card warning animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                            <h3><?php echo $stats['windows_users']; ?></h3>
                            <p class="mb-0"><i class="bi bi-windows"></i> Windows用户</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card danger animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                            <h3><?php echo $stats['expiring_soon']; ?></h3>
                            <p class="mb-0"><i class="bi bi-clock"></i> 即将过期</p>
                        </div>
                    </div>
                </div>

                <!-- 智能搜索和快速操作 -->
                <div class="search-filters">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                                <input type="text" class="form-control search-box" id="userSearch"
                                       placeholder="搜索用户名、邮箱..." onkeyup="filterUsers()">
                                <select class="form-select" id="permissionFilter" onchange="filterUsers()">
                                    <option value="">全部权限状态</option>
                                    <option value="android">Android用户</option>
                                    <option value="windows">Windows用户</option>
                                    <option value="expiring">即将过期</option>
                                    <option value="expired">已过期</option>
                                    <option value="no-permission">无权限用户</option>
                                </select>
                                <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                    <i class="bi bi-x-circle"></i> 清除
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-gradient" onclick="showBatchModal()">
                                    <i class="bi bi-people"></i> 批量操作
                                </button>
                                <button type="button" class="btn btn-success" onclick="showQuickGrantModal()">
                                    <i class="bi bi-lightning"></i> 快速授权
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选标签 -->
                    <div class="mt-3">
                        <div class="d-flex flex-wrap align-items-center">
                            <span class="me-2 text-muted">快速筛选:</span>
                            <span class="filter-chip" onclick="setFilter('all')">
                                <i class="bi bi-people"></i> 全部用户
                            </span>
                            <span class="filter-chip" onclick="setFilter('android')">
                                <i class="bi bi-phone"></i> Android
                            </span>
                            <span class="filter-chip" onclick="setFilter('windows')">
                                <i class="bi bi-windows"></i> Windows
                            </span>
                            <span class="filter-chip" onclick="setFilter('expiring')">
                                <i class="bi bi-clock"></i> 即将过期
                            </span>
                            <span class="filter-chip" onclick="setFilter('no-permission')">
                                <i class="bi bi-x-circle"></i> 无权限
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 权限管理向导 -->
                <div class="permission-wizard" style="display: none;" id="permissionWizard">
                    <h5><i class="bi bi-magic"></i> 权限管理向导</h5>
                    <div class="wizard-steps">
                        <div class="wizard-step active" id="step1">1</div>
                        <div class="wizard-step" id="step2">2</div>
                        <div class="wizard-step" id="step3">3</div>
                        <div class="wizard-step" id="step4">4</div>
                    </div>
                    <div class="wizard-content">
                        <p>选择用户 → 选择权限类型 → 设置参数 → 确认授权</p>
                    </div>
                </div>

                <!-- 用户权限卡片 -->
                <div class="row" id="userCards">
                    <?php foreach ($users as $index => $user): ?>
                    <?php
                    // 解析权限信息（实时更新版）
                    $userApps = [];
                    $appDetails = [];
                    if ($user['app_permissions']) {
                        $permissions = explode('|', $user['app_permissions']);
                        foreach ($permissions as $perm) {
                            $parts = explode('§', $perm);
                            if (count($parts) >= 4) {
                                $appType = $parts[0];
                                $expiresAt = $parts[1] === 'never' ? null : $parts[1];
                                $downloadCount = (int)$parts[2];
                                $isActive = (int)$parts[3];

                                // 显示所有活跃权限，不管是否过期
                                if ($isActive) {
                                    $userApps[] = $appType;
                                    $isExpired = $expiresAt && strtotime($expiresAt) <= time();
                                    $isExpiring = $expiresAt && !$isExpired && strtotime($expiresAt) <= strtotime('+7 days');

                                    $appDetails[$appType] = [
                                        'expires_at' => $expiresAt,
                                        'download_count' => $downloadCount,
                                        'is_expiring' => $isExpiring,
                                        'is_expired' => $isExpired
                                    ];
                                }
                            }
                        }
                    }
                    $userModules = $user['module_permissions'] ? explode(',', $user['module_permissions']) : [];
                    ?>
                    <div class="col-lg-6 col-xl-4 mb-4 user-card-container animate__animated animate__fadeInUp"
                         style="animation-delay: <?php echo ($index * 0.1); ?>s"
                         data-username="<?php echo strtolower($user['username']); ?>"
                         data-email="<?php echo strtolower($user['email']); ?>"
                         data-permissions="<?php echo implode(',', $userApps); ?>">
                        <div class="card user-card h-100">
                            <div class="card-header bg-transparent">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">
                                            <i class="bi bi-person-circle text-primary"></i>
                                            <?php echo htmlspecialchars($user['username']); ?>
                                        </h6>
                                        <small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-muted">ID: <?php echo $user['id']; ?></small>
                                        <br>
                                        <input type="checkbox" class="form-check-input user-checkbox" value="<?php echo $user['id']; ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- 应用权限状态 -->
                                <div class="mb-3">
                                    <h6><i class="bi bi-app-indicator"></i> 应用权限</h6>
                                    <div class="d-flex flex-wrap">
                                        <?php foreach (['android', 'windows'] as $appType): ?>
                                        <?php if (in_array($appType, $userApps)): ?>
                                            <?php
                                            $detail = $appDetails[$appType];
                                            $badgeClass = 'bg-success';
                                            $statusText = '';

                                            if ($detail['is_expired']) {
                                                $badgeClass = 'expired-badge';
                                                $statusText = ' (已过期)';
                                            } elseif ($detail['is_expiring']) {
                                                $badgeClass = 'expiring-badge';
                                                $statusText = ' (即将过期)';
                                            }

                                            if ($appType === 'android' && $detail['download_count'] > 0) {
                                                $statusText .= " ({$detail['download_count']}次)";
                                            }
                                            ?>
                                            <span class="badge <?php echo $badgeClass; ?> permission-badge">
                                                <i class="bi bi-<?php echo $appType === 'android' ? 'phone' : 'windows'; ?>"></i>
                                                <?php echo ucfirst($appType) . $statusText; ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary permission-badge">
                                                <i class="bi bi-<?php echo $appType === 'android' ? 'phone' : 'windows'; ?>"></i>
                                                <?php echo ucfirst($appType); ?> ❌
                                            </span>
                                        <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                </div>

                                <!-- 模块权限 -->
                                <div class="mb-3">
                                    <h6><i class="bi bi-puzzle"></i> 模块权限 (<?php echo count($userModules); ?>/<?php echo count($availableModules); ?>)</h6>
                                    <div class="progress mb-2" style="height: 8px;">
                                        <div class="progress-bar bg-info" style="width: <?php echo count($availableModules) > 0 ? (count($userModules) / count($availableModules)) * 100 : 0; ?>%"></div>
                                    </div>
                                    <div class="d-flex flex-wrap">
                                        <?php foreach ($availableModules as $moduleCode => $moduleName): ?>
                                        <small class="badge bg-<?php echo in_array($moduleCode, $userModules) ? 'primary' : 'light text-dark'; ?> permission-badge">
                                            <?php echo in_array($moduleCode, $userModules) ? '✅' : '❌'; ?>
                                            <?php echo htmlspecialchars($moduleName); ?>
                                        </small>
                                        <?php endforeach; ?>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="d-grid gap-2">
                                    <button class="btn btn-gradient btn-sm" onclick="manageAdvancedUser(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                        <i class="bi bi-gear-wide-connected"></i> 高级管理
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </main>
        </div>
    </div>

    <!-- 高级权限管理模态框 -->
    <div class="modal fade" id="advancedPermissionModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-stars"></i> 高级权限管理 - <span id="modalUsername"></span>
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 权限模板快速应用 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                <div class="card-header border-0">
                                    <h6 class="mb-0"><i class="bi bi-magic"></i> 智能权限模板 💖</h6>
                                </div>
                                <div class="card-body">
                                    <form method="POST" class="row align-items-end">
                                        <input type="hidden" name="user_id" class="modal-user-id">
                                        <div class="col-md-8">
                                            <select name="template" class="form-select" required>
                                                <option value="">选择权限模板...</option>
                                                <option value="trial_user">🔰 试用用户 (Android 3次, 7天有效, 基础模块)</option>
                                                <option value="basic_user">👤 基础用户 (Android 5次, 永久, 基础模块)</option>
                                                <option value="premium_user">⭐ 高级用户 (Android 20次 + Windows, 大部分模块)</option>
                                                <option value="vip_user">💎 VIP用户 (Android 50次 + Windows, 全部模块)</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <button type="submit" name="action" value="apply_template" class="btn btn-light w-100">
                                                <i class="bi bi-magic"></i> 一键应用
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 应用权限管理 -->
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="bi bi-app"></i> 应用权限管理</h6>
                                </div>
                                <div class="card-body">
                                    <!-- Android权限 -->
                                    <form method="POST" class="mb-3">
                                        <input type="hidden" name="user_id" class="modal-user-id">
                                        <input type="hidden" name="app_type" value="android">
                                        <div class="mb-3">
                                            <label class="form-label">📱 Android阅读软件</label>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <input type="number" name="download_count" class="form-control" placeholder="下载次数" min="0" value="10">
                                                </div>
                                                <div class="col-md-6">
                                                    <input type="number" name="duration" class="form-control" placeholder="有效天数(0=永久)" min="0" value="30">
                                                </div>
                                            </div>
                                            <textarea name="notes" class="form-control mt-2" placeholder="授权备注..." rows="2"></textarea>
                                            <div class="btn-group w-100 mt-2">
                                                <button type="submit" name="action" value="grant_app_with_duration" class="btn btn-success">
                                                    <i class="bi bi-check-circle"></i> 授予权限
                                                </button>
                                                <button type="submit" name="action" value="revoke_app" class="btn btn-danger">
                                                    <i class="bi bi-x-circle"></i> 撤销权限
                                                </button>
                                            </div>
                                        </div>
                                    </form>

                                    <!-- Windows权限 -->
                                    <form method="POST" class="mb-3">
                                        <input type="hidden" name="user_id" class="modal-user-id">
                                        <input type="hidden" name="app_type" value="windows">
                                        <div class="mb-3">
                                            <label class="form-label">💻 Windows工具箱</label>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <input type="number" name="download_count" class="form-control" value="0" readonly>
                                                </div>
                                                <div class="col-md-6">
                                                    <input type="number" name="duration" class="form-control" placeholder="有效天数(0=永久)" min="0" value="30">
                                                </div>
                                            </div>
                                            <textarea name="notes" class="form-control mt-2" placeholder="授权备注..." rows="2"></textarea>
                                            <div class="btn-group w-100 mt-2">
                                                <button type="submit" name="action" value="grant_app_with_duration" class="btn btn-success">
                                                    <i class="bi bi-check-circle"></i> 授予权限
                                                </button>
                                                <button type="submit" name="action" value="revoke_app" class="btn btn-danger">
                                                    <i class="bi bi-x-circle"></i> 撤销权限
                                                </button>
                                            </div>
                                        </div>
                                    </form>

                                    <!-- 权限延期 -->
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6><i class="bi bi-clock-history"></i> 权限延期</h6>
                                            <form method="POST" class="row">
                                                <input type="hidden" name="user_id" class="modal-user-id">
                                                <div class="col-md-4">
                                                    <select name="app_type" class="form-select" required>
                                                        <option value="">选择应用</option>
                                                        <option value="android">Android</option>
                                                        <option value="windows">Windows</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-4">
                                                    <input type="number" name="extend_days" class="form-control" placeholder="延长天数" min="1" required>
                                                </div>
                                                <div class="col-md-4">
                                                    <button type="submit" name="action" value="extend_permission" class="btn btn-warning w-100">
                                                        <i class="bi bi-plus-circle"></i> 延期
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 模块权限管理 -->
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="bi bi-puzzle"></i> 模块权限管理</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <?php foreach ($availableModules as $moduleCode => $moduleName): ?>
                                        <div class="col-md-6 mb-2">
                                            <form method="POST" class="d-flex align-items-center">
                                                <input type="hidden" name="user_id" class="modal-user-id">
                                                <input type="hidden" name="module_code" value="<?php echo $moduleCode; ?>">
                                                <small class="text-truncate me-2 flex-grow-1"><?php echo htmlspecialchars($moduleName); ?></small>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="submit" name="action" value="grant_module" class="btn btn-outline-success" title="授予">
                                                        <i class="bi bi-check"></i>
                                                    </button>
                                                    <button type="submit" name="action" value="revoke_module" class="btn btn-outline-danger" title="撤销">
                                                        <i class="bi bi-x"></i>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <!-- 批量模块权限操作 -->
                                    <div class="mt-3 pt-3 border-top">
                                        <h6 class="text-muted mb-2"><i class="bi bi-lightning"></i> 快速操作</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <form method="POST">
                                                    <input type="hidden" name="user_id" class="modal-user-id">
                                                    <button type="submit" name="action" value="grant_all_modules" class="btn btn-success btn-sm w-100">
                                                        <i class="bi bi-check-all"></i> 全部授予
                                                    </button>
                                                </form>
                                            </div>
                                            <div class="col-md-6">
                                                <form method="POST">
                                                    <input type="hidden" name="user_id" class="modal-user-id">
                                                    <button type="submit" name="action" value="revoke_all_modules" class="btn btn-danger btn-sm w-100">
                                                        <i class="bi bi-x-circle"></i> 全部撤销
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Android下载次数调整 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0"><i class="bi bi-download"></i> Android下载次数调整</h6>
                                </div>
                                <div class="card-body">
                                    <form method="POST" class="row align-items-end">
                                        <input type="hidden" name="user_id" class="modal-user-id">
                                        <div class="col-md-4">
                                            <label class="form-label">调整数量</label>
                                            <input type="number" name="adjustment" class="form-control" placeholder="输入数量" min="1" required>
                                        </div>
                                        <div class="col-md-4">
                                            <button type="submit" name="action" value="adjust_downloads" class="btn btn-success w-100" onclick="this.form.operation.value='add'">
                                                <i class="bi bi-plus-circle"></i> 增加次数
                                            </button>
                                        </div>
                                        <div class="col-md-4">
                                            <button type="submit" name="action" value="adjust_downloads" class="btn btn-warning w-100" onclick="this.form.operation.value='subtract'">
                                                <i class="bi bi-dash-circle"></i> 减少次数
                                            </button>
                                            <input type="hidden" name="operation" value="">
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量操作模态框 -->
    <div class="modal fade" id="batchModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-people"></i> 批量权限操作
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        已选择 <span id="selectedCount">0</span> 个用户进行批量操作
                    </div>

                    <form method="POST">
                        <input type="hidden" name="action" value="batch_grant">
                        <div id="selectedUserIds"></div>

                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">应用类型</label>
                                <select name="batch_app_type" class="form-select" required>
                                    <option value="">选择应用类型</option>
                                    <option value="android">📱 Android阅读软件</option>
                                    <option value="windows">💻 Windows工具箱</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">下载次数 (仅Android)</label>
                                <input type="number" name="batch_download_count" class="form-control" value="10" min="0">
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">有效天数 (0=永久)</label>
                                <input type="number" name="batch_duration" class="form-control" value="30" min="0">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">批量操作备注</label>
                                <input type="text" name="batch_notes" class="form-control" placeholder="批量授权...">
                            </div>
                        </div>

                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-gradient">
                                <i class="bi bi-check-all"></i> 执行批量授权
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动操作按钮 -->
    <div class="floating-action">
        <button class="btn btn-gradient rounded-circle" style="width: 60px; height: 60px;" onclick="scrollToTop()" title="回到顶部">
            <i class="bi bi-arrow-up"></i>
        </button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/theme-switcher.js"></script>
    <script>
        // 高级用户管理
        function manageAdvancedUser(userId, username) {
            document.getElementById('modalUsername').textContent = username;

            // 设置所有modal-user-id字段
            document.querySelectorAll('.modal-user-id').forEach(input => {
                input.value = userId;
            });

            new bootstrap.Modal(document.getElementById('advancedPermissionModal')).show();
        }

        // 批量操作
        function showBatchModal() {
            const checkboxes = document.querySelectorAll('.user-checkbox:checked');
            const selectedIds = Array.from(checkboxes).map(cb => cb.value);

            if (selectedIds.length === 0) {
                alert('请先选择要操作的用户！');
                return;
            }

            document.getElementById('selectedCount').textContent = selectedIds.length;

            // 清空并重新填充用户ID
            const container = document.getElementById('selectedUserIds');
            container.innerHTML = '';
            selectedIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'user_ids[]';
                input.value = id;
                container.appendChild(input);
            });

            new bootstrap.Modal(document.getElementById('batchModal')).show();
        }

        // 用户搜索和筛选
        function filterUsers() {
            const searchTerm = document.getElementById('userSearch').value.toLowerCase();
            const permissionFilter = document.getElementById('permissionFilter').value;
            const userCards = document.querySelectorAll('.user-card-container');

            userCards.forEach(card => {
                const username = card.dataset.username || '';
                const email = card.dataset.email || '';
                const permissions = card.dataset.permissions || '';

                let showCard = true;

                // 搜索过滤
                if (searchTerm && !username.includes(searchTerm) && !email.includes(searchTerm)) {
                    showCard = false;
                }

                // 权限过滤
                if (permissionFilter) {
                    switch (permissionFilter) {
                        case 'android':
                            showCard = showCard && permissions.includes('android');
                            break;
                        case 'windows':
                            showCard = showCard && permissions.includes('windows');
                            break;
                        case 'expiring':
                            showCard = showCard && card.querySelector('.expiring-badge');
                            break;
                        case 'no-permission':
                            showCard = showCard && permissions === '';
                            break;
                    }
                }

                card.style.display = showCard ? 'block' : 'none';
            });
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAllUsers');
            const userCheckboxes = document.querySelectorAll('.user-checkbox');

            if (selectAllCheckbox) {
                userCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
            }
        }

        // 回到顶部
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 筛选器功能
        function setFilter(filterType) {
            const filterSelect = document.getElementById('permissionFilter');
            const filterChips = document.querySelectorAll('.filter-chip');

            // 更新选择框
            if (filterType === 'all') {
                filterSelect.value = '';
            } else {
                filterSelect.value = filterType;
            }

            // 更新筛选标签样式
            filterChips.forEach(chip => {
                chip.classList.remove('active');
            });
            event.target.classList.add('active');

            // 执行筛选
            filterUsers();
        }

        // 清除筛选器
        function clearFilters() {
            document.getElementById('userSearch').value = '';
            document.getElementById('permissionFilter').value = '';
            document.querySelectorAll('.filter-chip').forEach(chip => {
                chip.classList.remove('active');
            });
            filterUsers();
        }

        // 快速授权模态框
        function showQuickGrantModal() {
            // 创建快速授权模态框
            const modalHtml = `
                <div class="modal fade" id="quickGrantModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi bi-lightning"></i> 快速授权
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="permission-wizard">
                                    <div class="wizard-steps">
                                        <div class="wizard-step active">1</div>
                                        <div class="wizard-step">2</div>
                                        <div class="wizard-step">3</div>
                                    </div>
                                    <div class="wizard-content">
                                        <h6>选择权限模板</h6>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="permission-item" onclick="selectTemplate('android_basic')">
                                                    <i class="bi bi-phone fs-2 text-success"></i>
                                                    <h6>Android基础</h6>
                                                    <small>30天有效期，10次下载</small>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="permission-item" onclick="selectTemplate('windows_basic')">
                                                    <i class="bi bi-windows fs-2 text-primary"></i>
                                                    <h6>Windows基础</h6>
                                                    <small>30天有效期</small>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="permission-item" onclick="selectTemplate('full_access')">
                                                    <i class="bi bi-stars fs-2 text-warning"></i>
                                                    <h6>完整权限</h6>
                                                    <small>永久有效，无限制</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加到页面并显示
            if (!document.getElementById('quickGrantModal')) {
                document.body.insertAdjacentHTML('beforeend', modalHtml);
            }
            new bootstrap.Modal(document.getElementById('quickGrantModal')).show();
        }

        // 选择权限模板
        function selectTemplate(template) {
            const items = document.querySelectorAll('#quickGrantModal .permission-item');
            items.forEach(item => item.classList.remove('active'));
            event.target.closest('.permission-item').classList.add('active');

            // 这里可以添加模板选择逻辑
            console.log('选择了模板:', template);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化筛选标签
            const firstChip = document.querySelector('.filter-chip');
            if (firstChip) {
                firstChip.classList.add('active');
            }

            // 自动处理过期权限提醒
            const expiringBadges = document.querySelectorAll('.expiring-badge');
            if (expiringBadges.length > 0) {
                console.log(`发现 ${expiringBadges.length} 个即将过期的权限`);
            }

            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                // Ctrl + A 全选
                if (e.ctrlKey && e.key === 'a' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                    e.preventDefault();
                    const selectAllCheckbox = document.getElementById('selectAllUsers');
                    if (selectAllCheckbox) {
                        selectAllCheckbox.checked = !selectAllCheckbox.checked;
                        toggleSelectAll();
                    }
                }

                // Ctrl + B 批量操作
                if (e.ctrlKey && e.key === 'b') {
                    e.preventDefault();
                    showBatchModal();
                }

                // ESC 关闭模态框
                if (e.key === 'Escape') {
                    const modals = document.querySelectorAll('.modal.show');
                    modals.forEach(modal => {
                        bootstrap.Modal.getInstance(modal)?.hide();
                    });
                }
            });

            // 添加实时统计更新
            updateStats();
        });

        // 更新统计信息
        function updateStats() {
            const visibleCards = document.querySelectorAll('.user-card-container:not([style*="display: none"])');
            const androidUsers = Array.from(visibleCards).filter(card =>
                card.dataset.permissions.includes('android')
            ).length;
            const windowsUsers = Array.from(visibleCards).filter(card =>
                card.dataset.permissions.includes('windows')
            ).length;
            const expiringUsers = document.querySelectorAll('.expiring-badge').length;

            // 可以在这里更新统计卡片的数字
            console.log(`当前显示: ${visibleCards.length} 用户, Android: ${androidUsers}, Windows: ${windowsUsers}, 即将过期: ${expiringUsers}`);
        }

        // 添加搜索框的实时搜索
        document.getElementById('userSearch')?.addEventListener('input', function() {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(filterUsers, 300);
        });

        // 权限状态实时检查和数据刷新（已禁用自动刷新，避免干扰用户操作）
        // setInterval(function() {
        //     // 每30秒刷新页面数据
        //     location.reload();
        // }, 30000); // 30秒自动刷新

        // 每5分钟检查一次权限状态
        setInterval(function() {
            fetch('advanced_permissions.php?check_expired=1')
                .then(response => response.json())
                .then(data => {
                    if (data.expired_count > 0) {
                        console.log(`处理了 ${data.expired_count} 个过期权限`);
                        // 如果有过期权限被处理，立即刷新页面
                        setTimeout(() => location.reload(), 1000);
                    }
                })
                .catch(error => console.log('权限检查失败:', error));
        }, 300000); // 5分钟

        // 添加成功提示音效（可选）
        function playSuccessSound() {
            // 创建一个简单的成功提示音
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        }
    </script>
</body>
</html>
