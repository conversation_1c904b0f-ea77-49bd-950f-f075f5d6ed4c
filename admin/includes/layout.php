<?php
/**
 * 管理后台布局模板
 * 统一的页面布局，包含头部、侧边栏和主内容区
 */

// 确保已经开始session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 检查管理员登录
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// 获取页面标题（如果没有设置则使用默认值）
$pageTitle = $pageTitle ?? '管理系统';
$currentPage = basename($_SERVER['PHP_SELF']);

// 获取管理员信息
$adminUsername = $_SESSION['admin_username'] ?? 'Admin';
$adminId = $_SESSION['admin_id'] ?? 0;

// 获取统计数据
try {
    require_once '../api/config/database.php';
    $database = new Database();
    $conn = $database->getConnection();
    
    // 获取待审批用户数量
    $sql = "SELECT COUNT(*) as pending_users FROM users WHERE status = 'pending'";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats = $stmt->fetch();
    
} catch (Exception $e) {
    $stats = ['pending_users' => 0];
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?> - 管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <link href="../assets/css/admin-theme.css" rel="stylesheet">
    <style>
        .gradient-text {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        <?php if (isset($customCSS)): ?>
        <?php echo $customCSS; ?>
        <?php endif; ?>
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="bi bi-shield-check"></i>
                            管理后台
                        </h4>
                        <small class="text-white-50">欢迎，<?php echo htmlspecialchars($adminUsername); ?></small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <!-- 主要功能 -->
                        <li class="nav-item">
                            <a class="nav-link <?php echo $currentPage === 'index.php' ? 'active' : ''; ?>" href="index.php">
                                <i class="bi bi-speedometer2"></i> 仪表盘
                            </a>
                        </li>
                        
                        <!-- 用户管理分组 -->
                        <li class="nav-item mt-3">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>用户管理</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $currentPage === 'pending.php' ? 'active' : ''; ?>" href="pending.php">
                                <i class="bi bi-person-plus"></i> 待审批用户
                                <?php if ($stats['pending_users'] > 0): ?>
                                    <span class="badge bg-danger ms-2"><?php echo $stats['pending_users']; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $currentPage === 'users.php' ? 'active' : ''; ?>" href="users.php">
                                <i class="bi bi-people"></i> 用户管理
                            </a>
                        </li>

                        <!-- 权限管理分组 -->
                        <li class="nav-item mt-3">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>权限管理</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $currentPage === 'module_permissions.php' ? 'active' : ''; ?>" href="module_permissions.php">
                                <i class="bi bi-gear-wide-connected"></i> 模块权限管理
                            </a>
                        </li>

                        <!-- 系统管理分组 -->
                        <li class="nav-item mt-3">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>系统管理</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $currentPage === 'logs.php' ? 'active' : ''; ?>" href="logs.php">
                                <i class="bi bi-journal-text"></i> 操作日志
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $currentPage === 'security.php' ? 'active' : ''; ?>" href="security.php">
                                <i class="bi bi-shield-check"></i> 安全监控
                            </a>
                        </li>

                        <li class="nav-item mt-4">
                            <a class="nav-link text-warning" href="logout.php">
                                <i class="bi bi-box-arrow-right"></i> 退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 移动端导航切换按钮 -->
                <div class="d-md-none mb-3">
                    <button class="btn btn-gradient" type="button" onclick="toggleMobileSidebar()">
                        <i class="bi bi-list"></i> 菜单
                    </button>
                </div>

                <?php if (isset($pageHeader) && $pageHeader): ?>
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <?php echo $pageHeader; ?>
                </div>
                <?php endif; ?>

                <?php if (isset($message) && $message): ?>
                <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <i class="bi bi-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- 页面内容将在这里插入 -->
                <?php if (isset($pageContent)): ?>
                    <?php echo $pageContent; ?>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/theme-switcher.js"></script>
    <script src="../assets/js/mobile-enhancements.js"></script>
    <script>
        // 移动端侧边栏切换
        function toggleMobileSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            if (sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
                if (overlay) overlay.remove();
            } else {
                sidebar.classList.add('show');

                // 创建遮罩层
                const overlayDiv = document.createElement('div');
                overlayDiv.id = 'sidebarOverlay';
                overlayDiv.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    z-index: 1040;
                    display: block;
                `;
                overlayDiv.onclick = toggleMobileSidebar;
                document.body.appendChild(overlayDiv);
            }
        }

        // 响应式处理
        function handleResize() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            if (window.innerWidth >= 768) {
                sidebar.classList.remove('show');
                if (overlay) overlay.remove();
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            window.addEventListener('resize', handleResize);

            // ESC键关闭侧边栏
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const sidebar = document.querySelector('.sidebar');
                    if (sidebar.classList.contains('show')) {
                        toggleMobileSidebar();
                    }
                }
            });

            // 添加触摸手势支持
            let touchStartX = 0;
            let touchEndX = 0;

            document.addEventListener('touchstart', function(e) {
                touchStartX = e.changedTouches[0].screenX;
            });

            document.addEventListener('touchend', function(e) {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            });

            function handleSwipe() {
                const sidebar = document.querySelector('.sidebar');
                const swipeThreshold = 50;

                // 从左边缘向右滑动打开侧边栏
                if (touchStartX < 20 && touchEndX - touchStartX > swipeThreshold) {
                    if (!sidebar.classList.contains('show')) {
                        toggleMobileSidebar();
                    }
                }

                // 向左滑动关闭侧边栏
                if (touchStartX - touchEndX > swipeThreshold) {
                    if (sidebar.classList.contains('show')) {
                        toggleMobileSidebar();
                    }
                }
            }
        });
    </script>
    <?php if (isset($customJS)): ?>
    <script>
        <?php echo $customJS; ?>
    </script>
    <?php endif; ?>
</body>
</html>
