<?php
/**
 * 管理后台顶部导航栏组件
 * 统一的顶部导航，适用于所有管理页面
 */

// 获取管理员用户名
$adminUsername = $_SESSION['admin_username'] ?? 'Admin';
?>

<!-- 顶部导航栏 -->
<nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
    <div class="container-fluid">
        <a class="navbar-brand fw-bold" href="index.php">
            <i class="bi bi-shield-check text-primary"></i>
            <span class="gradient-text">管理系统</span>
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="../dashboard/dashboard.php">
                        <i class="bi bi-house-door"></i> 首页
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../users/pending.php">
                        <i class="bi bi-person-plus"></i> 审批
                        <?php if (isset($stats['pending_users']) && $stats['pending_users'] > 0): ?>
                            <span class="badge bg-danger ms-1"><?php echo $stats['pending_users']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../users/users.php">
                        <i class="bi bi-people"></i> 用户
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../permissions/advanced_permissions.php">
                        <i class="bi bi-gear-wide-connected"></i> 权限
                    </a>
                </li>
            </ul>
            
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle"></i>
                        <?php echo htmlspecialchars($adminUsername); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../monitoring/logs.php">
                            <i class="bi bi-journal-text"></i> 操作日志
                        </a></li>
                        <li><a class="dropdown-item" href="../monitoring/security.php">
                            <i class="bi bi-shield-check"></i> 安全监控
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="../auth/logout.php">
                            <i class="bi bi-box-arrow-right"></i> 退出登录
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<style>
.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar {
    border-bottom: 1px solid #e9ecef;
}

.navbar-nav .nav-link {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 8px;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}
</style>
