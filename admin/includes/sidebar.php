<?php
/**
 * 管理后台侧边栏组件 💖
 * 统一的侧边栏导航，适用于所有管理页面
 * 重构后的清晰文件结构
 */

// 获取当前页面文件名，用于高亮当前页面
$current_page = basename($_SERVER['PHP_SELF']);

// 获取管理员用户名
$adminUsername = $_SESSION['admin_username'] ?? 'Admin';

// 确保管理员已登录
if ((!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) &&
    (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username']))) {
    $adminUsername = 'Guest';
}

// 获取统计数据（如果存在）
$pending_count = $stats['pending_users'] ?? 0;

// 根据当前文件位置确定基础路径
$current_dir = dirname($_SERVER['PHP_SELF']);
$admin_base = '';
if (strpos($current_dir, '/admin/') !== false) {
    // 如果在admin的子目录中，需要返回上级
    $admin_base = '../';
}
?>

<!-- 侧边栏 -->
<nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
    <div class="position-sticky pt-3">
        <div class="text-center mb-4">
            <h4 class="text-white">
                <i class="bi bi-shield-check"></i>
                管理后台
            </h4>
            <small class="text-white-50">欢迎，<?php echo htmlspecialchars($adminUsername); ?></small>
        </div>
        
        <ul class="nav flex-column">
            <!-- 主要功能 -->
            <li class="nav-item">
                <a class="nav-link <?php echo in_array($current_page, ['index.php', 'dashboard.php']) ? 'active' : ''; ?>" href="<?php echo $admin_base; ?>dashboard/dashboard.php">
                    <i class="bi bi-speedometer2"></i> 智能仪表板 💖
                </a>
            </li>

            <!-- 用户管理分组 -->
            <li class="nav-item mt-3">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                    <span>用户管理</span>
                </h6>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page === 'pending.php' ? 'active' : ''; ?>" href="<?php echo $admin_base; ?>users/pending.php">
                    <i class="bi bi-person-plus"></i> 待审批用户
                    <?php if ($pending_count > 0): ?>
                        <span class="badge bg-danger ms-2"><?php echo $pending_count; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page === 'advanced_permissions.php' ? 'active' : ''; ?>" href="<?php echo $admin_base; ?>permissions/advanced_permissions.php">
                    <i class="bi bi-stars"></i> 智能权限管理 💖
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page === 'users.php' ? 'active' : ''; ?>" href="<?php echo $admin_base; ?>users/users.php">
                    <i class="bi bi-person-gear"></i> 用户状态管理
                </a>
            </li>



            <!-- 系统管理分组 -->
            <li class="nav-item mt-3">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                    <span>系统管理</span>
                </h6>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page === 'logs.php' ? 'active' : ''; ?>" href="<?php echo $admin_base; ?>monitoring/logs.php">
                    <i class="bi bi-journal-text"></i> 操作日志
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page === 'security.php' ? 'active' : ''; ?>" href="<?php echo $admin_base; ?>monitoring/security.php">
                    <i class="bi bi-shield-check"></i> 安全监控
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page === 'sync_check.php' ? 'active' : ''; ?>" href="<?php echo $admin_base; ?>monitoring/sync_check.php">
                    <i class="bi bi-database-check"></i> 数据库同步检查 💖
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page === 'realtime_permission_check.php' ? 'active' : ''; ?>" href="<?php echo $admin_base; ?>permissions/realtime_permission_check.php">
                    <i class="bi bi-activity"></i> 实时权限检查 💖
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="<?php echo $admin_base; ?>../api/docs.php" target="_blank">
                    <i class="bi bi-code-slash"></i> API文档 <i class="bi bi-box-arrow-up-right small"></i>
                </a>
            </li>

            <li class="nav-item mt-4">
                <a class="nav-link text-warning" href="<?php echo $admin_base; ?>auth/logout.php" onclick="return confirm('确定要退出登录吗？')">
                    <i class="bi bi-box-arrow-right"></i> 安全退出
                </a>
            </li>
        </ul>
    </div>
</nav>

<!-- 样式已移至统一的admin-theme.css文件 -->
