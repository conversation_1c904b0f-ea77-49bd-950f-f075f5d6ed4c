<?php
/**
 * 用户详情页面 - 现代化设计
 * 管理系统 - management.djxs.xyz
 */

require_once __DIR__ . '/../../includes/admin.php';
require_once __DIR__ . '/../../includes/security.php';

$admin = new Admin();
$security = new SecurityManager();

// 检查登录状态
if (!$admin->checkLogin()) {
    header('Location: ../auth/login.php');
    exit();
}

$userId = intval($_GET['id'] ?? 0);
if (!$userId) {
    header('Location: users.php');
    exit();
}

session_start();
$adminId = $_SESSION['admin_id'];
$message = '';
$messageType = '';

// 处理用户操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $reason = trim($_POST['reason'] ?? '');
    $ipAddress = trim($_POST['ip_address'] ?? '');
    $ipAction = $_POST['ip_action'] ?? '';

    if ($action === 'ban') {
        $result = $admin->banUser($userId, $adminId, $reason);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'unban') {
        $result = $admin->unbanUser($userId, $adminId);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'trust_ip' && $ipAddress) {
        $result = $security->trustIP($userId, $ipAddress, $adminId);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'untrust_ip' && $ipAddress) {
        $result = $security->untrustIP($userId, $ipAddress, $adminId);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'block_ip' && $ipAddress) {
        $result = $security->blockIP($userId, $ipAddress, $adminId);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'unblock_ip' && $ipAddress) {
        $result = $security->unblockIP($userId, $ipAddress, $adminId);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'manual_ip' && $ipAddress && $ipAction) {
        // 手动添加IP管理
        if ($ipAction === 'trust') {
            $result = $security->trustIP($userId, $ipAddress, $adminId);
        } elseif ($ipAction === 'block') {
            $result = $security->blockIP($userId, $ipAddress, $adminId);
        } else {
            $result = ['success' => false, 'message' => '无效的操作类型'];
        }
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'refresh_ip' && $ipAddress) {
        // 刷新IP信息
        try {
            require_once __DIR__ . '/../../includes/IPQueryService.php';
            $database = new Database();
            $conn = $database->getConnection();
            $ipService = new IPQueryService($conn);

            // 强制刷新IP信息
            $result = $ipService->queryIP($ipAddress, true);

            if ($result['success']) {
                // 更新ip_monitors表中的信息
                $ipData = $result['data'];

                $sql = "UPDATE ip_monitors SET
                            location = ?, continent = ?, country = ?, country_code = ?,
                            province = ?, city = ?, district = ?, street = ?, isp = ?,
                            latitude = ?, longitude = ?, area_code = ?, zip_code = ?,
                            time_zone = ?, ip_int = ?, street_history = ?, risk_score = ?,
                            risk_level = ?, is_proxy = ?, proxy_type = ?, risk_tag = ?,
                            api_source = ?, last_updated = CURRENT_TIMESTAMP
                        WHERE user_id = ? AND ip_address = ?";

                $stmt = $conn->prepare($sql);
                $stmt->execute([
                    $ipData['location'] ?? null, $ipData['continent'] ?? null,
                    $ipData['country'] ?? null, $ipData['country_code'] ?? null,
                    $ipData['province'] ?? null, $ipData['city'] ?? null,
                    $ipData['district'] ?? null, $ipData['street'] ?? null,
                    $ipData['isp'] ?? null, $ipData['latitude'] ?? null,
                    $ipData['longitude'] ?? null, $ipData['area_code'] ?? null,
                    $ipData['zip_code'] ?? null, $ipData['time_zone'] ?? null,
                    $ipData['ip_int'] ?? null, $ipData['street_history'] ?? null,
                    $ipData['risk_score'] ?? 0, $ipData['risk_level'] ?? 'unknown',
                    $ipData['is_proxy'] ?? 0, $ipData['proxy_type'] ?? null,
                    $ipData['risk_tag'] ?? null, $ipData['api_source'] ?? 'unknown',
                    $userId, $ipAddress
                ]);

                $message = "IP地址 {$ipAddress} 的信息已成功刷新";
                $messageType = 'success';

                // 记录操作日志
                $admin->logOperation($adminId, $userId, 'refresh_ip', "刷新了IP {$ipAddress} 的信息");
            } else {
                $message = "刷新IP信息失败: " . $result['error'];
                $messageType = 'danger';
            }
        } catch (Exception $e) {
            $message = "刷新IP信息时发生错误: " . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// 获取用户详情
$detailResult = $admin->getUserDetail($userId);
if (!$detailResult['success']) {
    header('Location: users.php?error=' . urlencode($detailResult['message']));
    exit();
}

$userDetail = $detailResult['user'];
$sessionInfo = $detailResult['session_info'];
$activeSessions = $detailResult['active_sessions'];
$recentLogs = $detailResult['recent_logs'];
$banInfo = $detailResult['ban_info'];

// 获取用户IP信息
$userIPs = $security->getUserIPs($userId);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户详情 - <?php echo htmlspecialchars($userDetail['username']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <link href="../assets/css/admin-theme.css" rel="stylesheet">
    <style>
        /* 现代化用户详情页面样式 */
        :root {
            --header-height: 100px;
            --sidebar-width: 80px;
            --card-spacing: 1.5rem;
            --border-radius-modern: 16px;
            --shadow-modern: 0 4px 20px rgba(0,0,0,0.08);
            --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            overflow-x: hidden;
        }

        /* 紧凑型用户头部 */
        .user-header {
            background: var(--card-bg);
            border-radius: var(--border-radius-modern);
            padding: 1.5rem;
            margin-bottom: var(--card-spacing);
            box-shadow: var(--shadow-modern);
            position: sticky;
            top: 20px;
            z-index: 100;
            backdrop-filter: blur(10px);
        }

        .user-avatar-compact {
            width: 60px;
            height: 60px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            margin-right: 1rem;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        /* 状态徽章 */
        .status-badge-modern {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
            gap: 0.5rem;
            border: 2px solid transparent;
        }

        .status-badge-modern.active {
            background: rgba(34, 197, 94, 0.1);
            color: #059669;
            border-color: rgba(34, 197, 94, 0.3);
        }

        .status-badge-modern.banned {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border-color: rgba(239, 68, 68, 0.3);
        }

        /* 网格布局优化 */
        .info-grid-modern {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--card-spacing);
            margin-bottom: var(--card-spacing);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: var(--card-spacing);
        }

        /* 卡片样式 */
        .card-modern {
            background: var(--card-bg);
            border-radius: var(--border-radius-modern);
            border: none;
            box-shadow: var(--shadow-modern);
            transition: var(--transition-smooth);
            overflow: hidden;
        }

        .card-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .card-header-modern {
            background: var(--primary-gradient);
            color: white;
            padding: 1rem 1.5rem;
            border: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-header-modern h6 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-body-modern {
            padding: 1.5rem;
        }

        /* 信息项样式 */
        .info-item-modern {
            display: grid;
            grid-template-columns: auto 1fr;
            gap: 1rem;
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            align-items: center;
        }

        .info-item-modern:last-child {
            border-bottom: none;
        }

        .info-label-modern {
            font-size: 0.8rem;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 120px;
        }

        .info-value-modern {
            font-size: 0.9rem;
            font-weight: 500;
            color: #1f2937;
        }

        /* 统计卡片 */
        .stat-card {
            background: var(--card-bg);
            border-radius: var(--border-radius-modern);
            padding: 1.5rem;
            text-align: center;
            box-shadow: var(--shadow-modern);
            transition: var(--transition-smooth);
            border: 2px solid transparent;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .stat-card.primary { border-color: #6366f1; }
        .stat-card.success { border-color: #22c55e; }
        .stat-card.warning { border-color: #f59e0b; }
        .stat-card.danger { border-color: #ef4444; }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .stat-card.primary .stat-icon { background: #6366f1; }
        .stat-card.success .stat-icon { background: #22c55e; }
        .stat-card.warning .stat-icon { background: #f59e0b; }
        .stat-card.danger .stat-icon { background: #ef4444; }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }

        /* IP表格现代化 */
        .ip-table-modern {
            background: var(--card-bg);
            border-radius: var(--border-radius-modern);
            overflow: hidden;
            box-shadow: var(--shadow-modern);
        }

        .ip-table-modern .table {
            margin: 0;
            font-size: 0.85rem;
        }

        .ip-table-modern .table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 1rem 0.75rem;
            border: none;
        }

        .ip-table-modern .table td {
            padding: 0.75rem;
            vertical-align: middle;
            border-color: rgba(0,0,0,0.05);
        }

        .ip-table-modern .table tbody tr:hover {
            background: rgba(99, 102, 241, 0.03);
        }

        /* 快速操作栏 */
        .quick-actions {
            background: var(--card-bg);
            border-radius: var(--border-radius-modern);
            padding: 1rem;
            box-shadow: var(--shadow-modern);
            margin-bottom: var(--card-spacing);
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
            align-items: center;
        }

        .btn-modern {
            border-radius: 12px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: var(--transition-smooth);
            border: 2px solid transparent;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* 最近活动列表 */
        .activity-item {
            display: flex;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            transition: var(--transition-smooth);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-item:hover {
            background: rgba(99, 102, 241, 0.03);
            margin: 0 -1.5rem;
            padding-left: 1.5rem;
            padding-right: 1.5rem;
            border-radius: 8px;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(99, 102, 241, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6366f1;
            flex-shrink: 0;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .activity-desc {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .activity-time {
            font-size: 0.75rem;
            color: #9ca3af;
            text-align: right;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            :root {
                --card-spacing: 1rem;
            }

            .info-grid-modern {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.75rem;
            }

            .user-header {
                padding: 1rem;
                position: static;
            }

            .quick-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .btn-modern {
                justify-content: center;
            }

            .info-item-modern {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .info-label-modern {
                min-width: auto;
            }
        }

        /* 标签和徽章 */
        .badge-modern {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(99, 102, 241, 0.3);
            border-radius: 50%;
            border-top-color: #6366f1;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 搜索和筛选 */
        .search-modern {
            background: var(--card-bg);
            border-radius: var(--border-radius-modern);
            padding: 1rem;
            box-shadow: var(--shadow-modern);
            margin-bottom: var(--card-spacing);
        }

        .search-input {
            border: 2px solid transparent;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: var(--transition-smooth);
            background: rgba(99, 102, 241, 0.05);
        }

        .search-input:focus {
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            outline: none;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #6b7280;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* 进度条 */
        .progress-modern {
            height: 8px;
            border-radius: 4px;
            background: rgba(99, 102, 241, 0.1);
            overflow: hidden;
        }

        .progress-bar-modern {
            height: 100%;
            background: var(--primary-gradient);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php
            // 获取统计数据用于侧边栏
            $stats = ['pending_users' => 0];
            try {
                require_once '../../api/config/database.php';
                $database = new Database();
                $conn = $database->getConnection();
                $pendingSql = "SELECT COUNT(*) as pending_users FROM pending_users WHERE status = 'pending'";
                $pendingStmt = $conn->prepare($pendingSql);
                $pendingStmt->execute();
                $stats = $pendingStmt->fetch();
            } catch (Exception $e) {
                // 忽略错误，使用默认值
            }
            include '../includes/sidebar.php';
            ?>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-3">
                <!-- 消息提示 -->
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show animate__animated animate__fadeInDown" role="alert">
                        <i class="bi bi-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- 用户头部信息 -->
                <div class="user-header animate__animated animate__fadeInUp">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar-compact">
                                    <?php echo strtoupper(substr($userDetail['username'], 0, 1)); ?>
                                </div>
                                <div>
                                    <h2 class="mb-1"><?php echo htmlspecialchars($userDetail['username']); ?></h2>
                                    <p class="text-muted mb-2">用户ID: #<?php echo $userDetail['id']; ?> • <?php echo htmlspecialchars($userDetail['email']); ?></p>
                                    <?php
                                    $statusClass = 'status-badge-modern ' . $userDetail['status'];
                                    $statusText = [
                                        'active' => '活跃用户',
                                        'banned' => '已封禁',
                                        'suspended' => '已暂停'
                                    ][$userDetail['status']] ?? $userDetail['status'];
                                    $statusIcon = [
                                        'active' => 'bi-check-circle-fill',
                                        'banned' => 'bi-x-circle-fill',
                                        'suspended' => 'bi-pause-circle-fill'
                                    ][$userDetail['status']] ?? 'bi-question-circle-fill';
                                    ?>
                                    <span class="<?php echo $statusClass; ?>">
                                        <i class="bi <?php echo $statusIcon; ?>"></i>
                                        <?php echo $statusText; ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="d-flex gap-2 justify-content-end">
                                <a href="users.php" class="btn btn-outline-secondary btn-modern">
                                    <i class="bi bi-arrow-left"></i> 返回
                                </a>
                                <a href="../monitoring/logs.php?user_id=<?php echo $userId; ?>" class="btn btn-outline-info btn-modern">
                                    <i class="bi bi-journal-text"></i> 查看日志
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作栏 -->
                <div class="quick-actions animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                    <span class="text-muted fw-bold">快速操作：</span>
                    <?php if ($userDetail['status'] === 'active'): ?>
                        <button type="button" class="btn btn-danger btn-modern" onclick="showBanModal()">
                            <i class="bi bi-ban"></i> 封禁用户
                        </button>
                    <?php elseif ($userDetail['status'] === 'banned'): ?>
                        <button type="button" class="btn btn-success btn-modern" onclick="showUnbanModal()">
                            <i class="bi bi-check-circle"></i> 解封用户
                        </button>
                    <?php endif; ?>
                    <a href="../permissions/advanced_permissions.php?user_id=<?php echo $userId; ?>" class="btn btn-primary btn-modern">
                        <i class="bi bi-gear"></i> 权限管理
                    </a>
                    <button type="button" class="btn btn-outline-warning btn-modern" onclick="showIPManageModal()">
                        <i class="bi bi-shield-check"></i> IP管理
                    </button>
                    <button type="button" class="btn btn-outline-info btn-modern" onclick="exportUserData()">
                        <i class="bi bi-download"></i> 导出数据
                    </button>
                </div>

                <!-- 统计概览 -->
                <div class="stats-grid animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="bi bi-graph-up"></i>
                        </div>
                        <div class="stat-number"><?php echo $sessionInfo['total_sessions'] ?? 0; ?></div>
                        <div class="stat-label">总会话数</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="bi bi-activity"></i>
                        </div>
                        <div class="stat-number"><?php echo $activeSessions; ?></div>
                        <div class="stat-label">活跃会话</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="bi bi-shield-check"></i>
                        </div>
                        <div class="stat-number"><?php echo count($userIPs); ?></div>
                        <div class="stat-label">IP地址数</div>
                    </div>
                    <div class="stat-card danger">
                        <div class="stat-icon">
                            <i class="bi bi-clock-history"></i>
                        </div>
                        <div class="stat-number"><?php echo $userDetail['login_count']; ?></div>
                        <div class="stat-label">登录次数</div>
                    </div>
                </div>

                <!-- 主要内容网格 -->
                <div class="info-grid-modern">
                    <!-- 用户基本信息 -->
                    <div class="card-modern animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                        <div class="card-header-modern">
                            <h6><i class="bi bi-person-circle"></i> 基本信息</h6>
                        </div>
                        <div class="card-body-modern">
                            <div class="info-item-modern">
                                <div class="info-label-modern">
                                    <i class="bi bi-envelope"></i> 邮箱
                                </div>
                                <div class="info-value-modern"><?php echo htmlspecialchars($userDetail['email']); ?></div>
                            </div>
                            <div class="info-item-modern">
                                <div class="info-label-modern">
                                    <i class="bi bi-phone"></i> 手机
                                </div>
                                <div class="info-value-modern"><?php echo htmlspecialchars($userDetail['phone'] ?: '未填写'); ?></div>
                            </div>
                            <div class="info-item-modern">
                                <div class="info-label-modern">
                                    <i class="bi bi-person"></i> 姓名
                                </div>
                                <div class="info-value-modern"><?php echo htmlspecialchars($userDetail['real_name'] ?: '未填写'); ?></div>
                            </div>
                            <div class="info-item-modern">
                                <div class="info-label-modern">
                                    <i class="bi bi-calendar-plus"></i> 注册
                                </div>
                                <div class="info-value-modern"><?php echo date('Y-m-d H:i', strtotime($userDetail['created_at'])); ?></div>
                            </div>
                            <div class="info-item-modern">
                                <div class="info-label-modern">
                                    <i class="bi bi-clock"></i> 最后登录
                                </div>
                                <div class="info-value-modern"><?php echo $userDetail['last_login'] ? date('Y-m-d H:i', strtotime($userDetail['last_login'])) : '从未登录'; ?></div>
                            </div>
                            <div class="info-item-modern">
                                <div class="info-label-modern">
                                    <i class="bi bi-geo-alt"></i> 最后IP
                                </div>
                                <div class="info-value-modern">
                                    <code><?php echo htmlspecialchars($userDetail['last_ip'] ?: '未知'); ?></code>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- IP监控 -->
                    <div class="card-modern animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
                        <div class="card-header-modern">
                            <h6><i class="bi bi-shield-check"></i> IP监控</h6>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="refreshIPs()">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="showAllIPs()">
                                    <i class="bi bi-list"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body-modern p-0">
                            <?php if (empty($userIPs)): ?>
                                <div class="empty-state">
                                    <i class="bi bi-shield-x"></i>
                                    <h6 class="text-muted">暂无IP记录</h6>
                                    <p class="text-muted">用户还没有任何登录记录</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-dark">
                                            <tr>
                                                <th width="25%">IP地址</th>
                                                <th width="30%">位置</th>
                                                <th width="20%">状态</th>
                                                <th width="15%">次数</th>
                                                <th width="10%">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach (array_slice($userIPs, 0, 5) as $ip): ?>
                                                <?php
                                                $rowClass = '';
                                                if ($ip['is_blocked']) {
                                                    $rowClass = 'table-danger';
                                                } elseif ($ip['is_trusted']) {
                                                    $rowClass = 'table-success';
                                                } elseif (($ip['risk_score'] ?? 0) >= 70) {
                                                    $rowClass = 'table-warning';
                                                }
                                                ?>
                                                <tr class="<?php echo $rowClass; ?>">
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <?php
                                                            if (($ip['is_proxy'] ?? 0) == 1) {
                                                                echo '<i class="bi bi-shield-exclamation me-2 text-warning"></i>';
                                                            } elseif (($ip['risk_score'] ?? 0) >= 70) {
                                                                echo '<i class="bi bi-exclamation-triangle me-2 text-danger"></i>';
                                                            } else {
                                                                echo '<i class="bi bi-globe me-2 text-primary"></i>';
                                                            }
                                                            ?>
                                                            <div>
                                                                <code class="text-dark"><?php echo htmlspecialchars($ip['ip_address']); ?></code>
                                                                <br><small class="text-muted"><?php echo date('m-d H:i', strtotime($ip['last_seen'])); ?></small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <?php if (!empty($ip['country'])): ?>
                                                                <strong><?php echo htmlspecialchars($ip['country']); ?></strong>
                                                                <?php if (!empty($ip['country_code'])): ?>
                                                                    <span class="badge bg-light text-dark ms-1"><?php echo htmlspecialchars($ip['country_code']); ?></span>
                                                                <?php endif; ?>
                                                                <br>
                                                            <?php endif; ?>
                                                            <small class="text-muted">
                                                                <?php
                                                                $location_parts = array_filter([
                                                                    $ip['province'] ?? '',
                                                                    $ip['city'] ?? ''
                                                                ]);
                                                                echo htmlspecialchars(implode(' ', $location_parts) ?: '未知位置');
                                                                ?>
                                                            </small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex flex-column gap-1">
                                                            <?php if ($ip['is_trusted']): ?>
                                                                <span class="badge bg-success badge-modern">
                                                                    <i class="bi bi-shield-check"></i> 可信
                                                                </span>
                                                            <?php endif; ?>
                                                            <?php if ($ip['is_blocked']): ?>
                                                                <span class="badge bg-danger badge-modern">
                                                                    <i class="bi bi-shield-x"></i> 已阻止
                                                                </span>
                                                            <?php endif; ?>
                                                            <?php if (($ip['risk_score'] ?? 0) > 0): ?>
                                                                <?php
                                                                $riskScore = intval($ip['risk_score']);
                                                                $riskClass = $riskScore >= 70 ? 'bg-danger' : ($riskScore >= 40 ? 'bg-warning' : 'bg-success');
                                                                ?>
                                                                <span class="badge <?php echo $riskClass; ?> badge-modern">
                                                                    <i class="bi bi-shield"></i> <?php echo $riskScore; ?>
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-primary badge-modern"><?php echo $ip['login_count']; ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="dropdown">
                                                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button"
                                                                    data-bs-toggle="dropdown">
                                                                <i class="bi bi-three-dots"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item" href="#" onclick="showIPDetails('<?php echo $ip['ip_address']; ?>')">
                                                                    <i class="bi bi-info-circle"></i> 查看详情
                                                                </a></li>
                                                                <li><hr class="dropdown-divider"></li>
                                                                <?php if (!$ip['is_trusted']): ?>
                                                                    <li><a class="dropdown-item text-success" href="#" onclick="manageIP('trust', '<?php echo $ip['ip_address']; ?>')">
                                                                        <i class="bi bi-shield-check"></i> 设为可信
                                                                    </a></li>
                                                                <?php else: ?>
                                                                    <li><a class="dropdown-item text-warning" href="#" onclick="manageIP('untrust', '<?php echo $ip['ip_address']; ?>')">
                                                                        <i class="bi bi-shield-slash"></i> 取消信任
                                                                    </a></li>
                                                                <?php endif; ?>
                                                                <?php if (!$ip['is_blocked']): ?>
                                                                    <li><a class="dropdown-item text-danger" href="#" onclick="manageIP('block', '<?php echo $ip['ip_address']; ?>')">
                                                                        <i class="bi bi-shield-x"></i> 阻止访问
                                                                    </a></li>
                                                                <?php else: ?>
                                                                    <li><a class="dropdown-item text-info" href="#" onclick="manageIP('unblock', '<?php echo $ip['ip_address']; ?>')">
                                                                        <i class="bi bi-shield-plus"></i> 解除阻止
                                                                    </a></li>
                                                                <?php endif; ?>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                    <?php if (count($userIPs) > 5): ?>
                                        <div class="text-center p-3">
                                            <button class="btn btn-outline-primary btn-sm" onclick="showAllIPs()">
                                                查看全部 <?php echo count($userIPs); ?> 个IP地址
                                            </button>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- 最近活动 -->
                    <div class="card-modern animate__animated animate__fadeInUp" style="animation-delay: 0.5s">
                        <div class="card-header-modern">
                            <h6><i class="bi bi-clock-history"></i> 最近活动</h6>
                            <a href="../monitoring/logs.php?user_id=<?php echo $userId; ?>" class="btn btn-outline-light btn-sm">
                                查看全部
                            </a>
                        </div>
                        <div class="card-body-modern">
                            <?php if (empty($recentLogs)): ?>
                                <div class="empty-state">
                                    <i class="bi bi-clock-history"></i>
                                    <h6 class="text-muted">暂无活动记录</h6>
                                </div>
                            <?php else: ?>
                                <?php foreach (array_slice($recentLogs, 0, 5) as $log): ?>
                                    <div class="activity-item">
                                        <div class="activity-icon">
                                            <i class="bi bi-<?php echo ['user_login' => 'box-arrow-in-right', 'approve_user' => 'check-circle', 'ban_user' => 'ban', 'unban_user' => 'check-circle'][$log['action']] ?? 'activity'; ?>"></i>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">
                                                <?php 
                                                $actionNames = [
                                                    'user_login' => '用户登录',
                                                    'approve_user' => '用户审批',
                                                    'ban_user' => '用户封禁',
                                                    'unban_user' => '用户解封'
                                                ];
                                                echo $actionNames[$log['action']] ?? $log['action']; 
                                                ?>
                                            </div>
                                            <div class="activity-desc"><?php echo htmlspecialchars($log['description']); ?></div>
                                        </div>
                                        <div class="activity-time">
                                            <?php echo date('m-d H:i', strtotime($log['created_at'])); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- 封禁信息 -->
                    <?php if ($banInfo): ?>
                    <div class="card-modern animate__animated animate__fadeInUp" style="animation-delay: 0.6s">
                        <div class="card-header bg-danger text-white">
                            <h6><i class="bi bi-ban"></i> 封禁信息</h6>
                        </div>
                        <div class="card-body-modern">
                            <div class="info-item-modern">
                                <div class="info-label-modern">
                                    <i class="bi bi-calendar-x"></i> 封禁时间
                                </div>
                                <div class="info-value-modern"><?php echo date('Y-m-d H:i:s', strtotime($banInfo['banned_at'])); ?></div>
                            </div>
                            <div class="info-item-modern">
                                <div class="info-label-modern">
                                    <i class="bi bi-file-text"></i> 封禁原因
                                </div>
                                <div class="info-value-modern"><?php echo htmlspecialchars($banInfo['banned_reason']); ?></div>
                            </div>
                            <div class="info-item-modern">
                                <div class="info-label-modern">
                                    <i class="bi bi-person-badge"></i> 操作管理员
                                </div>
                                <div class="info-value-modern"><?php echo htmlspecialchars($banInfo['banned_by_admin']); ?></div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>

    <!-- 模态框区域 -->
    <!-- 封禁用户模态框 -->
    <div class="modal fade" id="banModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title"><i class="bi bi-ban"></i> 封禁用户</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="ban">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            确定要封禁用户 "<strong><?php echo htmlspecialchars($userDetail['username']); ?></strong>" 吗？
                        </div>
                        <div class="mb-3">
                            <label for="banReason" class="form-label">封禁原因 <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="reason" id="banReason" rows="3" 
                                      placeholder="请输入详细的封禁原因..." required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-ban"></i> 确认封禁
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 解封用户模态框 -->
    <div class="modal fade" id="unbanModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title"><i class="bi bi-check-circle"></i> 解封用户</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="unban">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            确定要解封用户 "<strong><?php echo htmlspecialchars($userDetail['username']); ?></strong>" 吗？解封后用户将恢复正常使用权限。
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check-circle"></i> 确认解封
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- IP管理模态框 -->
    <div class="modal fade" id="ipManageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-shield-check"></i> IP地址管理</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="bi bi-shield-check"></i> 可信IP地址</h6>
                                </div>
                                <div class="card-body">
                                    <?php
                                    $trustedIPs = array_filter($userIPs, function($ip) { return $ip['is_trusted']; });
                                    if (empty($trustedIPs)):
                                    ?>
                                        <div class="empty-state">
                                            <i class="bi bi-shield-check"></i>
                                            <p class="text-muted">暂无可信IP</p>
                                        </div>
                                    <?php else: ?>
                                        <?php foreach ($trustedIPs as $ip): ?>
                                            <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                                <div>
                                                    <code><?php echo htmlspecialchars($ip['ip_address']); ?></code>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($ip['location'] ?: '未知位置'); ?></small>
                                                </div>
                                                <button class="btn btn-sm btn-outline-warning" onclick="manageIP('untrust', '<?php echo $ip['ip_address']; ?>')">
                                                    取消信任
                                                </button>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0"><i class="bi bi-shield-x"></i> 被阻止IP地址</h6>
                                </div>
                                <div class="card-body">
                                    <?php
                                    $blockedIPs = array_filter($userIPs, function($ip) { return $ip['is_blocked']; });
                                    if (empty($blockedIPs)):
                                    ?>
                                        <div class="empty-state">
                                            <i class="bi bi-shield-x"></i>
                                            <p class="text-muted">暂无被阻止IP</p>
                                        </div>
                                    <?php else: ?>
                                        <?php foreach ($blockedIPs as $ip): ?>
                                            <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                                <div>
                                                    <code><?php echo htmlspecialchars($ip['ip_address']); ?></code>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($ip['location'] ?: '未知位置'); ?></small>
                                                </div>
                                                <button class="btn btn-sm btn-outline-info" onclick="manageIP('unblock', '<?php echo $ip['ip_address']; ?>')">
                                                    解除阻止
                                                </button>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="bi bi-plus-circle"></i> 手动添加IP管理</h6>
                                </div>
                                <div class="card-body">
                                    <form method="POST" action="" class="row g-3">
                                        <input type="hidden" name="action" value="manual_ip">
                                        <div class="col-md-6">
                                            <label for="manualIP" class="form-label">IP地址</label>
                                            <input type="text" class="form-control" name="ip_address" id="manualIP"
                                                   placeholder="例如: *************" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="ipAction" class="form-label">操作</label>
                                            <select class="form-select" name="ip_action" id="ipAction" required>
                                                <option value="">选择操作</option>
                                                <option value="trust">设为可信</option>
                                                <option value="block">设为阻止</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">&nbsp;</label>
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="bi bi-plus"></i> 添加
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 全部IP查看模态框 -->
    <div class="modal fade" id="allIPsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-list-ul"></i> 全部IP地址记录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>IP地址</th>
                                    <th>位置信息</th>
                                    <th>ISP</th>
                                    <th>安全状态</th>
                                    <th>登录次数</th>
                                    <th>首次访问</th>
                                    <th>最后访问</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($userIPs as $ip): ?>
                                    <tr>
                                        <td>
                                            <code><?php echo htmlspecialchars($ip['ip_address']); ?></code>
                                            <?php if (!empty($ip['country_code'])): ?>
                                                <span class="badge bg-light text-dark ms-1"><?php echo htmlspecialchars($ip['country_code']); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($ip['country'])): ?>
                                                <div><strong><?php echo htmlspecialchars($ip['country']); ?></strong></div>
                                            <?php endif; ?>
                                            <small class="text-muted">
                                                <?php
                                                $location_parts = array_filter([
                                                    $ip['province'] ?? '',
                                                    $ip['city'] ?? '',
                                                    $ip['district'] ?? ''
                                                ]);
                                                echo htmlspecialchars(implode(' ', $location_parts) ?: '未知位置');
                                                ?>
                                            </small>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars($ip['isp'] ?: '未知'); ?></small>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column gap-1">
                                                <?php if ($ip['is_trusted']): ?>
                                                    <span class="badge bg-success">可信</span>
                                                <?php endif; ?>
                                                <?php if ($ip['is_blocked']): ?>
                                                    <span class="badge bg-danger">已阻止</span>
                                                <?php endif; ?>
                                                <?php if (($ip['risk_score'] ?? 0) > 0): ?>
                                                    <?php
                                                    $riskScore = intval($ip['risk_score']);
                                                    $riskClass = $riskScore >= 70 ? 'bg-danger' : ($riskScore >= 40 ? 'bg-warning' : 'bg-success');
                                                    ?>
                                                    <span class="badge <?php echo $riskClass; ?>">风险 <?php echo $riskScore; ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-primary"><?php echo $ip['login_count']; ?></span></td>
                                        <td><small><?php echo date('Y-m-d H:i', strtotime($ip['first_seen'])); ?></small></td>
                                        <td><small><?php echo date('Y-m-d H:i', strtotime($ip['last_seen'])); ?></small></td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button"
                                                        data-bs-toggle="dropdown">
                                                    <i class="bi bi-three-dots"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="showIPDetails('<?php echo $ip['ip_address']; ?>')">
                                                        <i class="bi bi-info-circle"></i> 查看详情
                                                    </a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <?php if (!$ip['is_trusted']): ?>
                                                        <li><a class="dropdown-item text-success" href="#" onclick="manageIP('trust', '<?php echo $ip['ip_address']; ?>')">
                                                            <i class="bi bi-shield-check"></i> 设为可信
                                                        </a></li>
                                                    <?php else: ?>
                                                        <li><a class="dropdown-item text-warning" href="#" onclick="manageIP('untrust', '<?php echo $ip['ip_address']; ?>')">
                                                            <i class="bi bi-shield-slash"></i> 取消信任
                                                        </a></li>
                                                    <?php endif; ?>
                                                    <?php if (!$ip['is_blocked']): ?>
                                                        <li><a class="dropdown-item text-danger" href="#" onclick="manageIP('block', '<?php echo $ip['ip_address']; ?>')">
                                                            <i class="bi bi-shield-x"></i> 阻止访问
                                                        </a></li>
                                                    <?php else: ?>
                                                        <li><a class="dropdown-item text-info" href="#" onclick="manageIP('unblock', '<?php echo $ip['ip_address']; ?>')">
                                                            <i class="bi bi-shield-plus"></i> 解除阻止
                                                        </a></li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- IP详情模态框 -->
    <div class="modal fade" id="ipDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-info-circle"></i> IP地址详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="ipDetailsContent">
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="mt-2 text-muted">正在查询IP信息...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="refreshCurrentIPDetails()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新信息
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/theme-switcher.js"></script>
    <script>
        // 模态框管理
        function showBanModal() {
            new bootstrap.Modal(document.getElementById('banModal')).show();
        }

        function showUnbanModal() {
            new bootstrap.Modal(document.getElementById('unbanModal')).show();
        }

        function showIPManageModal() {
            new bootstrap.Modal(document.getElementById('ipManageModal')).show();
        }

        function showAllIPs() {
            new bootstrap.Modal(document.getElementById('allIPsModal')).show();
        }

        // IP管理功能
        function manageIP(action, ipAddress) {
            const actionTexts = {
                'trust': '信任',
                'untrust': '取消信任',
                'block': '阻止',
                'unblock': '解除阻止'
            };

            const actionText = actionTexts[action] || action;

            if (confirm(`确定要${actionText}IP地址 ${ipAddress} 吗？`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="${action}_ip">
                    <input type="hidden" name="ip_address" value="${ipAddress}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function refreshIPs() {
            location.reload();
        }

        // IP详情查看
        let currentIPAddress = '';

        function showIPDetails(ipAddress) {
            currentIPAddress = ipAddress;
            const modal = new bootstrap.Modal(document.getElementById('ipDetailsModal'));
            
            // 重置内容
            document.getElementById('ipDetailsContent').innerHTML = `
                <div class="text-center py-4">
                    <div class="loading-spinner"></div>
                    <p class="mt-2 text-muted">正在查询IP信息...</p>
                </div>
            `;
            
            modal.show();
            loadIPDetails(ipAddress);
        }

        function loadIPDetails(ipAddress) {
            fetch('get_ip_details.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'ip_address=' + encodeURIComponent(ipAddress)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayIPDetails(data.data);
                } else {
                    document.getElementById('ipDetailsContent').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            查询失败: ${data.error || '未知错误'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                document.getElementById('ipDetailsContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        网络错误: ${error.message}
                    </div>
                `;
            });
        }

        function displayIPDetails(ipData) {
            const riskClass = ipData.risk_score >= 70 ? 'danger' : (ipData.risk_score >= 40 ? 'warning' : 'success');
            
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-globe"></i> 基本信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="info-item-modern">
                                    <div class="info-label-modern">IP地址</div>
                                    <div class="info-value-modern"><code>${ipData.ip_address}</code></div>
                                </div>
                                <div class="info-item-modern">
                                    <div class="info-label-modern">ISP运营商</div>
                                    <div class="info-value-modern">${ipData.isp || '未知'}</div>
                                </div>
                                <div class="info-item-modern">
                                    <div class="info-label-modern">时区</div>
                                    <div class="info-value-modern">${ipData.time_zone || '未知'}</div>
                                </div>
                                <div class="info-item-modern">
                                    <div class="info-label-modern">数据源</div>
                                    <div class="info-value-modern">
                                        <span class="badge bg-info">${ipData.api_source || '未知'}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-geo-alt"></i> 地理位置</h6>
                            </div>
                            <div class="card-body">
                                <div class="info-item-modern">
                                    <div class="info-label-modern">国家</div>
                                    <div class="info-value-modern">
                                        ${ipData.country || '未知'}
                                        ${ipData.country_code ? '<span class="badge bg-light text-dark ms-1">' + ipData.country_code + '</span>' : ''}
                                    </div>
                                </div>
                                <div class="info-item-modern">
                                    <div class="info-label-modern">省份</div>
                                    <div class="info-value-modern">${ipData.province || '未知'}</div>
                                </div>
                                <div class="info-item-modern">
                                    <div class="info-label-modern">城市</div>
                                    <div class="info-value-modern">${ipData.city || '未知'}</div>
                                </div>
                                <div class="info-item-modern">
                                    <div class="info-label-modern">坐标</div>
                                    <div class="info-value-modern">
                                        ${ipData.latitude && ipData.longitude ?
                                            ipData.latitude + ', ' + ipData.longitude : '未知'}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-shield-exclamation"></i> 安全评估</h6>
                            </div>
                            <div class="card-body">
                                <div class="info-item-modern">
                                    <div class="info-label-modern">风险评分</div>
                                    <div class="info-value-modern">
                                        <div class="progress-modern mb-2">
                                            <div class="progress-bar-modern" style="width: ${ipData.risk_score}%"></div>
                                        </div>
                                        <span class="badge bg-${riskClass}">${ipData.risk_score}/100</span>
                                    </div>
                                </div>
                                <div class="info-item-modern">
                                    <div class="info-label-modern">是否代理</div>
                                    <div class="info-value-modern">
                                        ${ipData.is_proxy == 1 ?
                                            '<span class="badge bg-warning">是</span>' :
                                            '<span class="badge bg-success">否</span>'}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-clock-history"></i> 访问记录</h6>
                            </div>
                            <div class="card-body">
                                <div class="info-item-modern">
                                    <div class="info-label-modern">首次访问</div>
                                    <div class="info-value-modern">${ipData.first_seen || '未知'}</div>
                                </div>
                                <div class="info-item-modern">
                                    <div class="info-label-modern">最后访问</div>
                                    <div class="info-value-modern">${ipData.last_seen || '未知'}</div>
                                </div>
                                <div class="info-item-modern">
                                    <div class="info-label-modern">登录次数</div>
                                    <div class="info-value-modern">
                                        <span class="badge bg-primary">${ipData.login_count || 0}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('ipDetailsContent').innerHTML = content;
        }

        function refreshCurrentIPDetails() {
            if (currentIPAddress) {
                document.getElementById('ipDetailsContent').innerHTML = `
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="mt-2 text-muted">正在刷新IP信息...</p>
                    </div>
                `;
                loadIPDetails(currentIPAddress);
            }
        }

        // 导出用户数据
        function exportUserData() {
            const userData = {
                user_id: <?php echo $userId; ?>,
                username: '<?php echo htmlspecialchars($userDetail['username']); ?>',
                email: '<?php echo htmlspecialchars($userDetail['email']); ?>',
                status: '<?php echo $userDetail['status']; ?>',
                created_at: '<?php echo $userDetail['created_at']; ?>',
                last_login: '<?php echo $userDetail['last_login']; ?>',
                login_count: <?php echo $userDetail['login_count']; ?>,
                ip_count: <?php echo count($userIPs); ?>
            };

            const dataStr = JSON.stringify(userData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.setAttribute('href', url);
            link.setAttribute('download', `user_${userData.user_id}_data.json`);
            link.click();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加页面动画效果
            const cards = document.querySelectorAll('.card-modern');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>