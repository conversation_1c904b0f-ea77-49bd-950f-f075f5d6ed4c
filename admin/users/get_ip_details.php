<?php
/**
 * 获取IP详情API
 * 为用户详情页提供IP详细信息查询
 */

session_start();
require_once '../../api/config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/IPQueryService.php';

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => '不支持的请求方法']);
    exit;
}

// 获取IP地址参数
$ipAddress = $_POST['ip_address'] ?? '';

if (empty($ipAddress)) {
    echo json_encode(['success' => false, 'error' => 'IP地址不能为空']);
    exit;
}

// 验证IP地址格式
if (!filter_var($ipAddress, FILTER_VALIDATE_IP)) {
    echo json_encode(['success' => false, 'error' => '无效的IP地址格式']);
    exit;
}

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // 首先从ip_monitors表获取基本信息
    $sql = "SELECT * FROM ip_monitors WHERE ip_address = ? LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$ipAddress]);
    $ipRecord = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$ipRecord) {
        // 如果ip_monitors表中没有记录，尝试从缓存表获取
        $sql = "SELECT * FROM ip_query_cache WHERE ip_address = ? LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$ipAddress]);
        $cacheRecord = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($cacheRecord) {
            // 使用缓存数据
            $ipData = [
                'ip_address' => $cacheRecord['ip_address'],
                'ip_int' => $cacheRecord['ip_int'],
                'continent' => $cacheRecord['continent'],
                'country' => $cacheRecord['country'],
                'country_code' => $cacheRecord['country_code'],
                'province' => $cacheRecord['province'],
                'city' => $cacheRecord['city'],
                'district' => $cacheRecord['district'],
                'street' => $cacheRecord['street'],
                'isp' => $cacheRecord['isp'],
                'latitude' => $cacheRecord['latitude'],
                'longitude' => $cacheRecord['longitude'],
                'area_code' => $cacheRecord['area_code'],
                'zip_code' => $cacheRecord['zip_code'],
                'time_zone' => $cacheRecord['time_zone'],
                'location' => $cacheRecord['location'],
                'street_history' => $cacheRecord['street_history'],
                'risk_score' => $cacheRecord['risk_score'],
                'risk_level' => $cacheRecord['risk_level'],
                'is_proxy' => $cacheRecord['is_proxy'],
                'proxy_type' => $cacheRecord['proxy_type'],
                'risk_tag' => $cacheRecord['risk_tag'],
                'api_source' => $cacheRecord['api_source'],
                'first_seen' => $cacheRecord['created_at'],
                'last_seen' => $cacheRecord['updated_at'],
                'last_updated' => $cacheRecord['updated_at'],
                'login_count' => 0
            ];
        } else {
            // 使用IP查询服务获取新数据
            $ipService = new IPQueryService($conn);
            $result = $ipService->queryIP($ipAddress);
            
            if (!$result['success']) {
                echo json_encode(['success' => false, 'error' => $result['error']]);
                exit;
            }
            
            $ipData = $result['data'];
            $ipData['first_seen'] = date('Y-m-d H:i:s');
            $ipData['last_seen'] = date('Y-m-d H:i:s');
            $ipData['last_updated'] = date('Y-m-d H:i:s');
            $ipData['login_count'] = 0;
        }
    } else {
        // 使用ip_monitors表的数据
        $ipData = $ipRecord;
        
        // 如果某些字段为空，尝试从缓存表补充
        if (empty($ipData['country']) || empty($ipData['risk_score'])) {
            $sql = "SELECT * FROM ip_query_cache WHERE ip_address = ? LIMIT 1";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$ipAddress]);
            $cacheRecord = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($cacheRecord) {
                // 补充缺失的字段
                $fieldsToUpdate = [
                    'ip_int', 'continent', 'country', 'country_code', 'province', 
                    'city', 'district', 'street', 'isp', 'latitude', 'longitude',
                    'area_code', 'zip_code', 'time_zone', 'street_history',
                    'risk_score', 'risk_level', 'is_proxy', 'proxy_type', 'risk_tag'
                ];
                
                foreach ($fieldsToUpdate as $field) {
                    if (empty($ipData[$field]) && !empty($cacheRecord[$field])) {
                        $ipData[$field] = $cacheRecord[$field];
                    }
                }
            }
        }
    }
    
    // 格式化数据
    $responseData = [
        'ip_address' => $ipData['ip_address'],
        'ip_int' => $ipData['ip_int'] ?? null,
        'location' => $ipData['location'] ?? '未知位置',
        'continent' => $ipData['continent'] ?? null,
        'country' => $ipData['country'] ?? null,
        'country_code' => $ipData['country_code'] ?? null,
        'province' => $ipData['province'] ?? null,
        'city' => $ipData['city'] ?? null,
        'district' => $ipData['district'] ?? null,
        'street' => $ipData['street'] ?? null,
        'isp' => $ipData['isp'] ?? null,
        'latitude' => $ipData['latitude'] ?? null,
        'longitude' => $ipData['longitude'] ?? null,
        'area_code' => $ipData['area_code'] ?? null,
        'zip_code' => $ipData['zip_code'] ?? null,
        'time_zone' => $ipData['time_zone'] ?? null,
        'street_history' => $ipData['street_history'] ?? null,
        'risk_score' => intval($ipData['risk_score'] ?? 0),
        'risk_level' => $ipData['risk_level'] ?? '未知',
        'is_proxy' => intval($ipData['is_proxy'] ?? 0),
        'proxy_type' => $ipData['proxy_type'] ?? null,
        'risk_tag' => $ipData['risk_tag'] ?? null,
        'api_source' => $ipData['api_source'] ?? '未知',
        'first_seen' => $ipData['first_seen'] ?? null,
        'last_seen' => $ipData['last_seen'] ?? null,
        'last_updated' => $ipData['last_updated'] ?? null,
        'login_count' => intval($ipData['login_count'] ?? 0)
    ];
    
    // 格式化时间显示
    if ($responseData['first_seen']) {
        $responseData['first_seen'] = date('Y-m-d H:i:s', strtotime($responseData['first_seen']));
    }
    if ($responseData['last_seen']) {
        $responseData['last_seen'] = date('Y-m-d H:i:s', strtotime($responseData['last_seen']));
    }
    if ($responseData['last_updated']) {
        $responseData['last_updated'] = date('Y-m-d H:i:s', strtotime($responseData['last_updated']));
    }
    
    echo json_encode([
        'success' => true,
        'data' => $responseData
    ]);
    
} catch (Exception $e) {
    error_log("获取IP详情失败: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => '服务器内部错误: ' . $e->getMessage()
    ]);
}
?>
