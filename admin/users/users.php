<?php
/**
 * 智能用户管理中心 💖 - 现代化版本
 * 管理系统 - management.djxs.xyz
 */

require_once __DIR__ . '/../../includes/admin.php';

$admin = new Admin();

// 检查登录状态
if (!$admin->checkLogin()) {
    header('Location: ../auth/login.php');
    exit();
}

session_start();
$adminId = $_SESSION['admin_id'];
$message = '';
$messageType = '';

// 处理用户操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $userId = intval($_POST['user_id'] ?? 0);
    $userIds = $_POST['user_ids'] ?? [];
    $reason = trim($_POST['reason'] ?? '');

    if ($action === 'ban' && $userId > 0) {
        $result = $admin->banUser($userId, $adminId, $reason);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'unban' && $userId > 0) {
        $result = $admin->unbanUser($userId, $adminId);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'delete' && $userId > 0) {
        $result = $admin->deleteUser($userId, $adminId, $reason);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'batch_ban' && !empty($userIds)) {
        $result = $admin->batchBanUsers($userIds, $adminId, $reason);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'batch_unban' && !empty($userIds)) {
        $result = $admin->batchUnbanUsers($userIds, $adminId);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'batch_delete' && !empty($userIds)) {
        $result = $admin->batchDeleteUsers($userIds, $adminId, $reason);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    }

    // POST-Redirect-GET 模式
    if ($message) {
        $redirectUrl = "users.php?message=" . urlencode($message) . "&type=" . $messageType;
        if (isset($_GET['page'])) $redirectUrl .= "&page=" . $_GET['page'];
        if (isset($_GET['status'])) $redirectUrl .= "&status=" . $_GET['status'];
        if (isset($_GET['search'])) $redirectUrl .= "&search=" . urlencode($_GET['search']);
        header("Location: $redirectUrl");
        exit();
    }
}

// 处理GET消息
if (isset($_GET['message'])) {
    $message = $_GET['message'];
    $messageType = $_GET['type'] ?? 'info';
}

// 获取筛选参数
$page = intval($_GET['page'] ?? 1);
$status = $_GET['status'] ?? '';
$search = trim($_GET['search'] ?? '');
$sortBy = $_GET['sort'] ?? 'created_desc';
$dateRange = $_GET['date_range'] ?? '';
$permissionType = $_GET['permission_type'] ?? '';
$limit = 12; // 调整为更合适的分页大小

// 获取数据库连接
require_once '../../api/config/database.php';
$database = new Database();
$conn = $database->getConnection();

// 构建查询条件
$whereConditions = [];
$params = [];

// 状态筛选
if (!empty($status)) {
    $whereConditions[] = "u.status = ?";
    $params[] = $status;
}

// 搜索条件
if (!empty($search)) {
    $whereConditions[] = "(u.username LIKE ? OR u.email LIKE ? OR u.real_name LIKE ?)";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
}

// 日期范围筛选
if (!empty($dateRange)) {
    switch ($dateRange) {
        case 'today':
            $whereConditions[] = "DATE(u.created_at) = CURDATE()";
            break;
        case 'week':
            $whereConditions[] = "u.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            break;
        case 'month':
            $whereConditions[] = "u.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            break;
        case 'year':
            $whereConditions[] = "u.created_at >= DATE_SUB(NOW(), INTERVAL 365 DAY)";
            break;
    }
}

// 权限类型筛选
if (!empty($permissionType)) {
    if ($permissionType === 'none') {
        $whereConditions[] = "app_perms.user_id IS NULL";
    } else {
        $whereConditions[] = "app_perms.app_type = ? AND app_perms.is_active = 1";
        $params[] = $permissionType;
    }
}

// 排序条件
$orderBy = match($sortBy) {
    'created_asc' => 'u.created_at ASC',
    'username_asc' => 'u.username ASC',
    'username_desc' => 'u.username DESC',
    'login_count_desc' => 'u.login_count DESC',
    'last_login_desc' => 'u.last_login DESC',
    default => 'u.created_at DESC'
};

// 构建WHERE子句
$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// 查询用户数据
$offset = ($page - 1) * $limit;
$sql = "SELECT u.*, 
        GROUP_CONCAT(DISTINCT CASE WHEN app_perms.is_active = 1 THEN app_perms.app_type END) as active_apps,
        COUNT(DISTINCT CASE WHEN app_perms.is_active = 1 THEN app_perms.app_type END) as active_app_count,
        COUNT(DISTINCT CASE WHEN module_perms.is_active = 1 THEN module_perms.module_code END) as active_module_count,
        MAX(CASE WHEN app_perms.app_type = 'android' THEN app_perms.download_count END) as android_downloads
        FROM users u
        LEFT JOIN user_app_permissions app_perms ON u.id = app_perms.user_id
        LEFT JOIN user_module_permissions module_perms ON u.id = module_perms.user_id
        {$whereClause}
        GROUP BY u.id
        ORDER BY {$orderBy}
        LIMIT ? OFFSET ?";

$countSql = "SELECT COUNT(DISTINCT u.id) as total
             FROM users u
             LEFT JOIN user_app_permissions app_perms ON u.id = app_perms.user_id
             LEFT JOIN user_module_permissions module_perms ON u.id = module_perms.user_id
             {$whereClause}";

// 执行查询
$stmt = $conn->prepare($sql);
$stmt->execute([...$params, $limit, $offset]);
$users = $stmt->fetchAll();

$countStmt = $conn->prepare($countSql);
$countStmt->execute($params);
$total = $countStmt->fetchColumn();
$totalPages = ceil($total / $limit);

// 获取统计数据
$statsSql = "SELECT
    COUNT(*) as total_users,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
    COUNT(CASE WHEN status = 'banned' THEN 1 END) as banned_users,
    COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_users,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as new_users_week,
    COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_users_week
    FROM users";
$statsStmt = $conn->prepare($statsSql);
$statsStmt->execute();
$stats = $statsStmt->fetch();

// 为每个用户添加权限详细信息
foreach ($users as &$user) {
    // 获取应用权限详情
    $appSql = "SELECT app_type, download_count, expires_at, is_active FROM user_app_permissions
               WHERE user_id = ? ORDER BY created_at DESC";
    $appStmt = $conn->prepare($appSql);
    $appStmt->execute([$user['id']]);
    $user['app_permissions'] = $appStmt->fetchAll();
    
    // 计算过期权限
    $user['expired_permissions'] = 0;
    foreach ($user['app_permissions'] as $perm) {
        if ($perm['expires_at'] && strtotime($perm['expires_at']) <= time() && $perm['is_active']) {
            $user['expired_permissions']++;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能用户管理中心 💖 - 管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <link href="../assets/css/admin-theme.css" rel="stylesheet">
    <style>
        /* 现代化用户管理界面样式 */
        :root {
            --primary-color: #6366f1;
            --success-color: #22c55e;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-radius: 16px;
            --shadow-sm: 0 2px 8px rgba(0,0,0,0.08);
            --shadow-md: 0 4px 20px rgba(0,0,0,0.12);
            --shadow-lg: 0 8px 32px rgba(0,0,0,0.16);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            color: var(--dark-color);
        }

        /* 页面头部 */
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-md);
            position: sticky;
            top: 20px;
            z-index: 100;
        }

        .page-title {
            background: linear-gradient(135deg, var(--primary-color), #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            font-size: 2.5rem;
            margin: 0;
        }

        .page-subtitle {
            color: #6b7280;
            font-size: 1.1rem;
            margin-top: 0.5rem;
        }

        /* 统计卡片网格 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
            transition: var(--transition);
        }

        .stat-card.success::before { background: var(--success-color); }
        .stat-card.warning::before { background: var(--warning-color); }
        .stat-card.danger::before { background: var(--danger-color); }
        .stat-card.info::before { background: var(--info-color); }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: rgba(99, 102, 241, 0.2);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #6b7280;
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .stat-change.positive { color: var(--success-color); }
        .stat-change.negative { color: var(--danger-color); }

        /* 搜索和筛选区域 */
        .search-filters {
            background: rgba(255, 255, 255, 0.95);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
        }

        .search-input {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
            transition: var(--transition);
            background: #f9fafb;
        }

        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            outline: none;
            background: white;
        }

        .filter-tabs {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .filter-tab {
            padding: 0.5rem 1rem;
            border-radius: 50px;
            background: #f1f5f9;
            border: 2px solid transparent;
            color: #64748b;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
        }

        .filter-tab:hover {
            background: #e2e8f0;
            color: #475569;
        }

        .filter-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 用户卡片网格 */
        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .user-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
            border: 2px solid transparent;
            position: relative;
            cursor: pointer;
        }

        .user-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: rgba(99, 102, 241, 0.2);
        }

        .user-card.selected {
            border-color: var(--primary-color);
            background: rgba(99, 102, 241, 0.05);
        }

        .user-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.1rem;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .user-info h6 {
            margin: 0;
            font-weight: 600;
            color: var(--dark-color);
        }

        .user-info .user-id {
            color: #6b7280;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        .user-details {
            margin-bottom: 1rem;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.85rem;
            color: #6b7280;
        }

        .detail-item i {
            width: 16px;
            color: var(--primary-color);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-badge.active {
            background: rgba(34, 197, 94, 0.1);
            color: var(--success-color);
        }

        .status-badge.banned {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .status-badge.suspended {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .permissions-summary {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .permission-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .permission-badge.app {
            background: rgba(34, 197, 94, 0.1);
            color: var(--success-color);
        }

        .permission-badge.module {
            background: rgba(59, 130, 246, 0.1);
            color: var(--info-color);
        }

        .permission-badge.expired {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .user-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: space-between;
            align-items: center;
        }

        .user-select {
            position: absolute;
            top: 1rem;
            right: 1rem;
        }

        .user-select input[type="checkbox"] {
            width: 18px;
            height: 18px;
            border-radius: 4px;
        }

        /* 批量操作栏 */
        .batch-actions {
            background: rgba(255, 255, 255, 0.95);
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
            display: none;
            align-items: center;
            justify-content: space-between;
        }

        .batch-actions.show {
            display: flex;
        }

        .batch-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: var(--dark-color);
            font-weight: 500;
        }

        .batch-buttons {
            display: flex;
            gap: 0.75rem;
        }

        /* 按钮样式 */
        .btn-modern {
            border-radius: 12px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: var(--transition);
            border: 2px solid transparent;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .btn-modern.btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-modern.btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-modern.btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-modern.btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-modern.btn-outline-primary {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: transparent;
        }

        .btn-modern.btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
        }

        /* 分页样式 */
        .pagination-modern {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }

        .page-link-modern {
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid #e5e7eb;
            color: #6b7280;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }

        .page-link-modern:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .page-link-modern.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: #6b7280;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            backdrop-filter: blur(4px);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .page-header {
                padding: 1.5rem;
                position: static;
            }

            .page-title {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .users-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .search-filters {
                padding: 1rem;
            }

            .filter-tabs {
                gap: 0.25rem;
            }

            .filter-tab {
                padding: 0.375rem 0.75rem;
                font-size: 0.8rem;
            }

            .batch-actions {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .batch-buttons {
                justify-content: center;
                flex-wrap: wrap;
            }
        }

        @media (max-width: 576px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .user-card {
                padding: 1rem;
            }

            .user-header {
                gap: 0.75rem;
            }

            .user-avatar {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-in {
            animation: slideIn 0.4s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* 工具提示 */
        .tooltip-modern {
            position: relative;
        }

        .tooltip-modern::before {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--dark-color);
            color: white;
            padding: 0.5rem;
            border-radius: 6px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
            z-index: 1000;
        }

        .tooltip-modern:hover::before {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php
            // 获取统计数据用于侧边栏
            $sidebarStats = ['pending_users' => 0];
            try {
                $pendingSql = "SELECT COUNT(*) as pending_users FROM pending_users WHERE status = 'pending'";
                $pendingStmt = $conn->prepare($pendingSql);
                $pendingStmt->execute();
                $sidebarStats = $pendingStmt->fetch();
            } catch (Exception $e) {
                // 忽略错误，使用默认值
            }
            include '../includes/sidebar.php';
            ?>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-3">
                <!-- 消息提示 -->
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show animate__animated animate__fadeInDown" role="alert">
                        <i class="bi bi-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- 页面头部 -->
                <div class="page-header animate__animated animate__fadeInUp">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="page-title">智能用户管理中心</h1>
                            <p class="page-subtitle">全方位用户管理解决方案 • 当前显示 <?php echo count($users); ?> / <?php echo $total; ?> 个用户</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="d-flex gap-2 justify-content-end">
                                <a href="pending.php" class="btn btn-modern btn-success">
                                    <i class="bi bi-person-plus"></i> 审批用户
                                </a>
                                <button type="button" class="btn btn-modern btn-primary" onclick="exportUsers()">
                                    <i class="bi bi-download"></i> 导出数据
                                </button>
                                <button type="button" class="btn btn-modern btn-outline-primary" onclick="location.reload()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计概览 -->
                <div class="stats-grid animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_users']; ?></div>
                        <div class="stat-label">总用户数</div>
                        <div class="stat-change positive">
                            <i class="bi bi-arrow-up"></i>
                            +<?php echo $stats['new_users_week']; ?> 本周新增
                        </div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number"><?php echo $stats['active_users']; ?></div>
                        <div class="stat-label">活跃用户</div>
                        <div class="stat-change positive">
                            <i class="bi bi-activity"></i>
                            <?php echo $stats['active_users_week']; ?> 本周活跃
                        </div>
                    </div>
                    <div class="stat-card danger">
                        <div class="stat-number"><?php echo $stats['banned_users']; ?></div>
                        <div class="stat-label">封禁用户</div>
                        <div class="stat-change <?php echo $stats['banned_users'] > 0 ? 'negative' : 'positive'; ?>">
                            <i class="bi bi-shield-x"></i>
                            需要关注
                        </div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number"><?php echo $stats['suspended_users']; ?></div>
                        <div class="stat-label">暂停用户</div>
                        <div class="stat-change">
                            <i class="bi bi-pause-circle"></i>
                            临时状态
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filters animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                    <form method="GET" id="searchForm" class="row align-items-end">
                        <div class="col-md-4">
                            <label class="form-label fw-bold">搜索用户</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-0">
                                    <i class="bi bi-search text-primary"></i>
                                </span>
                                <input type="text" class="form-control search-input border-start-0" name="search"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="搜索用户名、邮箱、姓名..."
                                       id="searchInput">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label fw-bold">日期范围</label>
                            <select class="form-select search-input" name="date_range">
                                <option value="">全部时间</option>
                                <option value="today" <?php echo $dateRange === 'today' ? 'selected' : ''; ?>>今天</option>
                                <option value="week" <?php echo $dateRange === 'week' ? 'selected' : ''; ?>>本周</option>
                                <option value="month" <?php echo $dateRange === 'month' ? 'selected' : ''; ?>>本月</option>
                                <option value="year" <?php echo $dateRange === 'year' ? 'selected' : ''; ?>>今年</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label fw-bold">权限类型</label>
                            <select class="form-select search-input" name="permission_type">
                                <option value="">全部权限</option>
                                <option value="android" <?php echo $permissionType === 'android' ? 'selected' : ''; ?>>Android</option>
                                <option value="windows" <?php echo $permissionType === 'windows' ? 'selected' : ''; ?>>Windows</option>
                                <option value="none" <?php echo $permissionType === 'none' ? 'selected' : ''; ?>>无权限</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label fw-bold">排序方式</label>
                            <select class="form-select search-input" name="sort">
                                <option value="created_desc" <?php echo $sortBy === 'created_desc' ? 'selected' : ''; ?>>注册时间↓</option>
                                <option value="created_asc" <?php echo $sortBy === 'created_asc' ? 'selected' : ''; ?>>注册时间↑</option>
                                <option value="username_asc" <?php echo $sortBy === 'username_asc' ? 'selected' : ''; ?>>用户名A-Z</option>
                                <option value="username_desc" <?php echo $sortBy === 'username_desc' ? 'selected' : ''; ?>>用户名Z-A</option>
                                <option value="login_count_desc" <?php echo $sortBy === 'login_count_desc' ? 'selected' : ''; ?>>登录次数↓</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-modern btn-primary">
                                    <i class="bi bi-search"></i> 搜索
                                </button>
                                <a href="users.php" class="btn btn-modern btn-outline-primary">
                                    <i class="bi bi-x-circle"></i>
                                </a>
                            </div>
                        </div>
                        <!-- 保持当前状态 -->
                        <?php if ($status): ?>
                            <input type="hidden" name="status" value="<?php echo $status; ?>">
                        <?php endif; ?>
                    </form>

                    <!-- 状态筛选标签 -->
                    <div class="filter-tabs">
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['status' => '', 'page' => 1])); ?>" 
                           class="filter-tab <?php echo $status === '' ? 'active' : ''; ?>">
                            <i class="bi bi-people"></i>
                            全部用户 (<?php echo $stats['total_users']; ?>)
                        </a>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['status' => 'active', 'page' => 1])); ?>" 
                           class="filter-tab <?php echo $status === 'active' ? 'active' : ''; ?>">
                            <i class="bi bi-person-check"></i>
                            活跃用户 (<?php echo $stats['active_users']; ?>)
                        </a>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['status' => 'banned', 'page' => 1])); ?>" 
                           class="filter-tab <?php echo $status === 'banned' ? 'active' : ''; ?>">
                            <i class="bi bi-person-x"></i>
                            封禁用户 (<?php echo $stats['banned_users']; ?>)
                        </a>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['status' => 'suspended', 'page' => 1])); ?>" 
                           class="filter-tab <?php echo $status === 'suspended' ? 'active' : ''; ?>">
                            <i class="bi bi-pause-circle"></i>
                            暂停用户 (<?php echo $stats['suspended_users']; ?>)
                        </a>
                    </div>
                </div>

                <!-- 批量操作栏 -->
                <div class="batch-actions" id="batchActions">
                    <div class="batch-info">
                        <i class="bi bi-check-circle text-primary"></i>
                        已选择 <span id="selectedCount">0</span> 个用户
                    </div>
                    <div class="batch-buttons">
                        <button type="button" class="btn btn-modern btn-danger" onclick="showBatchBanModal()">
                            <i class="bi bi-ban"></i> 批量封禁
                        </button>
                        <button type="button" class="btn btn-modern btn-success" onclick="showBatchUnbanModal()">
                            <i class="bi bi-check-circle"></i> 批量解封
                        </button>
                        <button type="button" class="btn btn-modern btn-warning" onclick="showBatchDeleteModal()">
                            <i class="bi bi-trash"></i> 批量删除
                        </button>
                        <button type="button" class="btn btn-modern btn-outline-primary" onclick="clearSelection()">
                            <i class="bi bi-x"></i> 取消选择
                        </button>
                    </div>
                </div>

                <!-- 用户卡片网格 -->
                <?php if (empty($users)): ?>
                    <div class="empty-state animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                        <i class="bi bi-people"></i>
                        <h3 class="text-muted">暂无用户数据</h3>
                        <p class="text-muted">尝试调整搜索条件或筛选器</p>
                        <button type="button" class="btn btn-modern btn-primary" onclick="location.href='users.php'">
                            <i class="bi bi-arrow-clockwise"></i> 重置筛选条件
                        </button>
                    </div>
                <?php else: ?>
                    <div class="users-grid animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                        <?php foreach ($users as $index => $user): ?>
                            <div class="user-card fade-in" style="animation-delay: <?php echo ($index * 0.05); ?>s" data-user-id="<?php echo $user['id']; ?>">
                                <!-- 选择框 -->
                                <div class="user-select">
                                    <input type="checkbox" class="form-check-input user-checkbox" 
                                           value="<?php echo $user['id']; ?>" 
                                           id="user_<?php echo $user['id']; ?>"
                                           onchange="updateSelection()">
                                </div>

                                <!-- 用户头部 -->
                                <div class="user-header">
                                    <div class="user-avatar">
                                        <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                                    </div>
                                    <div class="user-info flex-grow-1">
                                        <h6>
                                            <a href="user_detail.php?id=<?php echo $user['id']; ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($user['username']); ?>
                                            </a>
                                        </h6>
                                        <div class="user-id">ID: #<?php echo $user['id']; ?></div>
                                    </div>
                                    <div>
                                        <?php
                                        $statusClass = 'status-badge ' . $user['status'];
                                        $statusText = [
                                            'active' => '活跃',
                                            'banned' => '封禁',
                                            'suspended' => '暂停'
                                        ][$user['status']] ?? $user['status'];
                                        $statusIcon = [
                                            'active' => 'bi-check-circle',
                                            'banned' => 'bi-x-circle',
                                            'suspended' => 'bi-pause-circle'
                                        ][$user['status']] ?? 'bi-question-circle';
                                        ?>
                                        <span class="<?php echo $statusClass; ?>">
                                            <i class="bi <?php echo $statusIcon; ?>"></i>
                                            <?php echo $statusText; ?>
                                        </span>
                                    </div>
                                </div>

                                <!-- 用户详情 -->
                                <div class="user-details">
                                    <div class="detail-item">
                                        <i class="bi bi-envelope"></i>
                                        <span><?php echo htmlspecialchars($user['email']); ?></span>
                                    </div>
                                    <?php if ($user['real_name']): ?>
                                    <div class="detail-item">
                                        <i class="bi bi-person"></i>
                                        <span><?php echo htmlspecialchars($user['real_name']); ?></span>
                                    </div>
                                    <?php endif; ?>
                                    <div class="detail-item">
                                        <i class="bi bi-calendar"></i>
                                        <span>注册于 <?php echo date('Y-m-d', strtotime($user['created_at'])); ?></span>
                                    </div>
                                    <div class="detail-item">
                                        <i class="bi bi-clock"></i>
                                        <span>登录 <?php echo $user['login_count']; ?> 次 • 最后 <?php echo $user['last_login'] ? date('m-d H:i', strtotime($user['last_login'])) : '从未'; ?></span>
                                    </div>
                                </div>

                                <!-- 权限概要 -->
                                <div class="permissions-summary">
                                    <?php if ($user['active_app_count'] > 0): ?>
                                        <span class="permission-badge app">
                                            <i class="bi bi-app"></i> <?php echo $user['active_app_count']; ?>个应用权限
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($user['active_module_count'] > 0): ?>
                                        <span class="permission-badge module">
                                            <i class="bi bi-grid-3x3-gap"></i> <?php echo $user['active_module_count']; ?>个模块权限
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($user['expired_permissions'] > 0): ?>
                                        <span class="permission-badge expired">
                                            <i class="bi bi-clock-history"></i> <?php echo $user['expired_permissions']; ?>个已过期
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($user['android_downloads']): ?>
                                        <span class="permission-badge app">
                                            <i class="bi bi-download"></i> Android下载 <?php echo $user['android_downloads']; ?>次
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <!-- 用户操作 -->
                                <div class="user-actions">
                                    <div class="d-flex gap-1">
                                        <a href="user_detail.php?id=<?php echo $user['id']; ?>" 
                                           class="btn btn-modern btn-outline-primary btn-sm">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="../permissions/advanced_permissions.php?user_id=<?php echo $user['id']; ?>" 
                                           class="btn btn-modern btn-outline-primary btn-sm">
                                            <i class="bi bi-gear"></i>
                                        </a>
                                    </div>

                                    <div class="d-flex gap-1">
                                        <?php if ($user['status'] === 'active'): ?>
                                            <button type="button" class="btn btn-modern btn-danger btn-sm"
                                                    onclick="showBanModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                                <i class="bi bi-ban"></i>
                                            </button>
                                        <?php elseif ($user['status'] === 'banned'): ?>
                                            <button type="button" class="btn btn-modern btn-success btn-sm"
                                                    onclick="showUnbanModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                                <i class="bi bi-check-circle"></i>
                                            </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-modern btn-warning btn-sm"
                                                onclick="showDeleteModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <!-- 分页导航 -->
                <?php if ($totalPages > 1): ?>
                    <div class="pagination-modern animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
                        <?php if ($page > 1): ?>
                            <a class="page-link-modern" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                <i class="bi bi-chevron-left"></i> 上一页
                            </a>
                        <?php endif; ?>

                        <?php
                        $start = max(1, $page - 2);
                        $end = min($totalPages, $page + 2);
                        for ($i = $start; $i <= $end; $i++):
                        ?>
                            <a class="page-link-modern <?php echo $i === $page ? 'active' : ''; ?>" 
                               href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <a class="page-link-modern" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                下一页 <i class="bi bi-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </div>

                    <div class="text-center mt-3">
                        <small class="text-muted">
                            显示第 <?php echo ($page - 1) * $limit + 1; ?> - <?php echo min($page * $limit, $total); ?> 条，共 <?php echo $total; ?> 条记录
                        </small>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <!-- 模态框区域 -->
    <!-- 封禁用户模态框 -->
    <div class="modal fade" id="banModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title"><i class="bi bi-ban"></i> 封禁用户</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="ban">
                        <input type="hidden" name="user_id" id="banUserId">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            确定要封禁用户 "<span id="banUsername" class="fw-bold"></span>" 吗？
                        </div>
                        <div class="mb-3">
                            <label for="banReason" class="form-label">封禁原因 <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="reason" id="banReason" rows="3" 
                                      placeholder="请输入详细的封禁原因..." required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-ban"></i> 确认封禁
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 解封用户模态框 -->
    <div class="modal fade" id="unbanModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title"><i class="bi bi-check-circle"></i> 解封用户</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="unban">
                        <input type="hidden" name="user_id" id="unbanUserId">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            确定要解封用户 "<span id="unbanUsername" class="fw-bold"></span>" 吗？
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check-circle"></i> 确认解封
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除用户模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-white">
                    <h5 class="modal-title"><i class="bi bi-trash"></i> 删除用户</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="user_id" id="deleteUserId">
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>危险操作！</strong>确定要删除用户 "<span id="deleteUsername" class="fw-bold"></span>" 吗？
                        </div>
                        <div class="mb-3">
                            <label for="deleteReason" class="form-label">删除原因 <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="reason" id="deleteReason" rows="3" 
                                      placeholder="请输入详细的删除原因..." required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-warning">
                            <i class="bi bi-trash"></i> 确认删除
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 批量封禁模态框 -->
    <div class="modal fade" id="batchBanModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title"><i class="bi bi-ban"></i> 批量封禁用户</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="batch_ban">
                        <div id="batchBanUserIds"></div>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            确定要批量封禁 <span id="batchBanCount" class="fw-bold">0</span> 个用户吗？
                        </div>
                        <div class="mb-3">
                            <label for="batchBanReason" class="form-label">批量封禁原因 <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="reason" id="batchBanReason" rows="3" 
                                      placeholder="请输入批量封禁原因..." required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-ban"></i> 确认批量封禁
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 批量解封模态框 -->
    <div class="modal fade" id="batchUnbanModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title"><i class="bi bi-check-circle"></i> 批量解封用户</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="batch_unban">
                        <div id="batchUnbanUserIds"></div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            确定要批量解封 <span id="batchUnbanCount" class="fw-bold">0</span> 个用户吗？
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check-circle"></i> 确认批量解封
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 批量删除模态框 -->
    <div class="modal fade" id="batchDeleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-white">
                    <h5 class="modal-title"><i class="bi bi-trash"></i> 批量删除用户</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="batch_delete">
                        <div id="batchDeleteUserIds"></div>
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>危险操作！</strong>确定要批量删除 <span id="batchDeleteCount" class="fw-bold">0</span> 个用户吗？
                        </div>
                        <div class="mb-3">
                            <label for="batchDeleteReason" class="form-label">批量删除原因 <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="reason" id="batchDeleteReason" rows="3" 
                                      placeholder="请输入批量删除原因..." required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-warning">
                            <i class="bi bi-trash"></i> 确认批量删除
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/theme-switcher.js"></script>
    <script>
        // 全局变量
        let selectedUsers = new Set();

        // 单个操作模态框
        function showBanModal(userId, username) {
            document.getElementById('banUserId').value = userId;
            document.getElementById('banUsername').textContent = username;
            new bootstrap.Modal(document.getElementById('banModal')).show();
        }

        function showUnbanModal(userId, username) {
            document.getElementById('unbanUserId').value = userId;
            document.getElementById('unbanUsername').textContent = username;
            new bootstrap.Modal(document.getElementById('unbanModal')).show();
        }

        function showDeleteModal(userId, username) {
            document.getElementById('deleteUserId').value = userId;
            document.getElementById('deleteUsername').textContent = username;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // 选择管理
        function updateSelection() {
            const checkboxes = document.querySelectorAll('.user-checkbox:checked');
            const count = checkboxes.length;

            // 更新选择计数
            document.getElementById('selectedCount').textContent = count;

            // 显示/隐藏批量操作栏
            const batchActions = document.getElementById('batchActions');
            if (count > 0) {
                batchActions.classList.add('show');
            } else {
                batchActions.classList.remove('show');
            }

            // 更新用户卡片选中状态
            document.querySelectorAll('.user-card').forEach(card => {
                const checkbox = card.querySelector('.user-checkbox');
                if (checkbox && checkbox.checked) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            });

            // 更新选择集合
            selectedUsers.clear();
            checkboxes.forEach(cb => selectedUsers.add(cb.value));
        }

        function clearSelection() {
            document.querySelectorAll('.user-checkbox').forEach(cb => cb.checked = false);
            updateSelection();
        }

        // 批量操作
        function showBatchBanModal() {
            const checkboxes = document.querySelectorAll('.user-checkbox:checked');
            if (checkboxes.length === 0) {
                alert('请先选择要封禁的用户');
                return;
            }

            const userIdsContainer = document.getElementById('batchBanUserIds');
            userIdsContainer.innerHTML = '';
            checkboxes.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'user_ids[]';
                input.value = checkbox.value;
                userIdsContainer.appendChild(input);
            });

            document.getElementById('batchBanCount').textContent = checkboxes.length;
            new bootstrap.Modal(document.getElementById('batchBanModal')).show();
        }

        function showBatchUnbanModal() {
            const checkboxes = document.querySelectorAll('.user-checkbox:checked');
            if (checkboxes.length === 0) {
                alert('请先选择要解封的用户');
                return;
            }

            const userIdsContainer = document.getElementById('batchUnbanUserIds');
            userIdsContainer.innerHTML = '';
            checkboxes.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'user_ids[]';
                input.value = checkbox.value;
                userIdsContainer.appendChild(input);
            });

            document.getElementById('batchUnbanCount').textContent = checkboxes.length;
            new bootstrap.Modal(document.getElementById('batchUnbanModal')).show();
        }

        function showBatchDeleteModal() {
            const checkboxes = document.querySelectorAll('.user-checkbox:checked');
            if (checkboxes.length === 0) {
                alert('请先选择要删除的用户');
                return;
            }

            const userIdsContainer = document.getElementById('batchDeleteUserIds');
            userIdsContainer.innerHTML = '';
            checkboxes.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'user_ids[]';
                input.value = checkbox.value;
                userIdsContainer.appendChild(input);
            });

            document.getElementById('batchDeleteCount').textContent = checkboxes.length;
            new bootstrap.Modal(document.getElementById('batchDeleteModal')).show();
        }

        // 导出功能
        function exportUsers() {
            const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
            if (selectedCheckboxes.length === 0) {
                alert('请先选择要导出的用户');
                return;
            }

            const userIds = Array.from(selectedCheckboxes).map(cb => cb.value);
            
            // 创建导出表单
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'export_users.php';
            
            userIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'user_ids[]';
                input.value = id;
                form.appendChild(input);
            });
            
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        // 实时搜索
        let searchTimeout;
        function setupRealTimeSearch() {
            const searchInput = document.getElementById('searchInput');
            
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 2 || this.value.length === 0) {
                        // 可以选择是否启用实时搜索
                        // document.getElementById('searchForm').submit();
                    }
                }, 500);
            });
        }

        // 卡片交互
        function setupCardInteractions() {
            document.querySelectorAll('.user-card').forEach(card => {
                card.addEventListener('click', function(e) {
                    // 如果点击的是复选框、按钮或链接，不触发卡片选择
                    if (e.target.type === 'checkbox' || 
                        e.target.tagName === 'BUTTON' || 
                        e.target.tagName === 'A' ||
                        e.target.closest('button') || 
                        e.target.closest('a')) {
                        return;
                    }

                    // 切换选择状态
                    const checkbox = this.querySelector('.user-checkbox');
                    if (checkbox) {
                        checkbox.checked = !checkbox.checked;
                        updateSelection();
                    }
                });
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupRealTimeSearch();
            setupCardInteractions();
            updateSelection();

            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                // Ctrl + A 全选
                if (e.ctrlKey && e.key === 'a') {
                    e.preventDefault();
                    document.querySelectorAll('.user-checkbox').forEach(cb => cb.checked = true);
                    updateSelection();
                }

                // ESC 取消选择
                if (e.key === 'Escape') {
                    clearSelection();
                }
            });

            // 添加滚动动画
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // 观察用户卡片
            document.querySelectorAll('.user-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        });

        // 表单自动提交（搜索筛选）
        document.querySelectorAll('select[name="date_range"], select[name="permission_type"], select[name="sort"]').forEach(select => {
            select.addEventListener('change', function() {
                document.getElementById('searchForm').submit();
            });
        });
    </script>
</body>
</html>