<?php
/**
 * 待审批用户管理页面
 * 管理系统 - management.djxs.xyz
 */

require_once __DIR__ . '/../../includes/admin.php';

$admin = new Admin();

// 检查登录状态
if (!$admin->checkLogin()) {
    header('Location: ../auth/login.php');
    exit();
}

session_start();
$adminId = $_SESSION['admin_id'];
$message = '';
$messageType = '';

// 处理审批操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $pendingId = intval($_POST['pending_id'] ?? 0);
    $pendingIds = $_POST['pending_ids'] ?? [];
    $note = trim($_POST['note'] ?? '');

    if ($action === 'approve' && $pendingId > 0) {
        $result = $admin->approveUser($pendingId, $adminId, $note);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'reject' && $pendingId > 0) {
        $result = $admin->rejectUser($pendingId, $adminId, $note);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'batch_approve' && !empty($pendingIds)) {
        $result = $admin->batchApproveUsers($pendingIds, $adminId, $note);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'batch_reject' && !empty($pendingIds)) {
        $result = $admin->batchRejectUsers($pendingIds, $adminId, $note);
        $message = $result['message'];
        $messageType = $result['success'] ? 'danger' : 'danger';
    } elseif ($action === 'approve_all') {
        $result = $admin->approveAllUsers($adminId, $note);
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    } elseif ($action === 'reject_all') {
        $result = $admin->rejectAllUsers($adminId, $note);
        $message = $result['message'];
        $messageType = $result['success'] ? 'warning' : 'danger';
    }
}

// 获取待审批用户列表
$page = intval($_GET['page'] ?? 1);
$limit = 20;
$pendingResult = $admin->getPendingUsers($page, $limit);
$pendingUsers = $pendingResult['success'] ? $pendingResult['users'] : [];
$total = $pendingResult['total'] ?? 0;
$totalPages = ceil($total / $limit);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>待审批用户 - <?php echo SYSTEM_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .user-card {
            transition: transform 0.3s;
        }
        .user-card:hover {
            transform: translateY(-2px);
        }
        .btn-approve {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            border: none;
        }
        .btn-reject {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border: none;
        }
        .status-badge {
            font-size: 0.8em;
            padding: 0.4em 0.8em;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <?php
            // 获取统计数据用于侧边栏
            $stats = ['pending_users' => $total];
            include '../includes/sidebar.php';
            ?>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-clock-history"></i>
                        待审批用户
                        <span class="badge bg-primary ms-2"><?php echo $total; ?></span>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-success" onclick="showAllModal('approve')"
                                    <?php echo empty($pendingUsers) ? 'disabled' : ''; ?>>
                                <i class="bi bi-check-circle-fill"></i> 全部通过
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="showAllModal('reject')"
                                    <?php echo empty($pendingUsers) ? 'disabled' : ''; ?>>
                                <i class="bi bi-x-octagon"></i> 全部拒绝
                            </button>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <i class="bi bi-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (empty($pendingUsers)): ?>
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h3 class="mt-3 text-muted">暂无待审批用户</h3>
                            <p class="text-muted">所有用户申请都已处理完毕</p>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- 批量操作控制栏 -->
                    <div class="card mb-3">
                        <div class="card-body py-2">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                        <label class="form-check-label" for="selectAll">
                                            <strong>全选/取消全选</strong>
                                        </label>
                                        <span class="text-muted ms-2" id="selectedCount">已选择 0 个用户</span>
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-success" onclick="batchApprove()" id="batchApproveBtn" disabled>
                                            <i class="bi bi-check-circle"></i> 批量通过
                                        </button>
                                        <button type="button" class="btn btn-danger" onclick="batchReject()" id="batchRejectBtn" disabled>
                                            <i class="bi bi-x-circle"></i> 批量拒绝
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <?php foreach ($pendingUsers as $user): ?>
                            <div class="col-lg-6 mb-4">
                                <div class="card user-card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <input class="form-check-input me-2" type="checkbox"
                                                   value="<?php echo $user['id']; ?>"
                                                   id="user_<?php echo $user['id']; ?>"
                                                   onchange="updateSelection()">
                                            <h5 class="mb-0">
                                                <i class="bi bi-person-circle"></i>
                                                <?php echo htmlspecialchars($user['username']); ?>
                                            </h5>
                                        </div>
                                        <span class="badge bg-warning status-badge">待审批</span>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>邮箱：</strong><?php echo htmlspecialchars($user['email']); ?></p>
                                                <p><strong>手机：</strong><?php echo htmlspecialchars($user['phone'] ?: '未填写'); ?></p>
                                                <p><strong>真实姓名：</strong><?php echo htmlspecialchars($user['real_name'] ?: '未填写'); ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>申请时间：</strong><?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?></p>
                                                <p><strong>IP地址：</strong><?php echo htmlspecialchars($user['ip_address']); ?></p>
                                            </div>
                                        </div>
                                        
                                        <?php if ($user['reason']): ?>
                                            <div class="mt-3">
                                                <strong>申请理由：</strong>
                                                <p class="text-muted mt-1"><?php echo nl2br(htmlspecialchars($user['reason'])); ?></p>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="mt-3">
                                            <button type="button" class="btn btn-approve btn-sm me-2" 
                                                    onclick="showApprovalModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>', 'approve')">
                                                <i class="bi bi-check-circle"></i> 通过
                                            </button>
                                            <button type="button" class="btn btn-reject btn-sm" 
                                                    onclick="showApprovalModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>', 'reject')">
                                                <i class="bi bi-x-circle"></i> 拒绝
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- 分页 -->
                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="分页导航">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>">上一页</a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>">下一页</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <!-- 审批模态框 -->
    <div class="modal fade" id="approvalModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">审批用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" id="modalAction">
                        <input type="hidden" name="pending_id" id="modalPendingId">

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <span id="modalMessage"></span>
                        </div>

                        <div class="mb-3">
                            <label for="note" class="form-label">备注说明（可选）</label>
                            <textarea class="form-control" name="note" id="note" rows="3"
                                      placeholder="请输入审批备注..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn" id="modalSubmitBtn">确认</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 批量操作模态框 -->
    <div class="modal fade" id="batchModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="batchModalTitle">批量操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="" id="batchForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" id="batchAction">
                        <div id="batchPendingIds"></div>

                        <div class="alert" id="batchAlert">
                            <i class="bi bi-info-circle"></i>
                            <span id="batchMessage"></span>
                        </div>

                        <div class="mb-3">
                            <label for="batchNote" class="form-label">批量操作备注（可选）</label>
                            <textarea class="form-control" name="note" id="batchNote" rows="3"
                                      placeholder="请输入批量操作的备注说明..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn" id="batchSubmitBtn">确认操作</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 全部操作模态框 -->
    <div class="modal fade" id="allModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="allModalTitle">全部操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" id="allAction">

                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <span id="allMessage"></span>
                        </div>

                        <div class="mb-3">
                            <label for="allNote" class="form-label">操作备注（可选）</label>
                            <textarea class="form-control" name="note" id="allNote" rows="3"
                                      placeholder="请输入操作备注..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn" id="allSubmitBtn">确认操作</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showApprovalModal(pendingId, username, action) {
            document.getElementById('modalPendingId').value = pendingId;
            document.getElementById('modalAction').value = action;

            const modal = document.getElementById('approvalModal');
            const title = document.getElementById('modalTitle');
            const message = document.getElementById('modalMessage');
            const submitBtn = document.getElementById('modalSubmitBtn');

            if (action === 'approve') {
                title.textContent = '通过用户申请';
                message.textContent = `确定要通过用户 "${username}" 的注册申请吗？`;
                submitBtn.textContent = '通过申请';
                submitBtn.className = 'btn btn-success';
            } else {
                title.textContent = '拒绝用户申请';
                message.textContent = `确定要拒绝用户 "${username}" 的注册申请吗？`;
                submitBtn.textContent = '拒绝申请';
                submitBtn.className = 'btn btn-danger';
            }

            new bootstrap.Modal(modal).show();
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('input[type="checkbox"][id^="user_"]');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateSelection();
        }

        // 更新选择状态
        function updateSelection() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"][id^="user_"]');
            const selectedCheckboxes = document.querySelectorAll('input[type="checkbox"][id^="user_"]:checked');
            const selectAll = document.getElementById('selectAll');
            const selectedCount = document.getElementById('selectedCount');
            const batchApproveBtn = document.getElementById('batchApproveBtn');
            const batchRejectBtn = document.getElementById('batchRejectBtn');

            // 更新全选状态
            if (selectedCheckboxes.length === 0) {
                selectAll.indeterminate = false;
                selectAll.checked = false;
            } else if (selectedCheckboxes.length === checkboxes.length) {
                selectAll.indeterminate = false;
                selectAll.checked = true;
            } else {
                selectAll.indeterminate = true;
                selectAll.checked = false;
            }

            // 更新选择计数
            selectedCount.textContent = `已选择 ${selectedCheckboxes.length} 个用户`;

            // 更新批量操作按钮状态
            const hasSelection = selectedCheckboxes.length > 0;
            batchApproveBtn.disabled = !hasSelection;
            batchRejectBtn.disabled = !hasSelection;
        }

        // 批量通过选中用户
        function batchApprove() {
            const selectedCheckboxes = document.querySelectorAll('input[type="checkbox"][id^="user_"]:checked');
            if (selectedCheckboxes.length === 0) {
                alert('请先选择要操作的用户');
                return;
            }

            showBatchOperationModal('batch_approve', selectedCheckboxes.length);
        }

        // 批量拒绝选中用户
        function batchReject() {
            const selectedCheckboxes = document.querySelectorAll('input[type="checkbox"][id^="user_"]:checked');
            if (selectedCheckboxes.length === 0) {
                alert('请先选择要操作的用户');
                return;
            }

            showBatchOperationModal('batch_reject', selectedCheckboxes.length);
        }

        // 显示批量操作模态框
        function showBatchOperationModal(action, count) {
            const selectedCheckboxes = document.querySelectorAll('input[type="checkbox"][id^="user_"]:checked');
            const pendingIds = Array.from(selectedCheckboxes).map(cb => cb.value);

            document.getElementById('batchAction').value = action;

            // 清空并重新添加隐藏字段
            const container = document.getElementById('batchPendingIds');
            container.innerHTML = '';
            pendingIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'pending_ids[]';
                input.value = id;
                container.appendChild(input);
            });

            const title = document.getElementById('batchModalTitle');
            const message = document.getElementById('batchMessage');
            const alert = document.getElementById('batchAlert');
            const submitBtn = document.getElementById('batchSubmitBtn');

            if (action === 'batch_approve') {
                title.textContent = '批量通过申请';
                message.textContent = `确定要批量通过 ${count} 个用户的注册申请吗？`;
                alert.className = 'alert alert-success';
                submitBtn.textContent = '批量通过';
                submitBtn.className = 'btn btn-success';
            } else {
                title.textContent = '批量拒绝申请';
                message.textContent = `确定要批量拒绝 ${count} 个用户的注册申请吗？`;
                alert.className = 'alert alert-danger';
                submitBtn.textContent = '批量拒绝';
                submitBtn.className = 'btn btn-danger';
            }

            new bootstrap.Modal(document.getElementById('batchModal')).show();
        }



        // 显示全部操作模态框
        function showAllModal(action) {
            const totalUsers = <?php echo $total; ?>;

            document.getElementById('allAction').value = action === 'approve' ? 'approve_all' : 'reject_all';

            const title = document.getElementById('allModalTitle');
            const message = document.getElementById('allMessage');
            const submitBtn = document.getElementById('allSubmitBtn');

            if (action === 'approve') {
                title.textContent = '全部通过申请';
                message.textContent = `确定要通过当前页面所有 ${totalUsers} 个用户的注册申请吗？此操作不可撤销！`;
                submitBtn.textContent = '全部通过';
                submitBtn.className = 'btn btn-success';
            } else {
                title.textContent = '全部拒绝申请';
                message.textContent = `确定要拒绝当前页面所有 ${totalUsers} 个用户的注册申请吗？此操作不可撤销！`;
                submitBtn.textContent = '全部拒绝';
                submitBtn.className = 'btn btn-danger';
            }

            new bootstrap.Modal(document.getElementById('allModal')).show();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateSelection();
        });
    </script>
</body>
</html>
