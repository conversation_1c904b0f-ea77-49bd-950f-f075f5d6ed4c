<?php
/**
 * 管理员登录页面
 * 管理系统 - management.djxs.xyz
 */

require_once __DIR__ . '/../../includes/admin.php';

$admin = new Admin();
$error = '';
$success = '';

// 如果已经登录，跳转到仪表板
if ($admin->checkLogin()) {
    header('Location: ../dashboard/dashboard.php');
    exit();
}

// 处理登录请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = '用户名和密码不能为空';
    } else {
        $result = $admin->login($username, $password);
        if ($result['success']) {
            header('Location: ../dashboard/dashboard.php');
            exit();
        } else {
            $error = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - <?php echo SYSTEM_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <link href="../assets/css/admin-theme.css" rel="stylesheet">
    <style>
        body {
            background: var(--primary-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* 登录页面主题适配 */
        [data-theme="dark"] body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        }

        [data-theme="dark"] .login-card {
            background: rgba(40, 40, 40, 0.95);
            color: #e0e0e0;
        }

        [data-theme="dark"] .form-control {
            background: rgba(60, 60, 60, 0.9);
            border-color: #555;
            color: #e0e0e0;
        }

        [data-theme="dark"] .form-control:focus {
            background: rgba(70, 70, 70, 0.9);
            border-color: #667eea;
            color: #e0e0e0;
        }

        [data-theme="dark"] .input-group-text {
            background: rgba(60, 60, 60, 0.9);
            border-color: #555;
            color: #e0e0e0;
        }

        [data-theme="dark"] .input-group:focus-within .input-group-text {
            background: rgba(70, 70, 70, 0.9);
            border-color: #667eea;
        }

        [data-theme="dark"] .text-muted {
            color: #aaa !important;
        }

        /* 动态背景粒子 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float-particle 8s linear infinite;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: var(--border-radius-xl);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            animation: slideInUp 0.8s ease-out;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 3s ease-in-out infinite;
        }

        .login-header {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 2rem;
            animation: fadeInDown 1s ease-out 0.3s both;
        }

        .login-header i {
            animation: pulse 2s ease-in-out infinite;
        }

        .form-group {
            animation: fadeInUp 0.6s ease-out both;
        }

        .form-group:nth-child(1) { animation-delay: 0.4s; }
        .form-group:nth-child(2) { animation-delay: 0.5s; }
        .form-group:nth-child(3) { animation-delay: 0.6s; }

        .form-control {
            border-radius: var(--border-radius-sm);
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            transition: var(--transition-normal);
            background: rgba(255,255,255,0.9);
            position: relative;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
            transform: scale(1.02);
        }

        .btn-login {
            background: var(--primary-gradient);
            border: none;
            border-radius: var(--border-radius-sm);
            padding: 15px;
            font-weight: var(--font-weight-semibold);
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: var(--transition-normal);
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .input-group-text {
            background: rgba(255,255,255,0.9);
            border: 2px solid #e9ecef;
            border-right: none;
            border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
            transition: var(--transition-normal);
        }

        .input-group .form-control {
            border-left: none;
            border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
        }

        .input-group:focus-within .input-group-text {
            border-color: #667eea;
            background: white;
        }

        .alert {
            border-radius: var(--border-radius-sm);
            border: none;
            animation: slideInDown 0.5s ease-out;
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 50%;
            animation: float-shape 8s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 100px;
            height: 100px;
            top: 15%;
            left: 8%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 150px;
            height: 150px;
            top: 55%;
            right: 8%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 15%;
            left: 15%;
            animation-delay: 4s;
        }

        .shape:nth-child(4) {
            width: 120px;
            height: 120px;
            top: 30%;
            right: 25%;
            animation-delay: 1s;
        }

        .shape:nth-child(5) {
            width: 60px;
            height: 60px;
            bottom: 40%;
            right: 15%;
            animation-delay: 3s;
        }

        @keyframes float-shape {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-30px) rotate(180deg);
                opacity: 0.8;
            }
        }

        @keyframes float-particle {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* 登录页面响应式优化 */
        @media (max-width: 576px) {
            body {
                padding: var(--spacing-sm);
            }

            .login-card {
                margin: var(--spacing-sm);
                border-radius: var(--border-radius-md);
            }

            .login-card .card-body {
                padding: var(--spacing-lg);
            }

            .login-header {
                margin-bottom: 1.5rem;
            }

            .login-header h2 {
                font-size: 1.5rem;
            }

            .login-header i {
                font-size: 2rem;
            }

            .form-control {
                padding: 12px 15px;
                font-size: 0.9rem;
            }

            .btn-login {
                padding: 12px;
                font-size: 0.9rem;
            }

            .input-group-text {
                padding: 12px 15px;
            }

            .floating-shapes .shape:nth-child(1) {
                width: 60px;
                height: 60px;
                top: 10%;
                left: 5%;
            }

            .floating-shapes .shape:nth-child(2) {
                width: 80px;
                height: 80px;
                top: 70%;
                right: 5%;
            }

            .floating-shapes .shape:nth-child(3) {
                width: 40px;
                height: 40px;
                bottom: 10%;
                left: 10%;
            }

            .floating-shapes .shape:nth-child(4) {
                width: 70px;
                height: 70px;
                top: 40%;
                right: 15%;
            }

            .floating-shapes .shape:nth-child(5) {
                width: 35px;
                height: 35px;
                bottom: 30%;
                right: 10%;
            }

            .alert {
                font-size: 0.9rem;
                padding: 0.75rem;
            }
        }

        @media (max-width: 768px) {
            .container .row .col-md-6 {
                padding: 0 var(--spacing-sm);
            }

            .login-card {
                margin: var(--spacing-md) 0;
            }
        }

        /* 横屏模式优化 */
        @media (orientation: landscape) and (max-height: 600px) {
            .login-card {
                margin: var(--spacing-sm) auto;
                max-width: 500px;
            }

            .login-header {
                margin-bottom: 1rem;
            }

            .login-header h2 {
                font-size: 1.3rem;
            }

            .login-header i {
                font-size: 1.8rem;
            }

            .form-group {
                margin-bottom: 0.75rem;
            }

            .floating-shapes {
                display: none;
            }
        }

        /* 高分辨率屏幕优化 */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .login-card {
                box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
            }

            .btn-login:hover {
                box-shadow: 0 15px 30px rgba(102, 126, 234, 0.5);
            }
        }
    </style>
</head>
<body>
    <!-- 动态背景粒子 -->
    <div class="particles" id="particles"></div>

    <!-- 浮动形状 -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card login-card">
                    <div class="card-body p-5">
                        <div class="login-header">
                            <i class="bi bi-shield-lock display-4"></i>
                            <h2 class="mt-3 mb-0">管理后台</h2>
                            <p class="text-muted"><?php echo DOMAIN; ?></p>
                        </div>

                        <?php if ($error): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="bi bi-exclamation-triangle"></i>
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="bi bi-check-circle"></i>
                                <?php echo htmlspecialchars($success); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="" id="loginForm">
                            <div class="mb-3 form-group">
                                <label for="username" class="form-label">用户名</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-person"></i>
                                    </span>
                                    <input type="text" class="form-control" id="username" name="username"
                                           placeholder="请输入管理员用户名" required
                                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>">
                                </div>
                            </div>

                            <div class="mb-4 form-group">
                                <label for="password" class="form-label">密码</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password"
                                           placeholder="请输入密码" required>
                                </div>
                            </div>

                            <div class="d-grid form-group">
                                <button type="submit" class="btn btn-primary btn-login" id="loginBtn">
                                    <i class="bi bi-box-arrow-in-right"></i>
                                    <span class="btn-text">登录管理后台</span>
                                    <span class="btn-loading d-none">
                                        <i class="bi bi-arrow-clockwise spin"></i>
                                        登录中...
                                    </span>
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="bi bi-info-circle"></i>
                                默认账户: admin / admin123
                            </small>
                        </div>

                        <div class="text-center mt-3">
                            <small class="text-muted">
                                © 2024 <?php echo SYSTEM_NAME; ?> - 版本 <?php echo SYSTEM_VERSION; ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/theme-switcher.js"></script>
    <script>
        // 创建动态粒子背景
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 15;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.width = particle.style.height = (Math.random() * 4 + 2) + 'px';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 5) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 表单提交动画
        function handleFormSubmit() {
            const form = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            const btnText = loginBtn.querySelector('.btn-text');
            const btnLoading = loginBtn.querySelector('.btn-loading');

            form.addEventListener('submit', function(e) {
                // 显示加载状态
                btnText.classList.add('d-none');
                btnLoading.classList.remove('d-none');
                loginBtn.disabled = true;

                // 添加提交动画
                loginBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    loginBtn.style.transform = 'scale(1)';
                }, 150);
            });
        }

        // 输入框焦点动画
        function setupInputAnimations() {
            const inputs = document.querySelectorAll('.form-control');

            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                    this.parentElement.style.transition = 'transform 0.3s ease';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });

                // 输入时的动画效果
                input.addEventListener('input', function() {
                    if (this.value.length > 0) {
                        this.style.borderColor = '#28a745';
                        this.style.boxShadow = '0 0 0 0.2rem rgba(40, 167, 69, 0.25)';
                    } else {
                        this.style.borderColor = '#e9ecef';
                        this.style.boxShadow = 'none';
                    }
                });
            });
        }

        // 卡片悬停效果
        function setupCardHover() {
            const loginCard = document.querySelector('.login-card');

            loginCard.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
                this.style.boxShadow = '0 30px 60px rgba(0, 0, 0, 0.2)';
            });

            loginCard.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.15)';
            });
        }

        // 键盘快捷键
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                // Ctrl + Enter 快速提交
                if (e.ctrlKey && e.key === 'Enter') {
                    document.getElementById('loginForm').submit();
                }

                // ESC 清空表单
                if (e.key === 'Escape') {
                    document.getElementById('username').value = '';
                    document.getElementById('password').value = '';
                    document.getElementById('username').focus();
                }
            });
        }

        // 添加旋转动画样式
        const style = document.createElement('style');
        style.textContent = `
            .spin {
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            .form-control:valid {
                border-color: #28a745 !important;
                box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            handleFormSubmit();
            setupInputAnimations();
            setupCardHover();
            setupKeyboardShortcuts();

            // 自动聚焦到用户名输入框
            setTimeout(() => {
                document.getElementById('username').focus();
            }, 500);

            // 添加页面加载动画
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';
            }, 100);
        });

        // 错误消息动画
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            alert.style.animation = 'slideInDown 0.5s ease-out';

            // 自动隐藏成功消息
            if (alert.classList.contains('alert-success')) {
                setTimeout(() => {
                    alert.style.animation = 'slideOutUp 0.5s ease-out';
                    setTimeout(() => alert.remove(), 500);
                }, 3000);
            }
        });
    </script>
</body>
</html>
