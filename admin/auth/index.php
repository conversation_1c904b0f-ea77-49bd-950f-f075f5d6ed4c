<?php
/**
 * 管理员后台认证入口 💖
 * 重定向到仪表板
 */

// 重定向到仪表板
header('Location: ../dashboard/dashboard.php');
exit;

require_once __DIR__ . '/../includes/admin.php';
require_once __DIR__ . '/../includes/module_permission_manager.php';

$admin = new Admin();
$permissionManager = new ModulePermissionManager($admin->getConnection());

// 检查登录状态
if (!$admin->checkLogin()) {
    header('Location: login.php');
    exit();
}

// 获取统计数据
$statsResult = $admin->getStats();
$stats = $statsResult['success'] ? $statsResult['stats'] : [];

// 获取模块权限统计数据
try {
    $availableModules = $permissionManager->getAvailableModules();

    // 统计应用权限分配情况
    $sql = "SELECT COUNT(*) FROM user_app_permissions WHERE is_active = 1";
    $stmt = $admin->getConnection()->prepare($sql);
    $stmt->execute();
    $activeAppPermissions = $stmt->fetchColumn();

    // 统计模块权限分配情况
    $sql = "SELECT COUNT(*) FROM user_module_permissions WHERE is_active = 1";
    $stmt = $admin->getConnection()->prepare($sql);
    $stmt->execute();
    $activeModulePermissions = $stmt->fetchColumn();

    // 统计Android下载总次数
    $sql = "SELECT SUM(download_count) FROM user_app_permissions WHERE app_type = 'android' AND is_active = 1";
    $stmt = $admin->getConnection()->prepare($sql);
    $stmt->execute();
    $totalDownloads = $stmt->fetchColumn() ?: 0;

    $permissionStats = [
        'total_modules' => count($availableModules),
        'app_permissions' => $activeAppPermissions,
        'module_permissions' => $activeModulePermissions,
        'total_downloads' => $totalDownloads
    ];
} catch (Exception $e) {
    $permissionStats = [
        'total_modules' => 8,
        'app_permissions' => 0,
        'module_permissions' => 0,
        'total_downloads' => 0
    ];
}

session_start();
$adminUsername = $_SESSION['admin_username'] ?? 'Admin';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - <?php echo SYSTEM_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stat-card-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        .stat-card-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-card-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .navbar-brand {
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="bi bi-shield-check"></i>
                            管理后台
                        </h4>
                        <small class="text-white-50">欢迎，<?php echo htmlspecialchars($adminUsername); ?></small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <!-- 主要功能 -->
                        <li class="nav-item">
                            <a class="nav-link active" href="index.php">
                                <i class="bi bi-speedometer2"></i> 仪表盘
                            </a>
                        </li>

                        <!-- 用户管理分组 -->
                        <li class="nav-item mt-3">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>用户管理</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="pending.php">
                                <i class="bi bi-person-plus"></i> 待审批用户
                                <?php if (isset($stats['pending_users']) && $stats['pending_users'] > 0): ?>
                                    <span class="badge bg-danger ms-2"><?php echo $stats['pending_users']; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="advanced_permissions.php">
                                <i class="bi bi-stars"></i> 智能权限管理 💖
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="bi bi-person-gear"></i> 用户状态管理
                            </a>
                        </li>



                        <!-- 系统管理分组 -->
                        <li class="nav-item mt-3">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>系统管理</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="logs.php">
                                <i class="bi bi-journal-text"></i> 操作日志
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="security.php">
                                <i class="bi bi-shield-check"></i> 安全监控
                            </a>
                        </li>

                        <li class="nav-item mt-4">
                            <a class="nav-link text-warning" href="logout.php">
                                <i class="bi bi-box-arrow-right"></i> 退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-speedometer2"></i>
                        仪表盘
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">总用户数</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $stats['total_users'] ?? 0; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-people-fill fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card-success">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">活跃用户</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $stats['active_users'] ?? 0; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-person-check-fill fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card-warning">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">待审批</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $stats['pending_users'] ?? 0; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-clock-fill fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card-info">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">在线用户</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $stats['online_users'] ?? 0; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-wifi fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第二行统计 -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: white;">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">今日登录</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $stats['today_logins'] ?? 0; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-graph-up fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333;">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">本周注册</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $stats['week_registrations'] ?? 0; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-calendar-week fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333;">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">今日注册</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $stats['today_registrations'] ?? 0; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-person-plus fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card" style="background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); color: #333;">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">封禁用户</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $stats['banned_users'] ?? 0; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-ban fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 模块权限统计 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="mb-3">
                            <i class="bi bi-gear"></i>
                            模块权限统计
                        </h5>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">可用模块</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $permissionStats['total_modules']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-puzzle-fill fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white;">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">应用权限</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $permissionStats['app_permissions']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-app-indicator fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">模块权限</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $permissionStats['module_permissions']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-gear-fill fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">下载次数</div>
                                        <div class="h5 mb-0 font-weight-bold"><?php echo $permissionStats['total_downloads']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-download fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-lightning-charge"></i>
                                    快速操作
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <a href="pending.php" class="btn btn-primary btn-lg w-100">
                                            <i class="bi bi-person-plus"></i>
                                            审批新用户
                                            <?php if (isset($stats['pending_users']) && $stats['pending_users'] > 0): ?>
                                                <span class="badge bg-light text-primary ms-2"><?php echo $stats['pending_users']; ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="advanced_permissions.php" class="btn btn-success btn-lg w-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;">
                                            <i class="bi bi-stars"></i>
                                            智能权限管理 💖
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="users.php" class="btn btn-warning btn-lg w-100">
                                            <i class="bi bi-person-gear"></i>
                                            用户状态管理
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="logs.php" class="btn btn-info btn-lg w-100">
                                            <i class="bi bi-journal-text"></i>
                                            操作日志
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-info-circle"></i>
                                    系统信息
                                </h5>
                            </div>
                            <div class="card-body">
                                <p><strong>系统版本：</strong><?php echo SYSTEM_VERSION; ?></p>
                                <p><strong>域名：</strong><?php echo DOMAIN; ?></p>
                                <p><strong>PHP版本：</strong><?php echo PHP_VERSION; ?></p>
                                <p><strong>当前时间：</strong><?php echo date('Y-m-d H:i:s'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 自动刷新统计数据
        setInterval(function() {
            location.reload();
        }, 300000); // 5分钟刷新一次
    </script>
</body>
</html>
