<?php
/**
 * 模块权限API
 * 简化的权限检查接口
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../api/config/database.php';
require_once __DIR__ . '/../includes/module_permission_manager.php';
require_once __DIR__ . '/../includes/jwt_helper.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    $permissionManager = new ModulePermissionManager($conn);
    $jwtHelper = new JWTHelper();

    // 调试：记录所有请求信息
    error_log("=== 权限API调试 ===");
    error_log("请求方法: " . $_SERVER['REQUEST_METHOD']);
    error_log("所有HTTP头: " . json_encode(getallheaders()));
    error_log("_SERVER中的HTTP头: " . json_encode(array_filter($_SERVER, function($key) {
        return strpos($key, 'HTTP_') === 0;
    }, ARRAY_FILTER_USE_KEY)));

    // 获取用户ID
    $userId = null;
    
    if (isset($_GET['user_id'])) {
        // 通过URL参数获取用户ID（用于管理员查询）
        $userId = (int)$_GET['user_id'];
    } else {
        // 通过Token获取当前用户ID
        $authHeader = '';

        // 更健壮的头部获取方法
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
            // 尝试多种可能的键名
            $authKeys = ['Authorization', 'authorization', 'AUTHORIZATION'];
            foreach ($authKeys as $key) {
                if (isset($headers[$key])) {
                    $authHeader = $headers[$key];
                    break;
                }
            }
        }

        // 如果getallheaders()失败，直接从$_SERVER获取
        if (!$authHeader) {
            $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        }

        if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $token = $matches[1];
            error_log("提取的Token: " . substr($token, 0, 50) . "...");

            $payload = $jwtHelper->validateToken($token);
            error_log("JWT验证结果: " . ($payload ? "成功" : "失败"));

            if ($payload) {
                error_log("JWT Payload: " . json_encode($payload));
                if (isset($payload['user_id'])) {
                    $userId = $payload['user_id'];
                    error_log("用户ID: " . $userId);
                } else {
                    error_log("JWT中缺少user_id字段");
                }
            } else {
                error_log("JWT验证失败");
            }
        } else {
            error_log("Authorization头格式错误或为空: " . $authHeader);
        }
    }
    
    if (!$userId) {
        error_log("最终用户ID为空，身份验证失败");
        throw new Exception('用户身份验证失败');
    }

    error_log("身份验证成功，用户ID: " . $userId);

    // 处理POST请求（下载扣减等操作）
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';

        if ($action === 'consume_download') {
            // 扣减Android下载次数
            $conn->beginTransaction();

            try {
                // 获取当前下载次数
                $sql = "SELECT download_count, is_active, expires_at
                        FROM user_app_permissions
                        WHERE user_id = ? AND app_type = 'android' AND is_active = 1";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$userId]);
                $permission = $stmt->fetch();

                if (!$permission) {
                    throw new Exception('用户没有Android应用权限');
                }

                // 检查权限是否过期
                if ($permission['expires_at'] && strtotime($permission['expires_at']) <= time()) {
                    throw new Exception('Android权限已过期');
                }

                // 检查下载次数
                if ($permission['download_count'] <= 0) {
                    throw new Exception('下载次数已用完');
                }

                // 扣减下载次数
                $newCount = $permission['download_count'] - 1;
                $updateSql = "UPDATE user_app_permissions
                             SET download_count = ?, updated_at = NOW()
                             WHERE user_id = ? AND app_type = 'android'";
                $updateStmt = $conn->prepare($updateSql);
                $updateStmt->execute([$newCount, $userId]);

                // 记录下载日志
                $logSql = "INSERT INTO download_logs (user_id, app_type, action, remaining_count, ip_address, user_agent, created_at)
                          VALUES (?, 'android', 'download', ?, ?, ?, NOW())";
                $logStmt = $conn->prepare($logSql);
                $logStmt->execute([
                    $userId,
                    $newCount,
                    $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                    $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
                ]);

                $conn->commit();

                $response = [
                    'success' => true,
                    'message' => '下载成功，剩余次数：' . $newCount,
                    'data' => [
                        'remaining_count' => $newCount,
                        'can_download_again' => $newCount > 0
                    ]
                ];

                echo json_encode($response, JSON_UNESCAPED_UNICODE);
                exit();

            } catch (Exception $e) {
                $conn->rollBack();
                throw $e;
            }
        }
    }

    // 获取用户权限（GET请求）
    $permissions = $permissionManager->getUserPermissions($userId);
    
    // 构建能力映射
    $capabilities = [
        'android' => [
            'can_access' => in_array('android', $permissions['apps']),
            'download_count' => $permissions['android_downloads'],
            'can_download' => $permissions['android_downloads'] > 0,
            'expires_at' => $permissions['app_details']['android']['expires_at'] ?? null,
            'granted_at' => $permissions['app_details']['android']['granted_at'] ?? null
        ],
        'windows' => [
            'can_access' => in_array('windows', $permissions['apps']),
            'expires_at' => $permissions['app_details']['windows']['expires_at'] ?? null,
            'granted_at' => $permissions['app_details']['windows']['granted_at'] ?? null,
            'modules' => []
        ]
    ];
    
    // 添加Windows模块能力
    $availableModules = $permissionManager->getAvailableModules();
    foreach ($availableModules as $moduleCode => $moduleName) {
        $capabilities['windows']['modules'][$moduleCode] = [
            'name' => $moduleName,
            'enabled' => in_array($moduleCode, $permissions['modules'])
        ];
    }
    
    // 构建权限代码列表（兼容旧系统）
    $permissionCodes = [];
    
    // 添加应用权限
    if (in_array('android', $permissions['apps'])) {
        $permissionCodes[] = 'android_access';
        if ($permissions['android_downloads'] > 0) {
            $permissionCodes[] = 'android_download';
        }
    }
    
    if (in_array('windows', $permissions['apps'])) {
        $permissionCodes[] = 'windows_access';
        
        // 添加模块权限
        foreach ($permissions['modules'] as $moduleCode) {
            $permissionCodes[] = 'windows_' . $moduleCode;
        }
    }
    
    // 返回权限信息
    $response = [
        'success' => true,
        'data' => [
            'user_id' => $userId,
            'permissions' => $permissionCodes,
            'capabilities' => $capabilities,
            'apps' => $permissions['apps'],
            'modules' => $permissions['modules'],
            'android_downloads' => $permissions['android_downloads'],
            'app_details' => $permissions['app_details'], // 添加详细权限信息
            'stats' => [
                'app_count' => count($permissions['apps']),
                'module_count' => count($permissions['modules']),
                'android_downloads' => $permissions['android_downloads']
            ]
        ]
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
