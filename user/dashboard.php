<?php
/**
 * 用户仪表板 💖
 * 用户登录后的主页面
 */

session_start();

// 检查用户是否登录
if (!isset($_SESSION['user_token']) || empty($_SESSION['user_token'])) {
    header('Location: login.php');
    exit();
}

require_once __DIR__ . '/../api/config/config.php';
require_once __DIR__ . '/../includes/jwt_helper.php';
require_once __DIR__ . '/../includes/module_permission_manager.php';

$jwtHelper = new JWTHelper();
$token = $_SESSION['user_token'];

// 验证token
$payload = $jwtHelper->validateToken($token);
if (!$payload) {
    // Token无效，清除session并重定向到登录页
    session_destroy();
    header('Location: login.php?error=token_invalid');
    exit();
}

$userId = $payload['user_id'];
$username = $payload['username'];
$email = $payload['email'];

// 获取用户权限信息
require_once __DIR__ . '/../api/config/database.php';
$database = new Database();
$conn = $database->getConnection();
$permissionManager = new ModulePermissionManager($conn);

$permissions = $permissionManager->getUserPermissions($userId);

// 获取用户详细信息
$sql = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$userId]);
$userInfo = $stmt->fetch();

if (!$userInfo) {
    // 用户不存在，清除session
    session_destroy();
    header('Location: login.php?error=user_not_found');
    exit();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户中心 💖 - <?php echo SYSTEM_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .stats-card {
            background: var(--primary-gradient);
            color: white;
            text-align: center;
            padding: 2rem;
            border-radius: 20px;
        }
        
        .stats-card.success { background: var(--success-gradient); }
        .stats-card.warning { background: var(--warning-gradient); }
        .stats-card.danger { background: var(--danger-gradient); }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 2rem;
            margin: 0 auto;
        }
        
        .permission-badge {
            margin: 2px;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            color: white;
            border-radius: 50px;
            padding: 0.75rem 2rem;
            transition: all 0.3s ease;
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            color: white;
        }
        
        .navbar {
            background: var(--primary-gradient) !important;
            backdrop-filter: blur(10px);
        }
        
        .navbar-brand {
            font-weight: bold;
            color: white !important;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.9) !important;
        }
        
        .nav-link:hover {
            color: white !important;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-person-circle"></i>
                用户中心
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="bi bi-speedometer2"></i> 仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="permissions.php">
                            <i class="bi bi-key"></i> 我的权限
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="bi bi-person-gear"></i> 个人设置
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            <?php echo htmlspecialchars($username); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="bi bi-person-gear"></i> 个人设置
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="logout.php">
                                <i class="bi bi-box-arrow-right"></i> 退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 欢迎区域 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body p-4">
                        <div class="row align-items-center">
                            <div class="col-md-2 text-center">
                                <div class="user-avatar">
                                    <?php echo strtoupper(substr($username, 0, 1)); ?>
                                </div>
                            </div>
                            <div class="col-md-10">
                                <h2 class="mb-2 animate__animated animate__fadeInDown">
                                    欢迎回来，<?php echo htmlspecialchars($userInfo['real_name'] ?: $username); ?>！💖
                                </h2>
                                <p class="text-muted mb-2">
                                    <i class="bi bi-envelope"></i> <?php echo htmlspecialchars($email); ?>
                                </p>
                                <p class="text-muted mb-0">
                                    <i class="bi bi-calendar"></i> 上次登录：<?php echo $userInfo['last_login'] ? date('Y-m-d H:i', strtotime($userInfo['last_login'])) : '首次登录'; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 权限统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-4 mb-3">
                <div class="card stats-card animate__animated animate__fadeInUp">
                    <h3><?php echo count($permissions['apps']); ?></h3>
                    <p class="mb-0"><i class="bi bi-app"></i> 应用权限</p>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card stats-card success animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                    <h3><?php echo count($permissions['modules']); ?></h3>
                    <p class="mb-0"><i class="bi bi-puzzle"></i> 模块权限</p>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card stats-card warning animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                    <h3><?php echo $permissions['android_downloads']; ?></h3>
                    <p class="mb-0"><i class="bi bi-download"></i> Android下载次数</p>
                </div>
            </div>
        </div>

        <!-- 权限详情 -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-app text-primary"></i>
                            应用权限
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($permissions['apps'])): ?>
                            <div class="text-center text-muted py-3">
                                <i class="bi bi-inbox display-4"></i>
                                <p class="mt-2">暂无应用权限</p>
                            </div>
                        <?php else: ?>
                            <div class="d-flex flex-wrap">
                                <?php foreach ($permissions['apps'] as $app): ?>
                                    <span class="badge bg-primary permission-badge">
                                        <?php if ($app === 'android'): ?>
                                            📱 Android应用
                                        <?php elseif ($app === 'windows'): ?>
                                            💻 Windows应用
                                        <?php else: ?>
                                            <?php echo htmlspecialchars($app); ?>
                                        <?php endif; ?>
                                    </span>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-puzzle text-success"></i>
                            模块权限
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($permissions['modules'])): ?>
                            <div class="text-center text-muted py-3">
                                <i class="bi bi-inbox display-4"></i>
                                <p class="mt-2">暂无模块权限</p>
                            </div>
                        <?php else: ?>
                            <div class="d-flex flex-wrap">
                                <?php 
                                $availableModules = $permissionManager->getAvailableModules();
                                foreach ($permissions['modules'] as $moduleCode): 
                                    $moduleName = $availableModules[$moduleCode] ?? $moduleCode;
                                ?>
                                    <span class="badge bg-success permission-badge">
                                        🧩 <?php echo htmlspecialchars($moduleName); ?>
                                    </span>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- 账户状态 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle text-info"></i>
                            账户信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>用户名：</strong><?php echo htmlspecialchars($userInfo['username']); ?></p>
                                <p><strong>邮箱：</strong><?php echo htmlspecialchars($userInfo['email']); ?></p>
                                <p><strong>真实姓名：</strong><?php echo htmlspecialchars($userInfo['real_name'] ?: '未设置'); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>注册时间：</strong><?php echo date('Y-m-d H:i', strtotime($userInfo['created_at'])); ?></p>
                                <p><strong>登录次数：</strong><?php echo $userInfo['login_count']; ?> 次</p>
                                <p><strong>账户状态：</strong>
                                    <span class="badge bg-success">✅ 正常</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
