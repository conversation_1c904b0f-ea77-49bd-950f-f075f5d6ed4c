<?php
/**
 * 定时任务：处理过期权限
 * 建议每5分钟执行一次
 * 
 * Crontab 配置示例:
 */
// */5 * * * * /usr/bin/php /path/to/your/project/cron/process_expired_permissions.php

// 设置脚本执行时间限制
set_time_limit(300); // 5分钟

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入必要的文件
require_once __DIR__ . '/../api/config/database.php';
require_once __DIR__ . '/../includes/module_permission_manager.php';

// 日志文件路径
$logFile = __DIR__ . '/../logs/permission_cron.log';

/**
 * 写入日志
 */
function writeLog($message, $level = 'INFO') {
    global $logFile;
    
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    
    // 确保日志目录存在
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    
    // 同时输出到控制台（如果是命令行执行）
    if (php_sapi_name() === 'cli') {
        echo $logMessage;
    }
}

/**
 * 发送通知邮件（可选）
 */
function sendNotificationEmail($subject, $message) {
    // 这里可以实现邮件发送逻辑
    // 例如使用 PHPMailer 或其他邮件库
    writeLog("邮件通知: {$subject} - {$message}", 'NOTICE');
}

try {
    writeLog("开始执行权限过期处理任务 💖");

    // 创建数据库连接和权限管理器实例
    $database = new Database();
    $conn = $database->getConnection();
    $permissionManager = new ModulePermissionManager($conn);

    // 处理过期权限
    $result = $permissionManager->processExpiredPermissions();

    if (!$result['success']) {
        throw new Exception("处理过期权限失败: " . $result['message']);
    }

    $expiredCount = $result['expired_count'];

    writeLog("权限过期处理完成 - 过期权限数量: {$expiredCount}");
    
    // 如果有过期的权限，记录详细信息
    if ($expiredCount > 0) {
        writeLog("发现过期权限，正在记录详细信息...");

        // 获取今天过期的用户应用权限详情
        $sql = "SELECT uap.*, u.username, u.email
                FROM user_app_permissions uap
                JOIN users u ON uap.user_id = u.id
                WHERE uap.is_active = 0
                AND uap.expires_at IS NOT NULL
                AND DATE(uap.expires_at) = CURDATE()";

        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $expiredPermissionDetails = $stmt->fetchAll();

        foreach ($expiredPermissionDetails as $detail) {
            writeLog("用户 {$detail['username']} ({$detail['email']}) 的 {$detail['app_type']} 权限已过期", 'NOTICE');
        }

        // 发送通知邮件（如果配置了）
        if ($expiredCount > 0) {
            $subject = "权限过期通知 - " . date('Y-m-d H:i:s');
            $message = "今日权限过期统计：\n";
            $message .= "- 过期权限数量: {$expiredCount}\n";
            $message .= "\n详细信息请查看管理后台。";

            sendNotificationEmail($subject, $message);
        }

        // 检查即将过期的权限
        $expiringPermissions = $permissionManager->getExpiringPermissions(3); // 3天内过期
        if (!empty($expiringPermissions)) {
            writeLog("发现 " . count($expiringPermissions) . " 个权限即将在3天内过期", 'WARNING');

            foreach ($expiringPermissions as $permission) {
                $daysLeft = ceil((strtotime($permission['expires_at']) - time()) / 86400);
                writeLog("- 用户: {$permission['username']} ({$permission['email']}) - {$permission['app_type']} - 剩余 {$daysLeft} 天", 'WARNING');
            }
        }
    }
    
    // 清理过期的会话令牌（可选）
    writeLog("开始清理过期会话令牌");

    $sql = "DELETE FROM user_sessions WHERE expires_at < NOW()";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $expiredSessions = $stmt->rowCount();

    writeLog("清理过期会话令牌完成 - 清理数量: {$expiredSessions}");

    // 清理过期的权限操作日志（保留90天）
    writeLog("开始清理过期权限操作日志");

    $sql = "DELETE FROM permission_operation_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $expiredLogs = $stmt->rowCount();

    writeLog("清理过期权限操作日志完成 - 清理数量: {$expiredLogs}");

    // 生成统计报告
    $totalProcessed = $expiredCount + $expiredSessions + $expiredLogs;

    writeLog("权限过期处理任务执行完成 - 总处理数量: {$totalProcessed}");

    // 记录最后执行时间到权限操作日志
    $sql = "INSERT INTO permission_operation_logs (user_id, operation_type, notes, created_at)
            VALUES (0, 'system_cleanup', ?, NOW())";
    $stmt = $conn->prepare($sql);
    $now = date('Y-m-d H:i:s');
    $stmt->execute(["定时任务执行完成 - 处理数量: {$totalProcessed} - 执行时间: {$now}"]);

    // 检查即将过期的权限（7天内）
    writeLog("检查即将过期的权限");

    $expiringPermissions7Days = $permissionManager->getExpiringPermissions(7);
    $expiringCount = count($expiringPermissions7Days);

    if ($expiringCount > 0) {
        writeLog("发现 {$expiringCount} 个权限将在7天内过期", 'WARNING');

        foreach ($expiringPermissions7Days as $permission) {
            $daysLeft = ceil((strtotime($permission['expires_at']) - time()) / 86400);
            $expiryDate = date('Y-m-d H:i', strtotime($permission['expires_at']));
            writeLog("用户 {$permission['username']} ({$permission['email']}) 的 {$permission['app_type']} 权限将于 {$expiryDate} 过期 (剩余 {$daysLeft} 天)", 'WARNING');
        }

        // 发送即将过期通知
        $subject = "权限即将过期提醒 - " . date('Y-m-d H:i:s');
        $message = "发现 {$expiringCount} 个权限将在7天内过期，请及时处理。\n\n";
        $message .= "详细信息请查看管理后台高级权限管理页面。";

        sendNotificationEmail($subject, $message);
    }
    
    writeLog("权限过期处理任务全部完成");
    
} catch (Exception $e) {
    $errorMessage = "权限过期处理任务执行失败: " . $e->getMessage();
    writeLog($errorMessage, 'ERROR');
    
    // 发送错误通知邮件
    sendNotificationEmail("权限处理任务执行失败", $errorMessage);
    
    // 退出时返回错误代码
    exit(1);
}

// 成功退出
exit(0);
?>
